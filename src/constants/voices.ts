export const googleTtsVoices = [
  { id: '<PERSON><PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON><PERSON> (<PERSON>)' }, 
  { id: '<PERSON><PERSON>', name: '<PERSON><PERSON> (Upbeat)' }, 
  { id: '<PERSON><PERSON>', name: '<PERSON><PERSON> (Informative)' },
  { id: '<PERSON><PERSON>', name: '<PERSON><PERSON> (<PERSON>rm)' }, 
  { id: '<PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON> (Excitable)' }, 
  { id: '<PERSON><PERSON>', name: '<PERSON><PERSON> (<PERSON>ful)' },
  { id: '<PERSON><PERSON>', name: '<PERSON><PERSON> (Firm)' }, 
  { id: '<PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON> (<PERSON><PERSON>)' }, 
  { id: '<PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON> (Easy-going)' },
  { id: '<PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON> (<PERSON>)' }, 
  { id: '<PERSON><PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)' }, 
  { id: 'I<PERSON><PERSON>', name: '<PERSON><PERSON><PERSON> (Clear)' },
  { id: '<PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON> (Easy-going)' }, 
  { id: '<PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON> (Smooth)' }, 
  { id: '<PERSON><PERSON>', name: '<PERSON><PERSON> (Smooth)' },
  { id: '<PERSON><PERSON>', name: '<PERSON><PERSON> (<PERSON>)' }, 
  { id: '<PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON> (<PERSON><PERSON>)' }, 
  { id: 'Rasalgethi', name: 'Rasalgethi (Informative)' },
  { id: 'Laomedeia', name: 'Laomedeia (Upbeat)' }, 
  { id: 'Achernar', name: 'Achernar (Soft)' }, 
  { id: 'Alnilam', name: 'Alnilam (Firm)' },
  { id: 'Schedar', name: 'Schedar (Even)' }, 
  { id: 'Gacrux', name: 'Gacrux (Mature)' }, 
  { id: 'Pulcherrima', name: 'Pulcherrima (Forward)' },
  { id: 'Achird', name: 'Achird (Friendly)' }, 
  { id: 'Zubenelgenubi', name: 'Zubenelgenubi (Casual)' }, 
  { id: 'Vindemiatrix', name: 'Vindemiatrix (Gentle)' },
  { id: 'Sadachbia', name: 'Sadachbia (Lively)' }, 
  { id: 'Sadaltager', name: 'Sadaltager (Knowledgeable)' }, 
  { id: 'Sulafar', name: 'Sulafar (Warm)' }
];

export const googleTtsApiModels = [
  { id: 'gemini-2.5-flash-preview-tts', name: 'Gemini 2.5 Flash TTS' },
  { id: 'gemini-2.5-pro-preview-tts', name: 'Gemini 2.5 Pro TTS' }
];

export const googleTtsLanguages = [
  { id: 'ar-EG', name: 'Arabic (Egyptian)' }, 
  { id: 'de-DE', name: 'German (Germany)' },
  { id: 'en-US', name: 'English (US)' }, 
  { id: 'es-US', name: 'Spanish (US)' },
  { id: 'fr-FR', name: 'French (France)' }, 
  { id: 'hi-IN', name: 'Hindi (India)' },
  { id: 'id-ID', name: 'Indonesian (Indonesia)' }, 
  { id: 'it-IT', name: 'Italian (Italy)' },
  { id: 'ja-JP', name: 'Japanese (Japan)' }, 
  { id: 'ko-KR', name: 'Korean (Korea)' },
  { id: 'pt-BR', name: 'Portuguese (Brazil)' }, 
  { id: 'ru-RU', name: 'Russian (Russia)' },
  { id: 'nl-NL', name: 'Dutch (Netherlands)' }, 
  { id: 'pl-PL', name: 'Polish (Poland)' },
  { id: 'th-TH', name: 'Thai (Thailand)' }, 
  { id: 'tr-TR', name: 'Turkish (Turkey)' },
  { id: 'vi-VN', name: 'Vietnamese (Vietnam)' }, 
  { id: 'ro-RO', name: 'Romanian (Romania)' },
  { id: 'uk-UA', name: 'Ukrainian (Ukraine)' }, 
  { id: 'bn-BD', name: 'Bengali (Bangladesh)' },
  { id: 'en-IN', name: 'English (India)' }, 
  { id: 'mr-IN', name: 'Marathi (India)' },
  { id: 'ta-IN', name: 'Tamil (India)' }, 
  { id: 'te-IN', name: 'Telugu (India)' }
];
