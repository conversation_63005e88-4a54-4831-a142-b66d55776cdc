import * as admin from 'firebase-admin';
import type { Firestore } from 'firebase-admin/firestore';

// Since we've migrated to Baserow, we only need Firebase Admin for authentication
// We won't initialize Firestore as we're using Baserow instead

console.log('[firebaseAdmin] INFO: Firebase Admin SDK setup for authentication only (Baserow migration)');

const dbAdmin: Firestore | undefined = undefined;

// Initialize Firebase Admin SDK for authentication only (without service account)
try {
  if (!admin.apps.length) {
    console.log('[firebaseAdmin] Initializing Firebase Admin SDK for authentication only...');
    admin.initializeApp();
    admin.app(); // Initialize but don't store reference
    console.log('[firebaseAdmin] Firebase Admin SDK initialized for authentication');
  } else {
    console.log('[firebaseAdmin] Using existing Firebase Admin app for authentication');
    admin.app(); // Initialize but don't store reference
  }
} catch (error) {
  console.error('[firebaseAdmin] Error initializing Firebase Admin SDK:', error);
}

// Export undefined dbAdmin since we're using Baserow
export { dbAdmin, admin as firebaseAdmin };