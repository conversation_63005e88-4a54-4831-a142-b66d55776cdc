import type { Story } from "@/types/story";

// Interface for parsed prompts
export interface ParsedPrompt {
  name?: string;
  description: string;
  originalIndex: number;
}

// Helper function to convert names to @ reference format
export const nameToReference = (name: string): string => {
  // Allow alphanumeric characters and dots in references
  return '@' + name.replace(/\s+/g, '').replace(/[^A-Za-z0-9.]/g, '');
};

// Helper function to extract entity names and placeholders from prompts
export const extractEntityNames = (storyData: Story | null): { 
  characters: Array<{ name: string; placeholder: string }>, 
  items: Array<{ name: string; placeholder: string }>, 
  locations: Array<{ name: string; placeholder: string }> 
} => {
  if (!storyData?.detailsPrompts) {
    return { characters: [], items: [], locations: [] };
  }

  const extractNamesFromSection = (section: string): Array<{ name: string; placeholder: string }> => {
    if (!section) return [];
    
    const cleanSection = section.replace(/^(Character Prompts:|Item Prompts:|Location Prompts:)\s*\n*/i, '').trim();
    if (!cleanSection) return [];
    
    return cleanSection
      .split(/\n\s*\n/)
      .map(block => {
        const lines = block.trim().split('\n').map(l => l.trim()).filter(l => l);
        if (lines.length === 0) return null;
        
        const firstLine = lines[0];
        // Check if first line looks like a name with placeholder format: "Name - @Placeholder"
        const nameWithPlaceholderMatch = firstLine.match(/^(.+?)\s*-\s*(@[A-Za-z0-9_]+)$/);
        if (nameWithPlaceholderMatch) {
          return {
            name: nameWithPlaceholderMatch[1].trim(),
            placeholder: nameWithPlaceholderMatch[2].trim()
          };
        }
        
        // Fallback: if no placeholder format, generate one from the name
        if (firstLine.length < 60 && !/[\.?!]$/.test(firstLine)) {
          return {
            name: firstLine,
            placeholder: nameToReference(firstLine)
          };
        }
        return null;
      })
      .filter(item => item !== null) as Array<{ name: string; placeholder: string }>;
  };

  return {
    characters: extractNamesFromSection(storyData.detailsPrompts.characterPrompts || ''),
    items: extractNamesFromSection(storyData.detailsPrompts.itemPrompts || ''),
    locations: extractNamesFromSection(storyData.detailsPrompts.locationPrompts || '')
  };
};

// Helper function to get all available placeholders from story data
export const getAvailablePlaceholders = (storyData: Story | null): string[] => {
  if (!storyData?.detailsPrompts) return [];
  
  const entityNames = extractEntityNames(storyData);
  return [
    ...entityNames.characters.map(char => char.placeholder),
    ...entityNames.items.map(item => item.placeholder),
    ...entityNames.locations.map(location => location.placeholder)
  ];
};

/**
 * Parses named prompts from a raw string
 * @param rawPrompts The raw prompts string
 * @param type The type of entity (Character, Item, or Location)
 * @returns Array of parsed prompts
 */
export const parseNamedPrompts = (
  rawPrompts: string | undefined,
  // type: "Character" | "Item" | "Location", // Unused parameter
): ParsedPrompt[] => {
  if (!rawPrompts) return [];

  // Normalize escaped newlines to actual newlines
  const normalizedPrompts = rawPrompts.replace(/\\n/g, "\n");

  const cleanPrompts = normalizedPrompts
    .replace(/^(Character Prompts:|Item Prompts:|Location Prompts:)\s*\n*/i, "")
    .trim();

  if (!cleanPrompts) return [];

  return cleanPrompts
    .split(/\n\s*\n/)
    .map((block, index) => {
      const lines = block
        .trim()
        .split("\n")
        .map((l) => l.trim())
        .filter((l) => l);
      if (lines.length === 0) {
        return null;
      }

      let name: string | undefined = undefined;
      let description: string;

      if (lines.length > 1) {
        const firstLine = lines[0];
        
        // Check if first line has the new format: "Name - @Placeholder"
        const nameWithPlaceholderMatch = firstLine.match(/^(.+?)\s*-\s*(@[A-Za-z0-9_]+)$/);
        if (nameWithPlaceholderMatch) {
          name = nameWithPlaceholderMatch[1].trim(); // Just the name part, without placeholder
          description = lines.slice(1).join("\n");
        } else {
          // Fallback to original logic for backward compatibility
          const firstLineIsLikelyName =
            firstLine.length < 60 &&
            !/[\.?!]$/.test(firstLine) &&
            lines.slice(1).join(" ").length > 0;

          if (firstLineIsLikelyName) {
            name = firstLine;
            description = lines.slice(1).join("\n");
          } else {
            description = lines.join("\n");
          }
        }
      } else {
        description = lines[0];
      }

      if (!description && name) {
        description = name;
        name = undefined;
      }

      if (!description) return null;

      return { name, description, originalIndex: index };
    })
    .filter((p) => p !== null) as ParsedPrompt[];
};

/**
 * Parses entity references in a prompt and replaces them with their descriptions
 * @param prompt The prompt containing entity references
 * @param storyData The story data containing entity descriptions
 * @returns The parsed prompt with entity references replaced
 */
export const parseEntityReferences = (prompt: string, storyData: Story | null): string => {
  if (!storyData) return prompt;
  
  console.log("[parseEntityReferences] Input prompt:", prompt);
  console.log("[parseEntityReferences] Has character prompts:", !!storyData?.detailsPrompts?.characterPrompts);

  const entityNames = extractEntityNames(storyData);
  console.log("[parseEntityReferences] Available entities:", entityNames);

  const characterPrompts = storyData?.detailsPrompts?.characterPrompts || "";
  const locationPrompts = storyData?.detailsPrompts?.locationPrompts || "";
  const itemPrompts = storyData?.detailsPrompts?.itemPrompts || "";

  // Helper function to normalize references for robust comparison
  const normalizeRefForComparison = (ref: string): string => {
    if (!ref.startsWith('@')) return ref.toLowerCase().replace(/[^a-z0-9]/g, '');
    return '@' + ref.substring(1).toLowerCase().replace(/[^a-z0-9]/g, '');
  };

  // Collect all possible entity references with their details
  const allEntityRefs: Array<{
    ref: string;
    normalizedRef: string;
    entityName: string;
    entityType: 'character' | 'item' | 'location';
  }> = [];

  // Add characters
  for (const char of entityNames.characters) {
    const normalizedRef = normalizeRefForComparison(char.placeholder);
    allEntityRefs.push({
      ref: char.placeholder,
      normalizedRef,
      entityName: char.name,
      entityType: 'character'
    });
  }

  // Add items
  for (const item of entityNames.items) {
    const normalizedRef = normalizeRefForComparison(item.placeholder);
    allEntityRefs.push({
      ref: item.placeholder,
      normalizedRef,
      entityName: item.name,
      entityType: 'item'
    });
  }

  // Add locations
  for (const location of entityNames.locations) {
    const normalizedRef = normalizeRefForComparison(location.placeholder);
    allEntityRefs.push({
      ref: location.placeholder,
      normalizedRef,
      entityName: location.name,
      entityType: 'location'
    });
  }

  // Sort by reference length (longest first) to ensure exact matching
  allEntityRefs.sort((a, b) => b.ref.length - a.ref.length);

  let parsedPrompt = prompt;

  // Process each entity reference from longest to shortest
  for (const entityRef of allEntityRefs) {
    // Create a regex that matches the exact reference with word boundaries
    const exactRefRegex = new RegExp('\\B' + entityRef.ref.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '\\b', 'g');
    
    console.log(`[parseEntityReferences] Checking for exact reference: ${entityRef.ref}`);
    
    if (exactRefRegex.test(parsedPrompt)) {
      console.log(`[parseEntityReferences] Found exact match for: ${entityRef.ref}`);
      
      // Get the description
      const promptsSection = entityRef.entityType === 'character' ? characterPrompts :
                            entityRef.entityType === 'item' ? itemPrompts : locationPrompts;
      
      // Updated pattern to match the new format: "Entity Name - @Placeholder"
      const entityPattern = new RegExp(
        entityRef.entityName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + "\\s*-\\s*" + entityRef.ref.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + "\\s*\\n+(.*?)(?=\\n\\n|$)",
        "s",
      );
      
      const entityMatch = promptsSection.match(entityPattern);
      if (entityMatch && entityMatch[1]) {
        const description = entityMatch[1].trim();
        console.log(`[parseEntityReferences] Found ${entityRef.entityType} description for ${entityRef.entityName}: "${description.substring(0, 100)}..."`);
        
        // Replace all occurrences of this exact reference
        parsedPrompt = parsedPrompt.replace(exactRefRegex, description);
        console.log(`[parseEntityReferences] Replaced "${entityRef.ref}" with description (${description.length} chars)`);
      } else {
        console.log(`[parseEntityReferences] No description found for "${entityRef.ref}"`);
      }
    }
  }

  console.log("[parseEntityReferences] Final parsed prompt:", parsedPrompt);
  return parsedPrompt;
};
