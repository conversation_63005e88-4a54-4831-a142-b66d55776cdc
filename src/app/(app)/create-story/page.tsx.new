import { useAuth } from '@/hooks/useAuth';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useState, useEffect } from 'react';
import { Label } from '@/components/ui/label';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Sparkles, Users, Bot, Pencil, Loader2, Edit2, Mic, Pencil1, ImageIcon, Image as LucideImage, Save, Film, Info, AlertCircle, CheckCircle, Clapperboard } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { 
  generateTitle, 
  generateScript, 
  generateCharacterPrompts, 
  generateNarrationAudio,
  generateImagePrompts,
  generateImageFromPrompt
} from '@/ai/generateStoryResources';
import { getStory, saveStory } from '@/actions/storyActions';
import { useToast } from '@/components/ui/use-toast'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Image from 'next/image';
import Link from 'next/link';

const initialStoryState: Story = {
  id: undefined,
  userId: '',
  title: '',
  userPrompt: '',
  generatedScript: undefined,
  detailsPrompts: undefined,
  narrationAudioUrl: undefined,
  narrationAudioDurationSeconds: undefined,
  elevenLabsVoiceId: undefined,
  imagePrompts: undefined,
  generatedImages: [],
  createdAt: new Date(),
  updatedAt: new Date(),
};

export default function CreateStoryPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const storyId = searchParams.get('storyId');
  const { toast } = useToast();

  const [storyData, setStoryData] = useState<Story>(initialStoryState);
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});
  const [currentStep, setCurrentStep] = useState(1);
  const [activeAccordionItem, setActiveAccordionItem] = useState<string | undefined>(`step-${currentStep}`);
  const [pageLoading, setPageLoading] = useState(true);
  const [imagesPerMinute, setImagesPerMinute] = useState(5);
  const [isSaveConfirmOpen, setIsSaveConfirmOpen] = useState(false);
  const [elevenLabsVoices, setElevenLabsVoices] = useState<ElevenLabsVoice[]>([]);
  const [selectedVoiceId, setSelectedVoiceId] = useState<string | undefined>(undefined);
  const [narrationSource, setNarrationSource] = useState<'generate' | 'upload'>('generate');
  const [uploadedAudioFileName, setUploadedAudioFileName] = useState<string | null>(null);
  const [isScriptManuallyEditing, setIsScriptManuallyEditing] = useState(false);
  const [isCharacterPromptsEditing, setIsCharacterPromptsEditing] = useState(false);
  const [isItemPromptsEditing, setIsItemPromptsEditing] = useState(false);
  const [isLocationPromptsEditing, setIsLocationPromptsEditing] = useState(false);
  const [isImagePromptEditing, setIsImagePromptEditing] = useState<boolean[]>([]);


  const updateStoryData = (updates: Partial<Story>) => {
    setStoryData(prev => ({ ...prev, ...updates }));
  };

  const handleSetLoading = (key: string, value: boolean) => {
    setIsLoading(prev => ({ ...prev, [key]: value }));
  };

    // Helper function to estimate duration from MP3 data URI (client-side)
    const getMp3DurationFromDataUriClient = (dataUri: string): Promise<number> => {
    return new Promise((resolve, reject) => {
        if (!dataUri.startsWith('data:audio/mpeg;base64,')) {
        console.warn('Cannot estimate duration: Not an MP3 data URI.');
        resolve(30); // Default duration
        return;
        }
        const audio = document.createElement('audio');
        audio.src = dataUri;
        audio.onloadedmetadata = () => {
        if (audio.duration === Infinity || !audio.duration) {
            // Fallback for browsers that struggle with data URI duration
            const base64Data = dataUri.substring('data:audio/mpeg;base64,'.length);
            const kbytes = Math.ceil(((base64Data.length / 4) * 3) / 1024); // Estimate kbytes
            const estimatedDuration = Math.max(1, Math.floor(kbytes / 16)); // Approx 128kbps
            console.warn(`Could not get precise duration, estimated: ${estimatedDuration}s`);
            resolve(estimatedDuration);
        } else {
            resolve(parseFloat(audio.duration.toFixed(2)));
        }
        };
        audio.onerror = (e) => {
            console.error('Error loading audio for duration calculation:', e);
            const base64Data = dataUri.substring('data:audio/mpeg;base64,'.length);
            const kbytes = Math.ceil(((base64Data.length / 4) * 3) / 1024);
            const estimatedDuration = Math.max(1, Math.floor(kbytes / 16));
            resolve(estimatedDuration); 
        };
    });
    };


  useEffect(() => {
    if (authLoading) return; 
    if (!user) {
        router.replace('/login');
        return;
    }

    if (user.uid && !storyData.userId) {
      updateStoryData({ userId: user.uid }); 
    }

    let initialStep = 1;
    if (storyId && user) {
      setPageLoading(true); 
      getStory(storyId, user.uid)
        .then(response => {
          if (response.success && response.data) {
            const loadedStory = response.data;
             if (loadedStory.createdAt && !(loadedStory.createdAt instanceof Date)) {
                loadedStory.createdAt = (loadedStory.createdAt as any).toDate();
            }
            if (loadedStory.updatedAt && !(loadedStory.updatedAt instanceof Date)) {
                loadedStory.updatedAt = (loadedStory.updatedAt as any).toDate();
            }
            setStoryData(loadedStory);
            if (loadedStory.elevenLabsVoiceId) {
              setSelectedVoiceId(loadedStory.elevenLabsVoiceId);
              setNarrationSource('generate'); 
            } else if (loadedStory.narrationAudioUrl) {
              setNarrationSource('upload');
              setUploadedAudioFileName("Previously uploaded audio");
            }
            
            updateStoryData({ 
                generatedScript: loadedStory.generatedScript || undefined,
                detailsPrompts: loadedStory.detailsPrompts || { characterPrompts: "", itemPrompts: "", locationPrompts: "" }
            });


            if (loadedStory.imagePrompts && loadedStory.imagePrompts.length > 0) initialStep = 4;
            else if (loadedStory.narrationAudioUrl) initialStep = 3;
            else if (loadedStory.detailsPrompts && (loadedStory.detailsPrompts.characterPrompts || loadedStory.detailsPrompts.itemPrompts || loadedStory.detailsPrompts.locationPrompts)) initialStep = 2;
            else if (loadedStory.generatedScript) initialStep = 2;
            else initialStep = 1;
            
            setCurrentStep(initialStep);
            setActiveAccordionItem(`step-${initialStep}`);
            if (loadedStory.imagePrompts) {
                setIsImagePromptEditing(Array(loadedStory.imagePrompts.length).fill(false));
            }

          } else {
            toast({ title: 'Error Loading Story', description: response.error || 'Failed to load story. Creating a new one.', variant: 'destructive' });
             setStoryData({...initialStoryState, userId: user.uid, generatedScript: initialStoryState.generatedScript || undefined}); 
             setCurrentStep(1);
             setActiveAccordionItem('step-1');
          }
        })
        .finally(() => setPageLoading(false)); 
    } else {
       setPageLoading(false); 
       if(user?.uid) { 
        setStoryData(prev => ({...prev, userId: user.uid, generatedScript: prev.generatedScript || undefined}));
       }
       setCurrentStep(initialStep); 
       setActiveAccordionItem(`step-${initialStep}`); 
    }
  }, [storyId, user, router, toast, authLoading]);

  useEffect(() => {
    setActiveAccordionItem(`step-${currentStep}`);
  }, [currentStep]);


  const handleGenerateScript = async () => {
    if (!storyData.userPrompt.trim()) {
      toast({ title: 'Missing Prompt', description: 'Please enter a story prompt.', variant: 'destructive' });
      return;
    }
    
    handleSetLoading('script', true);
    setIsScriptManuallyEditing(false); 

    let currentTitle = storyData.title;
    if (!currentTitle.trim() && storyData.userPrompt.trim()) {
        handleSetLoading('titleGen', true);
        const titleResult = await generateTitle({ userPrompt: storyData.userPrompt });
        handleSetLoading('titleGen', false);
        if (titleResult.success && titleResult.data?.title) {
            currentTitle = titleResult.data.title;
            updateStoryData({ title: currentTitle });
            toast({ title: 'Title Generated!', description: 'A title for your story has been created.', className: 'bg-primary text-primary-foreground' });
        } else {
            toast({ title: 'Title Generation Failed', description: titleResult.error || 'Could not generate a title. Please enter one manually.', variant: 'default' });
            const promptSegment = storyData.userPrompt.trim().substring(0, 30);
            currentTitle = promptSegment.length > 0 ? 
                            (promptSegment.length < storyData.userPrompt.trim().length ? `${promptSegment}... (Draft)` : `${promptSegment} (Draft)`)
                            : `Untitled Story - ${new Date().toLocaleTimeString()}`;
            updateStoryData({ title: currentTitle });
        }
    }
    
    const scriptResult = await generateScript({ prompt: storyData.userPrompt });
    if (scriptResult.success && scriptResult.data) {
      updateStoryData({ generatedScript: scriptResult.data.script });
      setCurrentStep(2);
      toast({ title: 'Script Generated!', description: 'Your story script is ready.', className: 'bg-primary text-primary-foreground' });
    } else {
      toast({ title: 'Error', description: scriptResult.error || 'Failed to generate script.', variant: 'destructive' });
    }
    handleSetLoading('script', false);
  };

  const handleGenerateDetails = async () => {
    if (!storyData.generatedScript) return;
    handleSetLoading('details', true);
    setIsCharacterPromptsEditing(false);
    setIsItemPromptsEditing(false);
    setIsLocationPromptsEditing(false);
    const result = await generateCharacterPrompts({ script: storyData.generatedScript });
    if (result.success && result.data) {
      updateStoryData({ detailsPrompts: result.data as StoryCharacterLocationItemPrompts });
      setCurrentStep(3);
      toast({ title: 'Details Generated!', description: 'Character, item, and location prompts are ready.', className: 'bg-primary text-primary-foreground' });
    } else {
      toast({ title: 'Error', description: result.error || 'Failed to generate details.', variant: 'destructive' });
    }
    handleSetLoading('details', false);
  };

  const handleGenerateNarration = async () => {
    if (!storyData.generatedScript || narrationSource !== 'generate') return;
    handleSetLoading('narration', true);

    const input = selectedVoiceId
      ? { script: storyData.generatedScript, voiceId: selectedVoiceId }
      : { script: storyData.generatedScript }; 

    const result = await generateNarrationAudio(input);

    if (result.success && result.data) {
      if (result.data.voices) {
        setElevenLabsVoices(result.data.voices);
        toast({ title: 'Voices Loaded', description: 'Please select a voice to generate narration.', className: 'bg-primary text-primary-foreground' });
      } else if (result.data.audioDataUri) {
        updateStoryData({ 
          narrationAudioUrl: result.data.audioDataUri, 
          narrationAudioDurationSeconds: result.data.duration,
          elevenLabsVoiceId: selectedVoiceId 
        });
        setUploadedAudioFileName(null); 
        setCurrentStep(4);
        toast({ title: 'Narration Generated!', description: 'Audio narration is ready.', className: 'bg-primary text-primary-foreground' });
      }
    } else {
      toast({ title: 'Narration Error', description: result.error || 'Failed to process narration.', variant: 'destructive' });
    }
    handleSetLoading('narration', false);
  };

  const handleAudioFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type !== "audio/mpeg") {
        toast({ title: "Invalid File Type", description: "Please upload an MP3 audio file.", variant: "destructive" });
        return;
      }
      if (file.size > 10 * 1024 * 1024) { 
        toast({ title: "File Too Large", description: "Please upload an audio file smaller than 10MB.", variant: "destructive" });
        return;
      }
      handleSetLoading('narrationUpload', true);
      const reader = new FileReader();
      reader.onload = async (e) => {
        const dataUri = e.target?.result as string;
        try {
            const duration = await getMp3DurationFromDataUriClient(dataUri);
            updateStoryData({
            narrationAudioUrl: dataUri,
            narrationAudioDurationSeconds: duration,
            elevenLabsVoiceId: undefined, 
            });
            setUploadedAudioFileName(file.name);
            setCurrentStep(4); 
            toast({ title: "Audio Uploaded", description: `${file.name} is ready.`, className: 'bg-primary text-primary-foreground' });
        } catch (error) {
            toast({ title: "Audio Processing Error", description: "Could not process the uploaded audio file.", variant: "destructive" });
        } finally {
            handleSetLoading('narrationUpload', false);
        }
      };
      reader.onerror = () => {
        toast({ title: "File Read Error", description: "Could not read the audio file.", variant: "destructive" });
        handleSetLoading('narrationUpload', false);
      };
      reader.readAsDataURL(file);
    }
  };


  const handleGenerateImagePrompts = async () => {
    if (!storyData.generatedScript || !storyData.detailsPrompts || !storyData.narrationAudioDurationSeconds) return;
    handleSetLoading('imagePrompts', true);
    const result = await generateImagePrompts({
      script: storyData.generatedScript,
      characterPrompts: storyData.detailsPrompts.characterPrompts || '',
      locationPrompts: storyData.detailsPrompts.locationPrompts || '',
      itemPrompts: storyData.detailsPrompts.itemPrompts || '',
      audioDurationSeconds: storyData.narrationAudioDurationSeconds,
      imagesPerMinute: imagesPerMinute,
    });
    if (result.success && result.data) {
      updateStoryData({ imagePrompts: result.data.imagePrompts });
      setIsImagePromptEditing(Array(result.data.imagePrompts.length).fill(false));
      toast({ title: 'Image Prompts Generated!', description: 'Prompts for your animation visuals are ready.', className: 'bg-primary text-primary-foreground' });
    } else {
      toast({ title: 'Error', description: result.error || 'Failed to generate image prompts.', variant: 'destructive' });
    }
    handleSetLoading('imagePrompts', false);
  };

  const handleImagePromptChange = (index: number, value: string) => {
    if (storyData.imagePrompts) {
      const updatedPrompts = [...storyData.imagePrompts];
      updatedPrompts[index] = value;
      updateStoryData({ imagePrompts: updatedPrompts });
    }
  };

  const toggleImagePromptEdit = (index: number) => {
    setIsImagePromptEditing(prev => {
        const newState = [...prev];
        newState[index] = !newState[index];
        return newState;
    });
  };


  const handleGenerateSingleImage = async (prompt: string, index: number) => {
    handleSetLoading(`image-${index}`, true);
    const result = await generateImageFromPrompt(prompt);
    if (result.success && result.imageUrl) {
      const newImage: GeneratedImage = { prompt, imageUrl: result.imageUrl, dataAiHint: result.dataAiHint };
      const currentGeneratedImages = Array.isArray(storyData.generatedImages) ? storyData.generatedImages : [];
      
      let updatedImages = [...currentGeneratedImages];
      const existingImageIndexForPrompt = updatedImages.findIndex(img => img.prompt === prompt);
      
      if (existingImageIndexForPrompt > -1) {
        updatedImages[existingImageIndexForPrompt] = newImage;
      } else {
         const newImagesArray = Array(storyData.imagePrompts?.length || 0).fill(null);
         currentGeneratedImages.forEach((img) => {
            if (img) { 
              const originalPromptIndex = storyData.imagePrompts?.indexOf(img.prompt);
              if(originalPromptIndex !== undefined && originalPromptIndex > -1) {
                  newImagesArray[originalPromptIndex] = img;
              }
            }
         });
         newImagesArray[index] = newImage;
         updatedImages = newImagesArray.filter(Boolean) as GeneratedImage[]; 
      }

      updateStoryData({ generatedImages: updatedImages });
      toast({ title: `Image ${index + 1} Generated!`, description: 'Visual for prompt ready.', className: 'bg-primary text-primary-foreground' });
    } else {
      toast({ title: 'Image Generation Error', description: result.error || `Failed to generate image ${index + 1}.`, variant: 'destructive' });
    }
    handleSetLoading(`image-${index}`, false);
  };

  const handleGenerateAllImages = async () => {
    if (!storyData.imagePrompts || storyData.imagePrompts.length === 0) return;
    handleSetLoading('allImages', true);
    
    const currentGeneratedImages = Array.isArray(storyData.generatedImages) ? storyData.generatedImages : [];
    const imagesToGenerate = storyData.imagePrompts.map((prompt, index) => ({ prompt, index })).filter(
      p => !currentGeneratedImages.some(img => img && img.prompt === p.prompt) 
    );

    const results = await Promise.all(
      imagesToGenerate.map(async ({prompt, index}) => {
        handleSetLoading(`image-${index}`, true);
        const result = await generateImageFromPrompt(prompt);
        handleSetLoading(`image-${index}`, false);
        if (result.success && result.imageUrl) {
          return { prompt, imageUrl: result.imageUrl, dataAiHint: result.dataAiHint, success: true, index };
        }
        toast({ title: 'Image Generation Error', description: result.error || `Failed for prompt: "${prompt.substring(0,30)}..."`, variant: 'destructive' });
        return { prompt, success: false, index, error: result.error };
      })
    );

    const successfulNewImages = results.filter(r => r.success).map(r => ({prompt: r.prompt, imageUrl: r.imageUrl!, dataAiHint: r.dataAiHint}));
    
    if (successfulNewImages.length > 0) {
      setStoryData(prev => {
        const existingImages = Array.isArray(prev.generatedImages) ? prev.generatedImages.filter(img => img !== null) as GeneratedImage[] : []; 
        const combined = [...existingImages, ...successfulNewImages];
        const uniqueImagesByPrompt = Array.from(new Map(combined.map(img => [img.prompt, img])).values());
        
        const orderedImages = prev.imagePrompts?.map(p => uniqueImagesByPrompt.find(img => img.prompt === p)).filter(Boolean) as GeneratedImage[] || uniqueImagesByPrompt;
        return { ...prev, generatedImages: orderedImages };
      });
    }
    
    if (successfulNewImages.length === imagesToGenerate.length && imagesToGenerate.length > 0) {
      toast({ title: 'All Remaining Images Generated!', description: 'All visuals are ready for your story.', className: 'bg-primary text-primary-foreground' });
    } else if (successfulNewImages.length > 0) {
      toast({ title: 'Image Generation Partially Completed', description: 'Some images were generated. Check individual prompts for failures.', variant: 'default' });
    } else if (imagesToGenerate.length > 0) { 
       toast({ title: 'Image Generation Failed', description: `Could not generate new images. Errors: ${results.filter(r=>!r.success).map(r => r.error).join(', ')}`, variant: 'destructive' });
    }

    handleSetLoading('allImages', false);
  };


  const handleConfirmSaveStory = async () => {
    if (!user || !user.uid || typeof user.uid !== 'string' || user.uid.trim() === '') {
      toast({ title: 'Authentication Error', description: 'User session is invalid or User ID is missing. Please re-login.', variant: 'destructive' });
      setIsSaveConfirmOpen(false); 
      return;
    }
    if (!storyData.title || !storyData.title.trim()) {
      toast({ title: 'Missing Title', description: 'Please give your story a title.', variant: 'destructive' });
      setIsSaveConfirmOpen(false); 
      return;
    }
    handleSetLoading('save', true);
    
    const storyToSave = { ...storyData };
    if (selectedVoiceId && !storyToSave.elevenLabsVoiceId && narrationSource === 'generate') {
        storyToSave.elevenLabsVoiceId = selectedVoiceId;
    }


    const result = await saveStory(storyToSave, user.uid); 
    if (result.success && result.storyId) {
      const updatedStoryData = { ...storyData, id: result.storyId };
      if (result.data?.narrationAudioUrl && result.data.narrationAudioUrl !== storyData.narrationAudioUrl) {
        updatedStoryData.narrationAudioUrl = result.data.narrationAudioUrl;
      }
       if (selectedVoiceId && narrationSource === 'generate') {
        updatedStoryData.elevenLabsVoiceId = selectedVoiceId;
      }

      setStoryData(updatedStoryData);
      
      toast({ title: 'Story Saved!', description: 'Your masterpiece is safely stored.', className: 'bg-primary text-primary-foreground' });
      if (!storyId && result.storyId) { 
          router.replace(`/create-story?storyId=${result.storyId}`, { scroll: false });
      }
    } else {
      toast({ title: 'Error Saving Story', description: result.error || 'Could not save your story.', variant: 'destructive' });
    }
    handleSetLoading('save', false);
    setIsSaveConfirmOpen(false); 
  };

  const progressPercentage = ((currentStep -1) / 3) * 100; 

  if (pageLoading || authLoading) {
    return (
      <div className="flex h-[calc(100vh-10rem)] items-center justify-center">
        <Loader2 className="h-16 w-16 animate-spin text-primary" />
        <p className="ml-4 text-xl text-foreground">Loading Story Editor...</p>
      </div>
    );
  }

  if(!user && !authLoading){ 
    return <div className="text-center p-8"><p>Redirecting to login...</p></div>
  }

  const canAssembleVideo = storyData.imagePrompts && storyData.imagePrompts.length > 0 && storyData.id !== undefined;
  const isSaveButtonDisabled = !storyData.title?.trim() || isLoading.save || !user?.uid;

  const narrationButtonText = () => {
    if (isLoading.narration) return "Processing...";
    if (narrationSource === 'generate') {
        if (storyData.narrationAudioUrl && storyData.elevenLabsVoiceId) return "Re-generate Narration";
        if (elevenLabsVoices.length > 0) return "Generate Narration with Selected Voice";
        return "Load Voices & Generate Narration";
    }
    return "Generate Narration (AI)" 
  };

  const isNarrationButtonDisabled = narrationSource === 'upload' || isLoading.narration || !storyData.generatedScript || (narrationSource === 'generate' && elevenLabsVoices.length > 0 && !selectedVoiceId && !storyData.elevenLabsVoiceId) || isLoading.narrationUpload;


  return (
    <div className="container mx-auto max-w-5xl py-8">
      <Card className="shadow-2xl">
        <CardHeader className="bg-card-foreground/5">
          <CardTitle className="text-3xl font-bold tracking-tight text-primary flex items-center">
             <Sparkles className="w-8 h-8 mr-3 text-accent" />
            {storyData.id ? 'Edit Your Story' : 'Create a New Story'}
          </CardTitle>
          <CardDescription>
            Follow the steps below to bring your animated story to life. Click Save Story anytime to store your progress.
          </CardDescription>
           <div className="mt-4">
            <Label htmlFor="storyTitle" className="text-sm font-medium flex items-center">
                Story Title 
                {isLoading.titleGen && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
            </Label>
            <Input
              id="storyTitle"
              placeholder="My Awesome Adventure"
              value={storyData.title}
              onChange={(e) => updateStoryData({ title: e.target.value })}
              className="mt-1 text-lg"
              disabled={isLoading.titleGen}
            />
          </div>
        </CardHeader>

        <CardContent className="p-6">
          <div className="mb-6">
            <Label className="text-sm font-medium">Overall Progress</Label>
            <Progress value={progressPercentage} className="w-full mt-1 h-3" />
            <p className="text-xs text-muted-foreground mt-1">Step {currentStep} of 4</p>
          </div>

          <Accordion 
            type="single" 
            collapsible 
            value={activeAccordionItem} 
            className="w-full" 
            onValueChange={(value) => {
              if (isScriptManuallyEditing && activeAccordionItem === 'step-1' && value !== 'step-1') {
                setIsScriptManuallyEditing(false);
              }
              if (activeAccordionItem === 'step-2' && value !== 'step-2' && (isCharacterPromptsEditing || isItemPromptsEditing || isLocationPromptsEditing)) {
                setIsCharacterPromptsEditing(false);
                setIsItemPromptsEditing(false);
                setIsLocationPromptsEditing(false);
              }
              if (activeAccordionItem === 'step-4' && value !== 'step-4' && isImagePromptEditing.some(Boolean)) {
                setIsImagePromptEditing(Array(storyData.imagePrompts?.length || 0).fill(false));
              }
              setActiveAccordionItem(value); 
              if (value) {
                setCurrentStep(parseInt(value.split('-')[1]));
              }
            }}
          >
            {/* Step 1: User Prompt */}
            <AccordionItem value="step-1">
              <AccordionTrigger className="text-xl font-semibold hover:no-underline data-[state=open]:text-primary">
                <div className="flex items-center">
                  <Pencil className="w-6 h-6 mr-3" /> Step 1: Craft Your Story Idea &amp; Script
                </div>
              </AccordionTrigger>
              <AccordionContent className="pt-4 space-y-4">
                <div>
                  <Label htmlFor="userPrompt" className="block text-md font-medium">Your Story Prompt</Label>
                  <Textarea
                    id="userPrompt"
                    placeholder="e.g., A brave little fox explores a magical forest, learns about courage, and meets talking animals..."
                    value={storyData.userPrompt}
                    onChange={(e) => updateStoryData({ userPrompt: e.target.value })}
                    rows={6}
                    className="text-base mt-1"
                  />
                </div>
                {storyData.generatedScript !== undefined && (
                  <div