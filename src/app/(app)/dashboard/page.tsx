
"use client";

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/components/auth-provider';
import type { Story } from '@/types/story';
import { PlusCircle, FileText, Loader2, AlertTriangle, Film, Edit, Trash2, Search } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useEffect, useState, useRef } from 'react';
// Removed Firebase Firestore imports - now using Baserow
import {formatDistanceToNow} from 'date-fns';
import { useToast } from '@/hooks/use-toast';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
// Import Baserow actions
import { deleteStory, getUserStories } from '@/actions/baserowStoryActions';

export default function DashboardPage() {
  const { user } = useAuth();
  const [stories, setStories] = useState<Story[]>([]);
  const [filteredStories, setFilteredStories] = useState<Story[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingStoryId, setDeletingStoryId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  
  // Load initial state from localStorage or use defaults
  const [statusFilter, setStatusFilter] = useState<Record<string, boolean>>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('storytailor_dashboard_statusFilter');
      return saved ? JSON.parse(saved) : { wip: true, completed: true, published: true };
    }
    return { wip: true, completed: true, published: true };
  });
  
  const [sortBy, setSortBy] = useState<'created' | 'updated'>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('storytailor_dashboard_sortBy');
      return saved === 'created' || saved === 'updated' ? saved : 'updated';
    }
    return 'updated';
  });
  
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('storytailor_dashboard_sortOrder');
      return saved === 'asc' || saved === 'desc' ? saved : 'desc';
    }
    return 'desc';
  });
  
  const { toast } = useToast();

  // Save filter preferences to localStorage whenever they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('storytailor_dashboard_statusFilter', JSON.stringify(statusFilter));
    }
  }, [statusFilter]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('storytailor_dashboard_sortBy', sortBy);
    }
  }, [sortBy]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('storytailor_dashboard_sortOrder', sortOrder);
    }
  }, [sortOrder]);

  useEffect(() => {
    if (user) {
      const fetchStories = async () => {
        setIsLoading(true);
        setError(null);
        try {
          const result = await getUserStories(user.uid);
          if (result.success && result.data) {
            setStories(result.data);
          } else {
            setError(result.error || "Failed to load your stories. Please try again later.");
          }
        } catch (err) {
          console.error("Error fetching stories:", err);
          setError("Failed to load your stories. Please try again later.");
        } finally {
          setIsLoading(false);
        }
      };
      fetchStories();
    }
  }, [user]);

  // Helper function to convert various date types to Date object
  const getDate = (date: unknown): Date => {
    if (date instanceof Date) {
      return date;
    }
    if (typeof date === 'string') {
      // Handle Baserow's YYYY-MM-DD format specifically
      if (date.match(/^\d{4}-\d{2}-\d{2}$/)) {
        const [year, month, day] = date.split('-').map(Number);
        return new Date(year, month - 1, day, 12, 0, 0); // Noon local time
      }
      return new Date(date);
    }
    if (date && typeof date === 'object' && 'toDate' in date) {
      return (date as { toDate: () => Date }).toDate();
    }
    // Fallback to current date if we can't parse it
    return new Date();
  };

  // Filter and sort stories
  useEffect(() => {
    // First filter by status
    const activeFilters = Object.entries(statusFilter)
      .filter(([, isActive]) => isActive)
      .map(([status]) => status);
    
    let result = stories;
    
    if (activeFilters.length > 0 && activeFilters.length < 3) {
      result = stories.filter(story => activeFilters.includes(story.workflowStatus || 'wip'));
    }
    
    // Then filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(story => {
        // Check title and user prompt
        if ((story.title && story.title.toLowerCase().includes(query)) ||
            (story.userPrompt && story.userPrompt.toLowerCase().includes(query))) {
          return true;
        }
        
        // Check character mappings if they exist
        if (story.character_mappings) {
          try {
            const characterMappings = typeof story.character_mappings === 'string' 
              ? JSON.parse(story.character_mappings) 
              : story.character_mappings;
              
            // Check if any character name or type includes the query
            return Object.values(characterMappings).some((character: unknown) => {
              if (typeof character === 'object' && character !== null) {
                const char = character as { name?: string; type?: string };
                return (char.name && char.name.toLowerCase().includes(query)) ||
                       (char.type && char.type.toLowerCase().includes(query));
              }
              return false;
            });
          } catch (e) {
            console.warn('Error parsing character mappings for search:', e);
          }
        }
        
        return false;
      });
    }
    
    // Then sort
    result = [...result].sort((a, b) => {
      const dateA = sortBy === 'created' ? getDate(a.createdAt) : getDate(a.updatedAt);
      const dateB = sortBy === 'created' ? getDate(b.createdAt) : getDate(b.updatedAt);
      
      const timeA = dateA.getTime();
      const timeB = dateB.getTime();
      
      // If dates are equal, use the story ID as a tiebreaker (higher ID = more recent)
      if (timeA === timeB) {
        const idA = typeof a.id === 'string' ? parseInt(a.id) || 0 : typeof a.id === 'number' ? a.id : 0;
        const idB = typeof b.id === 'string' ? parseInt(b.id) || 0 : typeof b.id === 'number' ? b.id : 0;
        return sortOrder === 'asc' ? idA - idB : idB - idA;
      }
      
      return sortOrder === 'asc' ? timeA - timeB : timeB - timeA;
    });
    
    setFilteredStories(result);
  }, [stories, statusFilter, sortBy, sortOrder, searchQuery]);

  const handleDeleteStory = async (storyId: string, storyTitle: string) => {
    if (!user) {
      toast({ title: 'Error', description: 'User not authenticated.', variant: 'destructive' });
      return;
    }

    setDeletingStoryId(storyId);
    
    try {
      const result = await deleteStory(storyId, user.uid);
      
      if (result.success) {
        toast({ 
          title: 'Story Deleted!', 
          description: `"${storyTitle}" has been permanently deleted.`, 
          className: 'bg-green-500 text-white' 
        });
        
        setStories(prevStories => prevStories.filter(story => story.id !== storyId));
      } else {
        toast({ 
          title: 'Delete Failed', 
          description: result.error || 'Failed to delete story.', 
          variant: 'destructive' 
        });
      }
    } catch (error) {
      console.error('Error deleting story:', error);
      toast({ 
        title: 'Delete Error', 
        description: 'An unexpected error occurred while deleting the story.', 
        variant: 'destructive' 
      });
    } finally {
      setDeletingStoryId(null);
    }
  };

  const toggleStatusFilter = (status: string) => {
    setStatusFilter(prev => ({
      ...prev,
      [status]: !prev[status]
    }));
  };

  const handleSortChange = (value: string) => {
    if (value === 'created' || value === 'updated') {
      setSortBy(value);
    }
  };

  const handleOrderChange = (value: string) => {
    if (value === 'asc' || value === 'desc') {
      setSortOrder(value);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="mt-4 text-lg text-muted-foreground">Loading your magical tales...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <AlertTriangle className="h-12 w-12 text-destructive" />
        <p className="mt-4 text-lg text-destructive">{error}</p>
        <Button onClick={() => window.location.reload()} className="mt-4">Retry</Button>
      </div>
    );
  }
  
  const handleSearchClick = () => {
    setIsSearchExpanded(true);
    // Focus the input after a short delay to ensure the animation completes
    setTimeout(() => {
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }, 100);
  };

  const handleSearchBlur = () => {
    // Only collapse if search is empty
    if (!searchQuery) {
      setIsSearchExpanded(false);
    }
  };

  return (
    <div className="container mx-auto">
      <div className="mb-8 flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">Your Story Collection</h1>
          <p className="text-muted-foreground">
            Revisit your adventures or craft new ones.
          </p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          {/* Search Bar */}
          <div className="relative">
            {isSearchExpanded ? (
              <div className="flex items-center rounded-md border border-input bg-background pl-3 pr-2 py-1 transition-all duration-300 ease-in-out">
                <Search className="h-4 w-4 text-muted-foreground mr-2" />
                <Input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search stories..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onBlur={handleSearchBlur}
                  className="border-0 p-0 focus-visible:ring-0"
                />
                {searchQuery && (
                  <button 
                    onClick={() => setSearchQuery('')}
                    className="ml-2 text-muted-foreground hover:text-foreground"
                  >
                    <span className="text-sm">×</span>
                  </button>
                )}
              </div>
            ) : (
              <Button 
                variant="outline" 
                size="icon"
                onClick={handleSearchClick}
                className="transition-all duration-300 ease-in-out"
              >
                <Search className="h-4 w-4" />
              </Button>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Label className="text-sm">Status:</Label>
            <div className="flex gap-2">
              <div className="flex items-center gap-1">
                <Checkbox 
                  id="wip-filter" 
                  checked={statusFilter.wip} 
                  onCheckedChange={() => toggleStatusFilter('wip')} 
                />
                <Label htmlFor="wip-filter" className="text-xs">WIP</Label>
              </div>
              <div className="flex items-center gap-1">
                <Checkbox 
                  id="completed-filter" 
                  checked={statusFilter.completed} 
                  onCheckedChange={() => toggleStatusFilter('completed')} 
                />
                <Label htmlFor="completed-filter" className="text-xs">Completed</Label>
              </div>
              <div className="flex items-center gap-1">
                <Checkbox 
                  id="published-filter" 
                  checked={statusFilter.published} 
                  onCheckedChange={() => toggleStatusFilter('published')} 
                />
                <Label htmlFor="published-filter" className="text-xs">Published</Label>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Label className="text-sm">Sort by:</Label>
            <Select value={sortBy} onValueChange={handleSortChange}>
              <SelectTrigger className="w-[120px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="updated">Updated</SelectItem>
                <SelectItem value="created">Created</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={sortOrder} onValueChange={handleOrderChange}>
              <SelectTrigger className="w-[100px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">Newest</SelectItem>
                <SelectItem value="asc">Oldest</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Button asChild size="lg" className="bg-accent hover:bg-accent/90 text-accent-foreground">
            <Link href="/create-story">
              <PlusCircle className="mr-2 h-5 w-5" /> Create New Story
            </Link>
          </Button>
        </div>
      </div>

      {filteredStories.length === 0 ? (
        <Card className="text-center py-12 shadow-lg bg-card">
          <CardHeader>
            <div className="mx-auto rounded-full bg-primary/10 p-4 w-fit">
                <FileText className="h-12 w-12 text-primary" />
            </div>
            <CardTitle className="mt-4 text-2xl font-semibold text-foreground">
              {Object.values(statusFilter).every(Boolean) 
                ? 'No Stories Yet' 
                : 'No Stories with Selected Status'}
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              {Object.values(statusFilter).every(Boolean)
                ? "It looks like your book of tales is empty. Start your first adventure now!" 
                : "You don't have any stories with the selected status. Try selecting different status options."}
            </CardDescription>
          </CardHeader>
          {Object.values(statusFilter).every(Boolean) && (
            <CardFooter className="justify-center">
              <Button asChild size="lg" className="bg-accent hover:bg-accent/90 text-accent-foreground">
                <Link href="/create-story">
                  <PlusCircle className="mr-2 h-5 w-5" /> Create Your First Story
                </Link>
              </Button>
            </CardFooter>
          )}
        </Card>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {filteredStories.map((story, index) => (
            <Card key={story.id} className="flex flex-col overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 bg-card relative">
            <div className="absolute top-2 right-2 z-10">
            <AlertDialog>
            <AlertDialogTrigger asChild>
            <Button 
              variant="destructive" 
              size="sm" 
                className="w-8 h-8 p-0 bg-destructive/80 hover:bg-destructive backdrop-blur-sm"
                  disabled={deletingStoryId === story.id}
                     >
                       {deletingStoryId === story.id ? (
                         <Loader2 className="h-4 w-4 animate-spin" />
                       ) : (
                         <Trash2 className="h-4 w-4" />
                       )}
                     </Button>
                   </AlertDialogTrigger>
                   <AlertDialogContent>
                     <AlertDialogHeader>
                       <AlertDialogTitle>Delete Story</AlertDialogTitle>
                       <AlertDialogDescription>
                         Are you sure you want to delete &quot;<strong>{story.title}</strong>&quot;? This action cannot be undone and will permanently delete the story and all its associated files (images, audio, etc.).
                       </AlertDialogDescription>
                     </AlertDialogHeader>
                     <AlertDialogFooter>
                       <AlertDialogCancel>Cancel</AlertDialogCancel>
                       <AlertDialogAction 
                         onClick={() => handleDeleteStory(story.id!, story.title)}
                         className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                       >
                         <Trash2 className="mr-2 h-4 w-4" />
                         Delete Permanently
                       </AlertDialogAction>
                     </AlertDialogFooter>
                   </AlertDialogContent>
                 </AlertDialog>
               </div>
               
               <CardHeader>
                  <div className="aspect-[16/9] bg-muted rounded-t-md overflow-hidden relative">
                   <Image 
                     src={story.generatedImages?.[0]?.imageUrl || "https://placehold.co/400x225.png"} 
                     alt={story.title || "Story image"} 
                     fill
                     style={{ objectFit: "cover" }}
                     priority={index === 0}
                     sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw, 25vw"
                     data-ai-hint="story cover"
                   />
                 </div>
                <div className="flex items-center justify-between">
                  <CardTitle className="mt-4 text-xl font-semibold text-foreground truncate">
                    {story.title || 'Untitled Story'}
                  </CardTitle>
                  {story.workflowStatus && (
                    <Badge 
                      className={`
                        ml-2 ${story.workflowStatus === 'wip' ? 'bg-yellow-500 hover:bg-yellow-600' : ''} 
                        ${story.workflowStatus === 'completed' ? 'bg-blue-500 hover:bg-blue-600' : ''} 
                        ${story.workflowStatus === 'published' ? 'bg-green-500 hover:bg-green-600' : ''}
                      `}
                    >
                      {story.workflowStatus === 'wip' ? 'WIP' : ''}
                      {story.workflowStatus === 'completed' ? 'Completed' : ''}
                      {story.workflowStatus === 'published' ? 'Published' : ''}
                    </Badge>
                  )}
                </div>
                <CardDescription className="text-sm text-muted-foreground h-10 overflow-hidden">
                  {story.userPrompt ? `${story.userPrompt.substring(0, 60)}...` : 'No prompt provided.'}
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-grow">
                <p className="text-xs text-muted-foreground">
                  {sortBy === 'created' 
                    ? `Created: ${story.createdAt ? formatDistanceToNow(
                        getDate(story.createdAt),
                        { addSuffix: true }
                      ) : 'N/A'}` 
                    : `Updated: ${story.updatedAt ? formatDistanceToNow(
                        getDate(story.updatedAt),
                        { addSuffix: true }
                      ) : 'N/A'}`}
                </p>
              </CardContent>
              <CardFooter className="flex flex-col gap-2">
              <div className="flex flex-col sm:flex-row gap-2 w-full">
              <Button asChild variant="outline" className="flex-1">
              <Link href={`/create-story?storyId=${story.id}`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Story
              </Link>
              </Button>
              <Button asChild variant="default" className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground">
              <Link href={`/assemble-video?storyId=${story.id}`}>
              <Film className="mr-2 h-4 w-4" />
              Edit Video
              </Link>
              </Button>
              </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
