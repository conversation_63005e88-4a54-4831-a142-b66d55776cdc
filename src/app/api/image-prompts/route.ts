import { NextRequest } from 'next/server';
import { generateImagePrompts } from '@/actions/storyActions';
import { getStory } from '@/actions/baserowStoryActions';
import { getUserApiKeys } from '@/actions/baserowApiKeyActions';

export const maxDuration = 300; // 5 minutes
export const dynamic = 'force-dynamic';

interface ImagePromptRequestBody {
  storyId: string;
  userId: string;
  imageProvider?: 'picsart' | 'gemini' | 'imagen3' | 'imagen4' | 'imagen4ultra';
  aiProvider?: 'google' | 'perplexity';
  googleScriptModel?: string;
  perplexityModel?: string;
  stream?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body: ImagePromptRequestBody = await request.json();
    const { storyId, userId, imageProvider, aiProvider, googleScriptModel, perplexityModel, stream } = body;

    if (!storyId || !userId) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Missing required fields: storyId and userId are required' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Get the story
    const storyResult = await getStory(storyId, userId);
    if (!storyResult.success || !storyResult.data) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: storyResult.error || 'Failed to retrieve story' 
        }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const story = storyResult.data;
    
    // Check if required data exists
    if (!story.generatedScript) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'No generated script found in the story' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (!story.detailsPrompts) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'No character/location/item details found in the story' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Calculate audio duration
    const audioDurationSeconds = story.narrationAudioDurationSeconds ||
      (story.narrationChunks?.reduce((total, chunk) => total + (chunk.duration || 0), 0)) ||
      60;

    // Get user API keys
    const userKeysResult = await getUserApiKeys(userId);
    if (!userKeysResult.success || !userKeysResult.data) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Failed to retrieve user API keys' 
        }),
        { 
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Prepare input for image prompt generation
    const imagePromptsInput = {
      userId,
      storyId,
      script: story.generatedScript,
      characterPrompts: story.detailsPrompts.characterPrompts || '',
      locationPrompts: story.detailsPrompts.locationPrompts || '',
      itemPrompts: story.detailsPrompts.itemPrompts || '',
      audioDurationSeconds,
      narrationChunks: story.narrationChunks?.map(chunk => ({
        text: chunk.text,
        duration: chunk.duration || 0,
        audioUrl: chunk.audioUrl
      })),
      imageProvider: imageProvider || 'picsart',
      isPicsart: imageProvider === 'picsart',
      aiProvider: aiProvider === 'google' || aiProvider === 'perplexity' ? aiProvider : undefined,
      perplexityModel,
      googleScriptModel
    };

    // If streaming is requested, set up a streaming response
    if (stream) {
      const stream = new ReadableStream({
        async start(controller) {
          const encoder = new TextEncoder();
          
          // Send initial response
          controller.enqueue(encoder.encode('data: {"type":"start","message":"Starting image prompt generation..."}\n\n'));
          
          try {
            // Send keep-alive messages every 10 seconds
            const keepAlive = setInterval(() => {
              controller.enqueue(encoder.encode('data: {"type":"ping","message":"Image prompt generation in progress..."}\n\n'));
            }, 10000);
            
            // Generate image prompts
            const result = await generateImagePrompts(imagePromptsInput);
            
            // Clear keep-alive interval
            clearInterval(keepAlive);
            
            if (!result.success) {
              controller.enqueue(encoder.encode('data: {"type":"error","message":"' + (result.error || 'Failed to generate image prompts') + '"}\n\n'));
              controller.close();
              return;
            }
            
            // Send success response
            controller.enqueue(encoder.encode('data: {"type":"success","data":' + JSON.stringify(result.data) + ',"message":"Image prompts generated successfully"}\n\n'));
            controller.close();
          } catch (error) {
            console.error('Image Prompt Generation Streaming Error:', error);
            controller.enqueue(encoder.encode('data: {"type":"error","message":"' + (error instanceof Error ? error.message : 'An unexpected error occurred during image prompt generation') + '"}\n\n'));
            controller.close();
          }
        }
      });
      
      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    }

    // Non-streaming version (existing implementation)
    const result = await generateImagePrompts(imagePromptsInput);

    if (!result.success) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: result.error || 'Failed to generate image prompts' 
        }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        data: result.data,
        message: 'Image prompts generated successfully'
      }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  } catch (error) {
    console.error('Image Prompt Generation API Error:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error instanceof Error ? error.message : 'An unexpected error occurred during image prompt generation' 
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}