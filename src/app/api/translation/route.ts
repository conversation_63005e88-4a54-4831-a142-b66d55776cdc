import { NextRequest } from 'next/server';
import { generateSpanishTranslation, generateRomanianTranslation } from '@/actions/storyActions';
import { getStory } from '@/actions/baserowStoryActions';
import { getUserApiKeys } from '@/actions/baserowApiKeyActions';
import { GenerateSpanishTranslationInput, GenerateRomanianTranslationInput } from '@/actions/storyActionSchemas';

export const maxDuration = 300; // 5 minutes
export const dynamic = 'force-dynamic';

interface TranslationRequestBody {
  storyId: string;
  userId: string;
  language: 'spanish' | 'romanian';
  aiProvider?: 'google' | 'perplexity';
  googleScriptModel?: string;
  perplexityModel?: string;
  stream?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body: TranslationRequestBody = await request.json();
    const { storyId, userId, language, aiProvider, googleScriptModel, perplexityModel, stream } = body;

    if (!storyId || !userId || !language) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Missing required fields: storyId, userId, and language are required' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Get the story
    const storyResult = await getStory(storyId, userId);
    if (!storyResult.success || !storyResult.data) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: storyResult.error || 'Failed to retrieve story' 
        }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const story = storyResult.data;
    
    // Check if narration chunks exist
    if (!story.narrationChunks || story.narrationChunks.length === 0) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'No narration chunks found in the story' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Prepare chunks for translation
    const chunksToTranslate = story.narrationChunks.map(chunk => ({
      id: chunk.id,
      text: chunk.text,
      index: chunk.index
    }));

    // Get user API keys
    const userKeysResult = await getUserApiKeys(userId);
    if (!userKeysResult.success || !userKeysResult.data) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Failed to retrieve user API keys' 
        }),
        { 
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // If streaming is requested, set up a streaming response
    if (stream) {
      const stream = new ReadableStream({
        async start(controller) {
          const encoder = new TextEncoder();
          
          // Send initial response
          controller.enqueue(encoder.encode('data: {"type":"start","message":"Starting translation..."}\n\n'));
          
          try {
            // Send keep-alive messages every 10 seconds
            const keepAlive = setInterval(() => {
              controller.enqueue(encoder.encode('data: {"type":"ping","message":"Translation in progress..."}\n\n'));
            }, 10000);
            
            // Perform translation based on language
            let result;
            if (language === 'spanish') {
              const translationInput: Partial<GenerateSpanishTranslationInput> = {
                userId,
                chunks: chunksToTranslate,
                googleScriptModel,
                perplexityModel
              };
              
              // Only add aiProvider if it's a valid value
              if (aiProvider === 'google' || aiProvider === 'perplexity') {
                translationInput.aiProvider = aiProvider;
              }
              
              result = await generateSpanishTranslation(translationInput as GenerateSpanishTranslationInput);
            } else if (language === 'romanian') {
              const translationInput: Partial<GenerateRomanianTranslationInput> = {
                userId,
                chunks: chunksToTranslate,
                googleScriptModel,
                perplexityModel
              };
              
              // Only add aiProvider if it's a valid value
              if (aiProvider === 'google' || aiProvider === 'perplexity') {
                translationInput.aiProvider = aiProvider;
              }
              
              result = await generateRomanianTranslation(translationInput as GenerateRomanianTranslationInput);
            } else {
              clearInterval(keepAlive);
              controller.enqueue(encoder.encode('data: {"type":"error","message":"Invalid language specified. Supported languages: spanish, romanian"}\n\n'));
              controller.close();
              return;
            }
            
            // Clear keep-alive interval
            clearInterval(keepAlive);
            
            if (!result.success) {
              controller.enqueue(encoder.encode('data: {"type":"error","message":"' + (result.error || 'Translation failed') + '"}\n\n'));
              controller.close();
              return;
            }
            
            // Send success response
            controller.enqueue(encoder.encode('data: {"type":"success","data":' + JSON.stringify(result.data) + ',"message":"' + (language.charAt(0).toUpperCase() + language.slice(1)) + ' translation completed successfully"}\n\n'));
            controller.close();
          } catch (error) {
            console.error('Translation Streaming Error:', error);
            controller.enqueue(encoder.encode('data: {"type":"error","message":"' + (error instanceof Error ? error.message : 'An unexpected error occurred during translation') + '"}\n\n'));
            controller.close();
          }
        }
      });
      
      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    }

    // Non-streaming version (existing implementation)
    let result;
    if (language === 'spanish') {
      const translationInput: Partial<GenerateSpanishTranslationInput> = {
        userId,
        chunks: chunksToTranslate,
        googleScriptModel,
        perplexityModel
      };
      
      // Only add aiProvider if it's a valid value
      if (aiProvider === 'google' || aiProvider === 'perplexity') {
        translationInput.aiProvider = aiProvider;
      }
      
      result = await generateSpanishTranslation(translationInput as GenerateSpanishTranslationInput);
    } else if (language === 'romanian') {
      const translationInput: Partial<GenerateRomanianTranslationInput> = {
        userId,
        chunks: chunksToTranslate,
        googleScriptModel,
        perplexityModel
      };
      
      // Only add aiProvider if it's a valid value
      if (aiProvider === 'google' || aiProvider === 'perplexity') {
        translationInput.aiProvider = aiProvider;
      }
      
      result = await generateRomanianTranslation(translationInput as GenerateRomanianTranslationInput);
    } else {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Invalid language specified. Supported languages: spanish, romanian' 
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (!result.success) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: result.error || 'Translation failed' 
        }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        data: result.data,
        message: language.charAt(0).toUpperCase() + language.slice(1) + ' translation completed successfully'
      }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  } catch (error) {
    console.error('Translation API Error:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error instanceof Error ? error.message : 'An unexpected error occurred during translation' 
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}