
'use client';

import React, { useState, useEffect } from 'react';
import { convertWavToPlayableUrl, needsAudioConversion } from '@/utils/audioConverter';
import { getValidMinIOUrl } from '@/utils/signedUrlGenerator';

interface ConvertibleAudioPlayerProps {
  src: string;
  className?: string;
  style?: React.CSSProperties;
  onError?: (error: string) => void;
  onCanPlay?: () => void;
  chunkId?: string;
}

export function ConvertibleAudioPlayer({ 
  src, 
  className, 
  style, 
  onError, 
  onCanPlay,
  chunkId 
}: ConvertibleAudioPlayerProps) {
  const [audioSrc, setAudioSrc] = useState<string>(src);
  const [isConverting, setIsConverting] = useState(false);
  const [conversionError, setConversionError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    let isMounted = true;
    
    const handleAudioConversion = async () => {
      console.log(`Processing audio for chunk ${chunkId}:`, src);
      
      try {
        // First, ensure we have a valid MinIO URL (refresh if expired)
        const validUrl = await getValidMinIOUrl(src);
        
        if (!validUrl) {
          console.error(`Failed to get valid URL for chunk ${chunkId}:`, src);
          if (isMounted) {
            setConversionError('Audio file URL could not be refreshed. The file may no longer exist.');
            onError?.('Failed to refresh audio URL');
          }
          return;
        }
        
        if (needsAudioConversion(validUrl)) {
          console.log(`Audio needs conversion for chunk ${chunkId}`);
          setIsConverting(true);
          setConversionError(null);
          
          try {
            const convertedUrl = await convertWavToPlayableUrl(validUrl);
            if (isMounted) {
              if (convertedUrl) {
                setAudioSrc(convertedUrl);
                console.log(`Audio converted successfully for chunk: ${chunkId}`);
              } else {
                console.error(`Failed to convert audio for chunk ${chunkId}, URL: ${validUrl}`);
                setConversionError('Failed to convert audio file');
                onError?.('Audio conversion failed');
              }
              setIsConverting(false);
            }
          } catch (error) {
            if (isMounted) {
              console.error(`Conversion error for chunk ${chunkId}:`, error, 'URL:', validUrl);
              
              // Check if this is a MinIO URL error
              if (validUrl.includes('minio-api.holoanima.com')) {
                setConversionError('Audio file could not be loaded. This may be due to network connectivity issues. Try the retry button below.');
              } else {
                setConversionError(`Conversion error: ${error}`);
              }
              
              onError?.(`Audio conversion error: ${error}`);
              setIsConverting(false);
            }
          }
        } else {
          // If no conversion needed, use valid URL directly
          console.log(`No conversion needed for chunk ${chunkId}, using valid URL:`, validUrl);
          if (isMounted) {
            setAudioSrc(validUrl);
          }
        }
      } catch (error) {
        console.error(`Error getting valid URL for chunk ${chunkId}:`, error);
        if (isMounted) {
          setConversionError('Failed to refresh audio URL. The file may no longer exist or there may be connectivity issues.');
          onError?.('Failed to refresh audio URL');
        }
      }
    };

    handleAudioConversion();

    return () => {
      isMounted = false;
      // Cleanup logic is handled by the second useEffect
    };
  }, [src, chunkId, onError, retryCount]); // Include retryCount to trigger retry

  // Cleanup blob URL when audioSrc changes or component unmounts
  useEffect(() => {
    const currentAudioSrcForCleanup = audioSrc; // Capture current value for cleanup
    return () => {
      // Only revoke if it's a blob URL and not the original src prop
      if (currentAudioSrcForCleanup && currentAudioSrcForCleanup !== src && currentAudioSrcForCleanup.startsWith('blob:')) {
        console.log(`Revoking blob URL: ${currentAudioSrcForCleanup} for chunk ${chunkId}`);
        URL.revokeObjectURL(currentAudioSrcForCleanup);
      }
    };
  }, [audioSrc, src, chunkId]); // This effect correctly depends on audioSrc and src for cleanup

  if (isConverting) {
    return (
      <div className={className} style={style}>
        <div className="flex items-center justify-center h-8 bg-gray-100 rounded">
          <span className="text-xs text-gray-600">Converting audio...</span>
        </div>
      </div>
    );
  }

  if (conversionError) {
    return (
      <div className={className} style={style}>
        <div className="flex flex-col p-3 bg-red-100 rounded">
          <span className="text-sm font-medium text-red-600">Audio Error:</span>
          <span className="text-xs text-red-600 mt-1">{conversionError}</span>
          <div className="mt-2 text-xs text-gray-600">
            <p>Common causes and solutions:</p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li><strong>Expired URL:</strong> Try the retry button below first</li>
              <li><strong>Missing file:</strong> Try regenerating the audio for this section</li>
              <li><strong>Network issues:</strong> Check your internet connection</li>
              <li><strong>Browser blocking:</strong> If using an ad blocker, try disabling it temporarily</li>
            </ul>
          </div>
          <button 
            onClick={() => {
              setConversionError(null);
              setRetryCount(count => count + 1);
            }}
            className="mt-2 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
          >
            Retry Loading Audio
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <audio
        controls
        src={audioSrc}
        className={className}
        style={style}
        preload="metadata"
        onError={(e) => {
          const target = e.target as HTMLAudioElement;
          const errorCode = target.error?.code || 'unknown';
          const errorMessage = target.error?.message || 'Unknown audio error';
          const errorMsg = `Audio error ${errorCode}: ${errorMessage}`;
          console.error(`Audio loading error for chunk: ${chunkId}`, errorMsg, 'URL:', audioSrc);
          
          if (errorMsg.includes("DEMUXER_ERROR_COULD_NOT_OPEN")) {
            console.warn(`Possible reasons for audio error:
              1. Story may not have been saved before generating audio (should be fixed now)
              2. Ad blocker or privacy extension might be blocking storage connections
              3. The audio file might be corrupted or in an unsupported format`);
            
            if (audioSrc.includes('storage.googleapis.com') || audioSrc.includes('minio-api.holoanima.com')) {
              const storageType = audioSrc.includes('storage.googleapis.com') ? 'Firebase Storage' : 'MinIO Storage';
              console.warn(`${storageType} URL detected. This might be blocked by ad blockers or privacy extensions.`);
              
              setConversionError(
                `Audio failed to load. This may be caused by an ad blocker or privacy extension
                blocking ${storageType} connections. Try disabling them temporarily for this site.`
              );
              return;
            }
          }
          
          setConversionError(`Failed to load audio: ${errorMessage}`);
          onError?.(errorMsg);
        }}
        onCanPlay={() => {
          console.log(`Audio can play for chunk: ${chunkId}`);
          onCanPlay?.();
        }}
        onLoadStart={() => {
          console.log(`Audio load started for chunk: ${chunkId}`);
        }}
        onLoadedMetadata={() => {
          console.log(`Audio metadata loaded for chunk: ${chunkId}`);
        }}
      >
        <source src={audioSrc} type="audio/wav" />
        Your browser does not support the audio element.
      </audio>

    </div>
  );
}
