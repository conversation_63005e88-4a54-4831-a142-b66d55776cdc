import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Users, Loader2, Edit2, RefreshCw, Youtube, Image } from 'lucide-react'; // Removed Settings
import Link from 'next/link'; // Added Link
import NextImage from 'next/image';
import { generateCharacterPrompts, generateSeoMetadata, generateThumbnailPrompt, generateThumbnailImage } from '@/actions/storyActions';
import { saveStory, updateStructuredEntityMappingsInBaserow } from '@/actions/baserowStoryActions'; // Corrected import path and added new function
import { useToast } from '@/hooks/use-toast';
import type { UseStoryStateReturn } from '@/hooks/useStoryState';
import type { StoryCharacterLocationItemPrompts } from '@/types/story';
import type { ImageStyleId } from '@/types/imageStyles';
import { IMAGE_STYLES, DEFAULT_STYLE_ID } from '@/types/imageStyles';
import { DetailImageManager } from './DetailImageManager';

interface StoryDetailsStepProps {
  storyState: UseStoryStateReturn;
}

export function StoryDetailsStep({ storyState }: StoryDetailsStepProps) {
  const { toast } = useToast();
  const {
    storyData,
    updateStoryData,
    isLoading,
    handleSetLoading,
    setCurrentStep,
    isCharacterPromptsEditing,
    setIsCharacterPromptsEditing,
    isItemPromptsEditing,
    setIsItemPromptsEditing,
    isLocationPromptsEditing,
    setIsLocationPromptsEditing,
    isYoutubeTitleEditing,
    setIsYoutubeTitleEditing,
    isYoutubeDescriptionEditing,
    setIsYoutubeDescriptionEditing,
    isYoutubeThumbnailPromptEditing,
    setIsYoutubeThumbnailPromptEditing,
    imageProvider,
    setImageProvider,
    userApiKeys, // Get userApiKeys
    apiKeysLoading, // Get apiKeysLoading
    aiProvider, // Added
    perplexityModel, // Added
    googleScriptModel // Added
  } = storyState;

  const googleKeyMissing = !apiKeysLoading && !userApiKeys?.googleApiKey;

  const handleGenerateDetails = async () => {
    // Updated API key check to be provider-specific
    if (aiProvider === 'google' && googleKeyMissing) {
      toast({ title: 'API Key Required', description: 'Please configure your Google API Key in Account Settings to generate details.', variant: 'destructive' });
      return;
    }
    if (aiProvider === 'perplexity' && !userApiKeys?.perplexityApiKey && !apiKeysLoading) {
      toast({ title: 'API Key Required', description: 'Please configure your Perplexity API Key in Account Settings to generate details.', variant: 'destructive' });
      return;
    }

    if (!storyData.generatedScript) return;
    handleSetLoading('details', true);
    setIsCharacterPromptsEditing(false);
    setIsItemPromptsEditing(false);
    setIsLocationPromptsEditing(false);
    
    const result = await generateCharacterPrompts({
      script: storyData.generatedScript,
      chunks: storyData.narrationChunks?.map(chunk => chunk.text) || [],
      imageStyleId: storyData.imageStyleId,
      imageProvider: imageProvider,
      userId: storyData.userId,
      aiProvider: aiProvider,
      perplexityModel: perplexityModel,
      googleScriptModel: googleScriptModel,
    });
    if (result.success && result.data) {
      const updatedStoryData = {
        ...storyData,
        detailsPrompts: result.data as StoryCharacterLocationItemPrompts
      };
      
      updateStoryData({ detailsPrompts: result.data as StoryCharacterLocationItemPrompts });
      
      if (storyData.id && storyData.userId) {
        try {
          await saveStory(updatedStoryData, storyData.userId);
          console.log('Auto-saved story with new character/location/item details');
        } catch (error) {
          console.error('Failed to auto-save story after details generation:', error);
        }
      }
      
      setCurrentStep(3);
      toast({ title: 'Details Generated!', description: 'Character, item, and location prompts are ready.', className: 'bg-primary text-primary-foreground' });
    } else {
      toast({ title: 'Error', description: result.error || 'Failed to generate details.', variant: 'destructive' });
    }
    handleSetLoading('details', false);
  };

  const handleRegeneratePrompts = async () => {
    // Updated API key check to be provider-specific
    if (aiProvider === 'google' && googleKeyMissing) {
      toast({ title: 'API Key Required', description: 'Please configure your Google API Key in Account Settings to regenerate prompts.', variant: 'destructive' });
      return;
    }
    if (aiProvider === 'perplexity' && !userApiKeys?.perplexityApiKey && !apiKeysLoading) {
      toast({ title: 'API Key Required', description: 'Please configure your Perplexity API Key in Account Settings to regenerate prompts.', variant: 'destructive' });
      return;
    }

    if (!storyData.generatedScript) return;
    handleSetLoading('details', true);
    
    const result = await generateCharacterPrompts({
      script: storyData.generatedScript,
      chunks: storyData.narrationChunks?.map(chunk => chunk.text) || [],
      imageStyleId: storyData.imageStyleId,
      imageProvider: imageProvider,
      userId: storyData.userId,
      aiProvider: aiProvider,
      perplexityModel: perplexityModel,
      googleScriptModel: googleScriptModel,
    });
    if (result.success && result.data) {
      const updatedStoryData = {
        ...storyData,
        detailsPrompts: result.data as StoryCharacterLocationItemPrompts
      };
      
      updateStoryData({ detailsPrompts: result.data as StoryCharacterLocationItemPrompts });
      
      if (storyData.id && storyData.userId) {
        try {
          await saveStory(updatedStoryData, storyData.userId);
          console.log('Auto-saved story with regenerated character/location/item details');
        } catch (error) {
          console.error('Failed to auto-save story after details regeneration:', error);
        }
      }
      
      toast({ 
        title: 'Prompts Regenerated!', 
        description: 'Character, item, and location prompts updated with consistency features.', 
        className: 'bg-primary text-primary-foreground' 
      });
    } else {
      toast({ title: 'Error', description: result.error || 'Failed to regenerate details.', variant: 'destructive' });
    }
    handleSetLoading('details', false);
  };

  const handleGenerateSeoMetadata = async () => {
    // Updated API key check to be provider-specific
    if (aiProvider === 'google' && googleKeyMissing) {
      toast({ title: 'API Key Required', description: 'Please configure your Google API Key in Account Settings to generate SEO metadata.', variant: 'destructive' });
      return;
    }
    if (aiProvider === 'perplexity' && !userApiKeys?.perplexityApiKey && !apiKeysLoading) {
      toast({ title: 'API Key Required', description: 'Please configure your Perplexity API Key in Account Settings to generate SEO metadata.', variant: 'destructive' });
      return;
    }

    if (!storyData.generatedScript) return;
    handleSetLoading('seo', true);
    
    const result = await generateSeoMetadata({
      script: storyData.generatedScript,
      userId: storyData.userId,
      aiProvider: aiProvider,
      perplexityModel: perplexityModel,
      googleScriptModel: googleScriptModel,
    });
    
    if (result.success && result.data) {
      const updatedStoryData = {
        ...storyData,
        youtubeTitle: result.data.youtubeTitle,
        youtubeDescription: result.data.youtubeDescription
      };
      
      updateStoryData({ 
        youtubeTitle: result.data.youtubeTitle,
        youtubeDescription: result.data.youtubeDescription 
      });
      
      if (storyData.id && storyData.userId) {
        try {
          await saveStory(updatedStoryData, storyData.userId);
          console.log('Auto-saved story with SEO metadata');
        } catch (error) {
          console.error('Failed to auto-save story after SEO generation:', error);
        }
      }
      
      toast({ title: 'SEO Metadata Generated!', description: 'YouTube title and description optimized for discoverability.', className: 'bg-primary text-primary-foreground' });
    } else {
      toast({ title: 'Error', description: result.error || 'Failed to generate SEO metadata.', variant: 'destructive' });
    }
    handleSetLoading('seo', false);
  };

  const handleGenerateThumbnailPrompt = async () => {
    // Updated API key check to be provider-specific
    if (aiProvider === 'google' && googleKeyMissing) {
      toast({ title: 'API Key Required', description: 'Please configure your Google API Key in Account Settings to generate thumbnail prompt.', variant: 'destructive' });
      return;
    }
    if (aiProvider === 'perplexity' && !userApiKeys?.perplexityApiKey && !apiKeysLoading) {
      toast({ title: 'API Key Required', description: 'Please configure your Perplexity API Key in Account Settings to generate thumbnail prompt.', variant: 'destructive' });
      return;
    }

    if (!storyData.generatedScript) return;
    handleSetLoading('thumbnail', true);
    
    const result = await generateThumbnailPrompt({
      script: storyData.generatedScript || '',
      characterPrompts: storyData.detailsPrompts?.characterPrompts,
      itemPrompts: storyData.detailsPrompts?.itemPrompts,
      locationPrompts: storyData.detailsPrompts?.locationPrompts,
      userId: storyData.userId || '',
      storyId: storyData.id,
      aiProvider: aiProvider as 'google' | 'perplexity',
      googleScriptModel: googleScriptModel,
      perplexityModel: perplexityModel,
    });
    
    if (result.success && result.data) {
      const updatedStoryData = {
        ...storyData,
        youtubeThumbnailPrompt: result.data.thumbnailPrompt
      };
      
      updateStoryData({ 
        youtubeThumbnailPrompt: result.data.thumbnailPrompt
      });
      
      if (storyData.id && storyData.userId) {
        try {
          await saveStory(updatedStoryData, storyData.userId);
          console.log('Auto-saved story with thumbnail prompt');
        } catch (error) {
          console.error('Failed to auto-save story after thumbnail generation:', error);
        }
      }
      
      toast({ title: 'Thumbnail Prompt Generated!', description: 'YouTube thumbnail prompt optimized for high click-through rates.', className: 'bg-primary text-primary-foreground' });
    } else {
      toast({ title: 'Error', description: result.error || 'Failed to generate thumbnail prompt.', variant: 'destructive' });
    }
    handleSetLoading('thumbnail', false);
  };

  const handleGenerateThumbnailImage = async () => {
    if (!storyData.youtubeThumbnailPrompt) {
      toast({ title: 'No Thumbnail Prompt', description: 'Please generate a thumbnail prompt first.', variant: 'destructive' });
      return;
    }

    if (!storyData.userId) {
      toast({ title: 'User Required', description: 'Please ensure you are logged in.', variant: 'destructive' });
      return;
    }

    handleSetLoading('thumbnailImage', true);
    
    const result = await generateThumbnailImage({
      thumbnailPrompt: storyData.youtubeThumbnailPrompt,
      userId: storyData.userId,
      storyId: storyData.id,
    });
    
    if (result.success && result.data) {
      const updatedStoryData = {
        ...storyData,
        youtubeThumbnailImageUrl: result.data.thumbnailImageUrl
      };
      
      updateStoryData({ 
        youtubeThumbnailImageUrl: result.data.thumbnailImageUrl
      });
      
      if (storyData.id && storyData.userId) {
        try {
          await saveStory(updatedStoryData, storyData.userId);
          console.log('Auto-saved story with thumbnail image');
        } catch (error) {
          console.error('Failed to auto-save story after thumbnail image generation:', error);
        }
      }
      
      toast({ title: 'Thumbnail Generated!', description: 'YouTube thumbnail image created with Imagen 4 Ultra.', className: 'bg-primary text-primary-foreground' });
    } else {
      toast({ title: 'Error', description: result.error || 'Failed to generate thumbnail image.', variant: 'destructive' });
    }
    handleSetLoading('thumbnailImage', false);
  };

  if (!storyData.generatedScript) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Step 2: Character & Scene Details
          </CardTitle>
          <CardDescription>
            Generate a script first to continue with character and scene details.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Step 2: Character & Scene Details
        </CardTitle>
        <CardDescription>
          Generate detailed prompts for characters, items, and locations in your story.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-md bg-muted/50">
          <div className="space-y-2">
            <Label>Image Provider for Details</Label>
            <Select 
              value={imageProvider} 
              onValueChange={(value: 'picsart' | 'gemini' | 'imagen3' | 'imagen4' | 'imagen4ultra') => setImageProvider(value)}
              disabled={apiKeysLoading}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="picsart">PicsArt AI</SelectItem>
                <SelectItem value="gemini">Gemini</SelectItem>
                <SelectItem value="imagen3">Imagen 3</SelectItem>
                <SelectItem value="imagen4">Imagen 4 (Preview)</SelectItem>
                <SelectItem value="imagen4ultra">Imagen 4 Ultra</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Art Style</Label>
            <Select 
              value={storyData.imageStyleId || DEFAULT_STYLE_ID} 
              onValueChange={(value: ImageStyleId) => updateStoryData({ imageStyleId: value })}
              disabled={apiKeysLoading}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.values(IMAGE_STYLES).map((style) => (
                  <SelectItem key={style.id} value={style.id}>
                    <div>
                      <div className="font-medium">{style.name}</div>
                      <div className="text-xs text-muted-foreground">{style.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={handleGenerateDetails}
            disabled={isLoading.details || apiKeysLoading || (aiProvider === 'google' && googleKeyMissing) || (aiProvider === 'perplexity' && !userApiKeys?.perplexityApiKey)}
            className="flex-1"
          >
            {isLoading.details || apiKeysLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {apiKeysLoading ? "Checking API Keys..." : "Generating Details..."}
              </>
            ) : (
              <>
                <Users className="mr-2 h-4 w-4" />
                Generate Character & Scene Details
              </>
            )}
          </Button>
          
          {storyData.detailsPrompts && (
            <Button
              onClick={handleRegeneratePrompts}
              disabled={isLoading.details || apiKeysLoading || (aiProvider === 'google' && googleKeyMissing) || (aiProvider === 'perplexity' && !userApiKeys?.perplexityApiKey)}
              variant="outline"
              className="px-3"
              title="Regenerate prompts with updated consistency requirements"
            >
              {isLoading.details || apiKeysLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
          <Button
            onClick={handleGenerateSeoMetadata}
            disabled={isLoading.seo || apiKeysLoading || (aiProvider === 'google' && googleKeyMissing) || (aiProvider === 'perplexity' && !userApiKeys?.perplexityApiKey)}
            variant="secondary"
          >
            {isLoading.seo || apiKeysLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {apiKeysLoading ? "Checking API Keys..." : "Generating SEO..."}
              </>
            ) : (
              <>
                <Youtube className="mr-2 h-4 w-4" />
                Generate YouTube Title & Description
              </>
            )}
          </Button>
          
          <Button
            onClick={handleGenerateThumbnailPrompt}
            disabled={isLoading.thumbnail || apiKeysLoading || (aiProvider === 'google' && googleKeyMissing) || (aiProvider === 'perplexity' && !userApiKeys?.perplexityApiKey)}
            variant="outline"
          >
            {isLoading.thumbnail || apiKeysLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {apiKeysLoading ? "Checking API Keys..." : "Generating Thumbnail..."}
              </>
            ) : (
              <>
                {/* eslint-disable-next-line jsx-a11y/alt-text */}
                <Image className="mr-2 h-4 w-4" aria-hidden="true" />
                Generate Thumbnail Prompt
              </>
            )}
          </Button>
        </div>
        {(aiProvider === 'google' && googleKeyMissing) && (
          <p className="text-xs text-destructive text-center">
            Google API Key required for detail generation. Please set it in <Link href="/settings" className="underline">Account Settings</Link>.
          </p>
        )}
        {(aiProvider === 'perplexity' && !userApiKeys?.perplexityApiKey && !apiKeysLoading) && (
          <p className="text-xs text-destructive text-center">
            Perplexity API Key required for detail generation. Please set it in <Link href="/settings" className="underline">Account Settings</Link>.
          </p>
        )}

        {storyData.youtubeTitle && storyData.youtubeDescription && (
          <div className="space-y-4 mt-6 p-4 border rounded-md bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950/20 dark:to-pink-950/20">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-red-700 dark:text-red-400">
                <Youtube className="h-5 w-5" />
                <Label className="text-base font-semibold">YouTube SEO Metadata</Label>
              </div>
              <Button
                onClick={handleGenerateSeoMetadata}
                disabled={isLoading.seo || apiKeysLoading || (aiProvider === 'google' && googleKeyMissing) || (aiProvider === 'perplexity' && !userApiKeys?.perplexityApiKey)}
                variant="outline"
                size="sm"
                title="Regenerate YouTube title and description"
              >
                {isLoading.seo || apiKeysLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
            </div>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Optimized Title</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsYoutubeTitleEditing(!isYoutubeTitleEditing)}
                  >
                    <Edit2 className="mr-1 h-3 w-3" />
                    {isYoutubeTitleEditing ? 'Save' : 'Edit'}
                  </Button>
                </div>
                
                {isYoutubeTitleEditing ? (
                  <>
                    <Textarea
                      value={storyData.youtubeTitle || ''}
                      onChange={(e) => updateStoryData({ youtubeTitle: e.target.value })}
                      rows={2}
                      className="text-sm font-medium"
                      placeholder="Enter optimized YouTube title..."
                      maxLength={70}
                    />
                    <p className="text-xs text-muted-foreground">
                      {(storyData.youtubeTitle || '').length}/70 characters (mobile optimized)
                    </p>
                  </>
                ) : (
                  <>
                    <div className="p-3 border rounded-md bg-white/70 dark:bg-gray-900/50 text-sm font-medium">
                      {storyData.youtubeTitle}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {storyData.youtubeTitle.length}/70 characters (mobile optimized)
                    </p>
                  </>
                )}
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Optimized Description</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsYoutubeDescriptionEditing(!isYoutubeDescriptionEditing)}
                  >
                    <Edit2 className="mr-1 h-3 w-3" />
                    {isYoutubeDescriptionEditing ? 'Save' : 'Edit'}
                  </Button>
                </div>
                
                {isYoutubeDescriptionEditing ? (
                  <>
                    <Textarea
                      value={storyData.youtubeDescription || ''}
                      onChange={(e) => updateStoryData({ youtubeDescription: e.target.value })}
                      rows={6}
                      className="text-sm"
                      placeholder="Enter optimized YouTube description..."
                    />
                    <p className="text-xs text-muted-foreground">
                      SEO optimized for discoverability and engagement
                    </p>
                  </>
                ) : (
                  <>
                    <div className="p-3 border rounded-md bg-white/70 dark:bg-gray-900/50 text-sm whitespace-pre-wrap max-h-32 overflow-y-auto">
                      {storyData.youtubeDescription}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      SEO optimized for discoverability and engagement
                    </p>
                  </>
                )}
              </div>
            </div>
          </div>
        )}

        {storyData.youtubeThumbnailPrompt && (
          <div className="space-y-4 mt-6 p-4 border rounded-md bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-950/20 dark:to-blue-950/20">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-purple-700 dark:text-purple-400">
                {/* eslint-disable-next-line jsx-a11y/alt-text */}
                <Image className="h-5 w-5" aria-hidden="true" />
                <Label className="text-base font-semibold">YouTube Thumbnail Prompt</Label>
              </div>
              <Button
                onClick={handleGenerateThumbnailPrompt}
                disabled={isLoading.thumbnail || apiKeysLoading || (aiProvider === 'google' && googleKeyMissing) || (aiProvider === 'perplexity' && !userApiKeys?.perplexityApiKey)}
                variant="outline"
                size="sm"
                title="Regenerate thumbnail prompt with character consistency"
              >
                {isLoading.thumbnail || apiKeysLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Imagen 4 Ultra Optimized Prompt</Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={async () => {
                    const newEditingState = !isYoutubeThumbnailPromptEditing;
                    setIsYoutubeThumbnailPromptEditing(newEditingState);
                    
                    // When saving (exiting edit mode), update the story in Baserow
                    if (!newEditingState && storyData.id && storyData.userId) {
                      try {
                        const result = await saveStory(storyData, storyData.userId);
                        if (result.success) {
                          console.log('[StoryDetailsStep] Successfully saved thumbnail prompt to Baserow');
                        } else {
                          console.error('[StoryDetailsStep] Failed to save thumbnail prompt to Baserow:', result.error);
                          toast({ 
                            title: 'Save Warning', 
                            description: 'Thumbnail prompt may not have been saved to database.', 
                            variant: 'destructive' 
                          });
                        }
                      } catch (error) {
                        console.error('[StoryDetailsStep] Error saving thumbnail prompt:', error);
                        toast({ 
                          title: 'Save Error', 
                          description: 'Failed to save thumbnail prompt to database.', 
                          variant: 'destructive' 
                        });
                      }
                    }
                  }}
                >
                  <Edit2 className="mr-1 h-3 w-3" />
                  {isYoutubeThumbnailPromptEditing ? 'Save' : 'Edit'}
                </Button>
              </div>
              
              {isYoutubeThumbnailPromptEditing ? (
                <>
                  <Textarea
                    value={storyData.youtubeThumbnailPrompt || ''}
                    onChange={(e) => updateStoryData({ youtubeThumbnailPrompt: e.target.value })}
                    rows={4}
                    className="text-sm"
                    placeholder="Enter Imagen 4 Ultra thumbnail prompt..."
                  />
                  <p className="text-xs text-muted-foreground">
                    Optimized for YouTube thumbnails with high CTR psychology
                  </p>
                </>
              ) : (
                <>
                  <div className="p-3 border rounded-md bg-white/70 dark:bg-gray-900/50 text-sm whitespace-pre-wrap">
                    {storyData.youtubeThumbnailPrompt}
                  </div>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>Ready for Imagen 4 Ultra generation</span>
                    <span className="text-purple-600 dark:text-purple-400 font-medium">
                      High CTR Optimized
                    </span>
                  </div>
                </>
              )}
              
              {!isYoutubeThumbnailPromptEditing && (
                <div className="mt-3">
                  <Button
                    onClick={handleGenerateThumbnailImage}
                    disabled={isLoading.thumbnailImage || !storyData.youtubeThumbnailPrompt}
                    variant="default"
                    size="sm"
                    className="w-full"
                  >
                    {isLoading.thumbnailImage ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating Thumbnail Image...
                      </>
                    ) : (
                      <>
                        {/* eslint-disable-next-line jsx-a11y/alt-text */}
                        <Image className="mr-2 h-4 w-4" aria-hidden="true" />
                        Generate Thumbnail Image
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>

            {storyData.youtubeThumbnailImageUrl && (
              <div className="mt-4 space-y-2">
                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Generated Thumbnail</Label>
                <div className="relative">
                  <NextImage 
                    src={storyData.youtubeThumbnailImageUrl} 
                    alt="YouTube Thumbnail"
                    className="w-full max-w-md rounded-lg border shadow-lg"
                    style={{ aspectRatio: '16/9' }}
                    width={400}
                    height={225}
                  />
                  <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                    YouTube Thumbnail
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Generated with Imagen 4 Ultra • Ready for YouTube upload
                </p>
              </div>
            )}
          </div>
        )}

        {storyData.detailsPrompts && (
          <div className="space-y-6 mt-6">
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-800">
                <strong>Placeholder Reference:</strong> The highlighted placeholders (like <span className="px-1 py-0.5 bg-blue-100 text-blue-800 rounded text-xs font-mono border">@CharacterName</span>) 
                can be used in Step 4 image prompts to reference these exact entities. Use the exact placeholder format shown here.
              </p>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Character Prompts</Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={async () => {
                    const newEditingState = !isCharacterPromptsEditing;
                    setIsCharacterPromptsEditing(newEditingState);
                    
                    // When saving (exiting edit mode), update Baserow columns
                    if (!newEditingState && storyData.id && storyData.userId && storyData.detailsPrompts) {
                      try {
                        const result = await updateStructuredEntityMappingsInBaserow(
                          storyData.id,
                          storyData.userId,
                          storyData.detailsPrompts
                        );
                        
                        if (result.success) {
                          console.log('[StoryDetailsStep] Successfully updated character mappings in Baserow');
                        } else {
                          console.error('[StoryDetailsStep] Failed to update character mappings in Baserow:', result.error);
                          toast({ 
                            title: 'Save Warning', 
                            description: 'Character mappings may not have been saved to database.', 
                            variant: 'destructive' 
                          });
                        }
                      } catch (error) {
                        console.error('[StoryDetailsStep] Error updating character mappings:', error);
                        toast({ 
                          title: 'Save Error', 
                          description: 'Failed to save character mappings to database.', 
                          variant: 'destructive' 
                        });
                      }
                    }
                  }}
                >
                  <Edit2 className="mr-1 h-3 w-3" />
                  {isCharacterPromptsEditing ? 'Save' : 'Edit'}
                </Button>
              </div>
              
              {isCharacterPromptsEditing ? (
                <Textarea
                  value={storyData.detailsPrompts.characterPrompts || ''}
                  onChange={(e) => {
                    const newDetailsPrompts = {
                      ...storyData.detailsPrompts!,
                      characterPrompts: e.target.value
                    };
                    updateStoryData({
                      detailsPrompts: newDetailsPrompts
                    });
                    
                    // Update Baserow columns when character prompts change
                    if (storyData.id && storyData.userId) {
                      // Debounce the updates to avoid too many API calls
                      const timeoutId = setTimeout(async () => {
                        try {
                          const result = await updateStructuredEntityMappingsInBaserow(
                            storyData.id!,
                            storyData.userId!,
                            newDetailsPrompts
                          );
                          
                          if (result.success) {
                            console.log('[StoryDetailsStep] Successfully updated character mappings in Baserow');
                          } else {
                            console.error('[StoryDetailsStep] Failed to update character mappings in Baserow:', result.error);
                          }
                        } catch (error) {
                          console.error('[StoryDetailsStep] Error updating character mappings:', error);
                        }
                      }, 1000); // 1 second debounce
                      
                      // Clear previous timeout if it exists
                      const existingTimeout = (window as Window & typeof globalThis & { characterUpdateTimeout?: NodeJS.Timeout }).characterUpdateTimeout;
                      if (existingTimeout) {
                        clearTimeout(existingTimeout);
                      }
                      (window as Window & typeof globalThis & { characterUpdateTimeout?: NodeJS.Timeout }).characterUpdateTimeout = timeoutId;
                    }
                  }}
                  rows={4}
                  className="text-sm"
                  placeholder="Enter character descriptions, one per paragraph..."
                />
              ) : (
                <div className="p-3 border rounded-md bg-muted/50 text-sm whitespace-pre-wrap max-h-40 overflow-y-auto">
                  {storyData.detailsPrompts.characterPrompts ? (
                    <div dangerouslySetInnerHTML={{
                      __html: storyData.detailsPrompts.characterPrompts.replace(
                        /(@[A-Za-z0-9_]+)/g, 
                        '<span class="inline-block px-1 py-0.5 bg-blue-100 text-blue-800 rounded text-xs font-mono border">$1</span>'
                      )
                    }} />
                  ) : (
                    'No character prompts generated yet.'
                  )}
                </div>
              )}

              <DetailImageManager 
                storyState={storyState}
                promptType="Character"
                promptsString={storyData.detailsPrompts.characterPrompts}
              />
            </div>

            <Separator />

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Item Prompts</Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={async () => {
                    const newEditingState = !isItemPromptsEditing;
                    setIsItemPromptsEditing(newEditingState);
                    
                    // When saving (exiting edit mode), update Baserow columns
                    if (!newEditingState && storyData.id && storyData.userId && storyData.detailsPrompts) {
                      try {
                        const result = await updateStructuredEntityMappingsInBaserow(
                          storyData.id,
                          storyData.userId,
                          storyData.detailsPrompts
                        );
                        
                        if (result.success) {
                          console.log('[StoryDetailsStep] Successfully updated item mappings in Baserow');
                        } else {
                          console.error('[StoryDetailsStep] Failed to update item mappings in Baserow:', result.error);
                          toast({ 
                            title: 'Save Warning', 
                            description: 'Item mappings may not have been saved to database.', 
                            variant: 'destructive' 
                          });
                        }
                      } catch (error) {
                        console.error('[StoryDetailsStep] Error updating item mappings:', error);
                        toast({ 
                          title: 'Save Error', 
                          description: 'Failed to save item mappings to database.', 
                          variant: 'destructive' 
                        });
                      }
                    }
                  }}
                >
                  <Edit2 className="mr-1 h-3 w-3" />
                  {isItemPromptsEditing ? 'Save' : 'Edit'}
                </Button>
              </div>
              
              {isItemPromptsEditing ? (
                <Textarea
                  value={storyData.detailsPrompts.itemPrompts || ''}
                  onChange={(e) => {
                    const newDetailsPrompts = {
                      ...storyData.detailsPrompts!,
                      itemPrompts: e.target.value
                    };
                    updateStoryData({
                      detailsPrompts: newDetailsPrompts
                    });
                    
                    // Update Baserow columns when item prompts change
                    if (storyData.id && storyData.userId) {
                      // Debounce the updates to avoid too many API calls
                      const timeoutId = setTimeout(async () => {
                        try {
                          const result = await updateStructuredEntityMappingsInBaserow(
                            storyData.id!,
                            storyData.userId!,
                            newDetailsPrompts
                          );
                          
                          if (result.success) {
                            console.log('[StoryDetailsStep] Successfully updated item mappings in Baserow');
                          } else {
                            console.error('[StoryDetailsStep] Failed to update item mappings in Baserow:', result.error);
                          }
                        } catch (error) {
                          console.error('[StoryDetailsStep] Error updating item mappings:', error);
                        }
                      }, 1000); // 1 second debounce
                      
                      // Clear previous timeout if it exists
                      const existingTimeout = (window as Window & typeof globalThis & { itemUpdateTimeout?: NodeJS.Timeout }).itemUpdateTimeout;
                      if (existingTimeout) {
                        clearTimeout(existingTimeout);
                      }
                      (window as Window & typeof globalThis & { itemUpdateTimeout?: NodeJS.Timeout }).itemUpdateTimeout = timeoutId;
                    }
                  }}
                  rows={4}
                  className="text-sm"
                  placeholder="Enter item descriptions, one per paragraph..."
                />
              ) : (
                <div className="p-3 border rounded-md bg-muted/50 text-sm whitespace-pre-wrap max-h-40 overflow-y-auto">
                  {storyData.detailsPrompts.itemPrompts ? (
                    <div dangerouslySetInnerHTML={{
                      __html: storyData.detailsPrompts.itemPrompts.replace(
                        /(@[A-Za-z0-9_]+)/g, 
                        '<span class="inline-block px-1 py-0.5 bg-green-100 text-green-800 rounded text-xs font-mono border">$1</span>'
                      )
                    }} />
                  ) : (
                    'No item prompts generated yet.'
                  )}
                </div>
              )}

              <DetailImageManager 
                storyState={storyState}
                promptType="Item"
                promptsString={storyData.detailsPrompts.itemPrompts}
              />
            </div>

            <Separator />

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Location Prompts</Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={async () => {
                    const newEditingState = !isLocationPromptsEditing;
                    setIsLocationPromptsEditing(newEditingState);
                    
                    // When saving (exiting edit mode), update Baserow columns
                    if (!newEditingState && storyData.id && storyData.userId && storyData.detailsPrompts) {
                      try {
                        const result = await updateStructuredEntityMappingsInBaserow(
                          storyData.id,
                          storyData.userId,
                          storyData.detailsPrompts
                        );
                        
                        if (result.success) {
                          console.log('[StoryDetailsStep] Successfully updated location mappings in Baserow');
                        } else {
                          console.error('[StoryDetailsStep] Failed to update location mappings in Baserow:', result.error);
                          toast({ 
                            title: 'Save Warning', 
                            description: 'Location mappings may not have been saved to database.', 
                            variant: 'destructive' 
                          });
                        }
                      } catch (error) {
                        console.error('[StoryDetailsStep] Error updating location mappings:', error);
                        toast({ 
                          title: 'Save Error', 
                          description: 'Failed to save location mappings to database.', 
                          variant: 'destructive' 
                        });
                      }
                    }
                  }}
                >
                  <Edit2 className="mr-1 h-3 w-3" />
                  {isLocationPromptsEditing ? 'Save' : 'Edit'}
                </Button>
              </div>
              
              {isLocationPromptsEditing ? (
                <Textarea
                  value={storyData.detailsPrompts.locationPrompts || ''}
                  onChange={(e) => {
                    const newDetailsPrompts = {
                      ...storyData.detailsPrompts!,
                      locationPrompts: e.target.value
                    };
                    updateStoryData({
                      detailsPrompts: newDetailsPrompts
                    });
                    
                    // Update Baserow columns when location prompts change
                    if (storyData.id && storyData.userId) {
                      // Debounce the updates to avoid too many API calls
                      const timeoutId = setTimeout(async () => {
                        try {
                          const result = await updateStructuredEntityMappingsInBaserow(
                            storyData.id!,
                            storyData.userId!,
                            newDetailsPrompts
                          );
                          
                          if (result.success) {
                            console.log('[StoryDetailsStep] Successfully updated location mappings in Baserow');
                          } else {
                            console.error('[StoryDetailsStep] Failed to update location mappings in Baserow:', result.error);
                          }
                        } catch (error) {
                          console.error('[StoryDetailsStep] Error updating location mappings:', error);
                        }
                      }, 1000); // 1 second debounce
                      
                      // Clear previous timeout if it exists
                      const existingTimeout = (window as Window & typeof globalThis & { locationUpdateTimeout?: NodeJS.Timeout }).locationUpdateTimeout;
                      if (existingTimeout) {
                        clearTimeout(existingTimeout);
                      }
                      (window as Window & typeof globalThis & { locationUpdateTimeout?: NodeJS.Timeout }).locationUpdateTimeout = timeoutId;
                    }
                  }}
                  rows={4}
                  className="text-sm"
                  placeholder="Enter location descriptions, one per paragraph..."
                />
              ) : (
                <div className="p-3 border rounded-md bg-muted/50 text-sm whitespace-pre-wrap max-h-40 overflow-y-auto">
                  {storyData.detailsPrompts.locationPrompts ? (
                    <div dangerouslySetInnerHTML={{
                      __html: storyData.detailsPrompts.locationPrompts.replace(
                        /(@[A-Za-z0-9_]+)/g, 
                        '<span class="inline-block px-1 py-0.5 bg-purple-100 text-purple-800 rounded text-xs font-mono border">$1</span>'
                      )
                    }} />
                  ) : (
                    'No location prompts generated yet.'
                  )}
                </div>
              )}

              <DetailImageManager 
                storyState={storyState}
                promptType="Location"
                promptsString={storyData.detailsPrompts.locationPrompts}
              />
            </div>

            <div className="pt-4">
              <DetailImageManager 
                storyState={storyState}
                promptType="All"
                promptsString="generate-all" // This is a special value to trigger the "Generate All" button
                showGenerateAllButton={true}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
