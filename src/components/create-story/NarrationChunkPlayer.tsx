import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { ConvertibleAudioPlayer } from '@/components/ConvertibleAudioPlayer';
import { Mic, Loader2, CheckCircle, RefreshCw, Edit, Save, X } from 'lucide-react';
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { saveStory } from '@/actions/baserowStoryActions';
import type { NarrationChunk } from '@/types/narration';
import type { UseStoryStateReturn } from '@/hooks/useStoryState';

interface NarrationChunkPlayerProps {
  chunk: NarrationChunk;
  index: number;
  storyState: UseStoryStateReturn;
  onGenerateChunk: (chunkIndex: number) => void;
}

export function NarrationChunkPlayer({ 
  chunk, 
  index, 
  storyState, 
  onGenerateChunk 
}: NarrationChunkPlayerProps) {
  const { toast } = useToast();
  const {
    isLoading,
    currentNarrationChunkIndex,
    processingAllMode,
    narrationSource,
    storyData,
    updateStoryData
  } = storyState;

  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(chunk.text);
  const [isSaving, setIsSaving] = useState(false);

  const isCurrentlyProcessing = currentNarrationChunkIndex === index && isLoading.narration;
  const isCompleted = Boolean(chunk.audioUrl);
  const isDisabled = isLoading.narration || processingAllMode;

  const handleEditClick = () => {
    setIsEditing(true);
    setEditText(chunk.text);
  };

  const handleSaveEdit = async () => {
    if (!storyData.narrationChunks || !storyData.userId || !storyData.id) {
      toast({
        title: 'Error',
        description: 'Cannot save changes. Story not properly loaded.',
        variant: 'destructive'
      });
      return;
    }

    setIsSaving(true);

    try {
      const updatedChunks = [...storyData.narrationChunks];
      updatedChunks[index] = {
        ...updatedChunks[index],
        text: editText
      };

      updateStoryData({
        narrationChunks: updatedChunks
      });

      const updatedStoryData = {
        ...storyData,
        narrationChunks: updatedChunks
      };

      const saveResult = await saveStory(updatedStoryData, storyData.userId);
      if (saveResult.success) {
        setIsEditing(false);
        toast({
          title: 'Chunk Updated',
          description: 'English chunk text has been saved.',
          className: 'bg-green-500 text-white'
        });
      } else {
        toast({
          title: 'Error Saving',
          description: saveResult.error || 'Failed to save changes.',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error saving chunk edit:', error);
      toast({
        title: 'Save Error',
        description: 'An unexpected error occurred while saving.',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditText(chunk.text);
  };

  return (
    <Card className="border-l-4 border-l-primary/20">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 mt-1">
            {isCompleted ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : isCurrentlyProcessing ? (
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
            ) : (
              <div className="h-5 w-5 rounded-full border-2 border-muted-foreground" />
            )}
          </div>
          
          <div className="flex-1 space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                Chunk {index + 1}
                {chunk.duration && (
                  <span className="text-muted-foreground ml-2">
                    ({chunk.duration.toFixed(1)}s)
                  </span>
                )}
              </span>
              
              <div className="flex items-center gap-1">
                {/* Edit buttons */}
                {isEditing ? (
                  <>
                    <Button
                      size="icon"
                      variant="outline"
                      onClick={handleSaveEdit}
                      disabled={isSaving}
                      className="h-7 w-7"
                      aria-label="Save edit"
                    >
                      {isSaving ? (
                        <Loader2 className="h-3.5 w-3.5 animate-spin" />
                      ) : (
                        <Save className="h-3.5 w-3.5" />
                      )}
                    </Button>
                    <Button
                      size="icon"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isSaving}
                      className="h-7 w-7"
                      aria-label="Cancel edit"
                    >
                      <X className="h-3.5 w-3.5" />
                    </Button>
                  </>
                ) : (
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={handleEditClick}
                    disabled={isDisabled}
                    className="h-7 w-7"
                    aria-label="Edit chunk text"
                  >
                    <Edit className="h-3.5 w-3.5" />
                  </Button>
                )}

                {/* Audio generation buttons */}
                {narrationSource === 'generate' && (
                  <>
                    {!isCompleted && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onGenerateChunk(index)}
                        disabled={isDisabled}
                        className="text-xs"
                      >
                        {isCurrentlyProcessing ? (
                          <>
                            <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <Mic className="mr-1 h-3 w-3" />
                            Generate
                          </>
                        )}
                      </Button>
                    )}
                    {isCompleted && (
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => onGenerateChunk(index)}
                        disabled={isDisabled}
                        className="h-7 w-7"
                        aria-label={isCurrentlyProcessing ? "Regenerating audio chunk" : "Regenerate audio chunk"}
                      >
                        {isCurrentlyProcessing ? (
                          <Loader2 className="h-3.5 w-3.5 animate-spin" />
                        ) : (
                          <RefreshCw className="h-3.5 w-3.5" />
                        )}
                      </Button>
                    )}
                  </>
                )}
              </div>
            </div>
            
            {isEditing ? (
              <Textarea
                value={editText}
                onChange={(e) => setEditText(e.target.value)}
                className="min-h-[60px] text-sm"
                placeholder="Edit chunk text..."
                disabled={isSaving}
              />
            ) : (
              <p className="text-xs text-muted-foreground line-clamp-3">
                {chunk.text}
              </p>
            )}
            
            {chunk.audioUrl && (
              <div className="mt-2">
                <ConvertibleAudioPlayer
                  src={chunk.audioUrl}
                  className="w-full"
                  chunkId={chunk.id}
                />
              </div>
            )}
            
            {isCurrentlyProcessing && (
              <div className="mt-2 text-xs text-primary">
                Generating audio for this chunk...
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
