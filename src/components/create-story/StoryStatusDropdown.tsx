import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { saveStory } from '@/actions/baserowStoryActions';
import type { UseStoryStateReturn } from '@/hooks/useStoryState';
import { useState } from 'react';

interface StoryStatusDropdownProps {
  storyState: UseStoryStateReturn;
}

export function StoryStatusDropdown({ storyState }: StoryStatusDropdownProps) {
  const { storyData, updateStoryData } = storyState;
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);

  const handleStatusChange = async (value: 'wip' | 'completed' | 'published') => {
    // Update the story state immediately for UI feedback
    updateStoryData({ workflowStatus: value });
    
    // Auto-save to Baserow if story has ID and user ID
    if (storyData.id && storyData.userId) {
      setIsSaving(true);
      try {
        const updatedStoryData = { 
          ...storyData, 
          workflowStatus: value 
        };
        
        const result = await saveStory(updatedStoryData, storyData.userId);
        
        if (result.success) {
          toast({
            title: 'Status Updated!',
            description: 'Story status has been saved.',
            className: 'bg-green-500 text-white'
          });
        } else {
          toast({
            title: 'Save Failed',
            description: result.error || 'Failed to save status.',
            variant: 'destructive'
          });
        }
      } catch (error) {
        console.error('Error saving status:', error);
        toast({
          title: 'Save Error',
          description: 'An unexpected error occurred while saving status.',
          variant: 'destructive'
        });
      } finally {
        setIsSaving(false);
      }
    }
  };

  const status = storyData.workflowStatus || 'wip';

  return (
    <div className="space-y-2">
      <Label htmlFor="story-status">Story Status</Label>
      <Select value={status} onValueChange={handleStatusChange} disabled={isSaving}>
        <SelectTrigger id="story-status" className="w-[180px]">
          <SelectValue placeholder="Select status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="wip">Work in Progress</SelectItem>
          <SelectItem value="completed">Completed</SelectItem>
          <SelectItem value="published">Published</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}