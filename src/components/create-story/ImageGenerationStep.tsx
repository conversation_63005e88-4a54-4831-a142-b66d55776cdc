
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ImagePopup } from '@/components/ui/image-popup';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Clapperboard, Loader2, Edit2, ImageIcon, RefreshCw, Download, <PERSON>tings, ListMusic, ChevronsRight, Upload, Video } from 'lucide-react';
import Link from 'next/link';
import { generateImageFromPrompt, generateVideoFromImage } from '@/actions/storyActions';
import { saveStory } from '@/actions/baserowStoryActions';
import { uploadImageToMinIOStorage } from '@/actions/minioStorageActions';
import { useToast } from '@/hooks/use-toast';
import { useStreamingApi } from '@/hooks/useStreamingApi';
import { countSceneImages } from '@/utils/storyHelpers';
import Image from 'next/image';
import { useState, useRef, useEffect } from 'react';
import type { UseStoryStateReturn } from '@/hooks/useStoryState';
import type { GeneratedImage, ActionPrompt } from '@/types/story';
import type { StreamingApiResponse } from '@/types/streamingApi';
import { IMAGE_STYLES, DEFAULT_STYLE_ID, type ImageStyleId } from '@/types/imageStyles';

// Hook to transform action descriptions from placeholders to descriptive terms
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function useTransformedActionDescription(actionDescription: string | undefined, storyData: any) {
    const [transformedAction, setTransformedAction] = useState<string>('No action defined');

    useEffect(() => {
        if (!actionDescription) {
            setTransformedAction('No action defined');
            return;
        }

        // Transform the action description asynchronously
        const transformAction = async () => {
            try {
                // Check if action prompt contains placeholders (@) - if so, transform them
                // If not, use the action prompt as-is (new format with descriptive terms)
                if (actionDescription && actionDescription.includes('@')) {
                    // This is an old action prompt with placeholders, transform it
                    const { transformActionPromptWithStoryData } = await import('@/actions/utils/storyHelpers');
                    const transformed = await transformActionPromptWithStoryData(actionDescription, storyData);
                    setTransformedAction(transformed);
                } else {
                    // This is a new action prompt with descriptive terms, use as-is
                    setTransformedAction(actionDescription || '');
                }
            } catch (error) {
                console.error('Error transforming action description:', error);
                setTransformedAction(actionDescription || ''); // Fallback to original
            }
        };

        transformAction();
    }, [actionDescription, storyData]);

    return transformedAction;
}

// Component to display transformed action description
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const ActionDisplay = ({ actionDescription, storyData }: { actionDescription: string | undefined; storyData: any }) => {
    const transformedAction = useTransformedActionDescription(actionDescription, storyData);
    return (
        <p className="text-xs leading-relaxed break-words whitespace-pre-wrap">
            <span className="font-medium text-foreground/80">Action:</span> {transformedAction}
        </p>
    );
};

const HighlightedPrompt = ({ prompt }: { prompt: string | undefined }) => {
    if (!prompt) {
        return <span>No prompt available</span>;
    }

    const parts = prompt.split(/(@[\w.-]+)/g); // Allow dots in entity names
    return (
        <span>
            {parts.map((part, index) => {
                if (part.startsWith('@')) {
                    return (
                        <span key={index} className="font-semibold text-primary bg-primary/10 px-1 rounded mr-1">
                            {part}
                        </span>
                    );
                }
                return part;
            })}
        </span>
    );
};



interface ImageGenerationStepProps {
    storyState: UseStoryStateReturn;
}

export function ImageGenerationStep({ storyState }: ImageGenerationStepProps) {
    const { toast } = useToast();
    const streamingApi = useStreamingApi<StreamingApiResponse>({
        onMessage: (data) => {
            if (data && typeof data === 'object' && 'type' in data && data.type === 'success' && 'data' in data && data.data && 'imagePrompts' in data.data) {
                handleImagePromptsSuccess(data.data);
            }
        },
        onError: (error) => {
            console.error('Image prompts streaming error:', error);
            setPromptProgress(null);
            toast({
                title: 'API Error',
                description: error instanceof Error ? error.message : 'Failed to call image prompt generation API.',
                variant: 'destructive'
            });
            handleSetLoading('imagePrompts', false);
            setIsRegenerateDialogOpen(false);
        }
    });
    
    const [popupImage, setPopupImage] = useState<{ src: string; alt: string } | null>(null);
    const [isRegenerateDialogOpen, setIsRegenerateDialogOpen] = useState(false);
    const [uploadingSceneIndex, setUploadingSceneIndex] = useState<number | null>(null);
    const [generatingVideoIndex, setGeneratingVideoIndex] = useState<number | null>(null);
    const [promptProgress, setPromptProgress] = useState<{ completed: number; total: number; failed?: boolean; error?: string } | null>(null);
    const [isImagePromptEditing, setIsImagePromptEditing] = useState<boolean[]>([]);
    const [imageGenerationProgress, setImageGenerationProgress] = useState<{ total: number; completed: number; generating: number[] }>({ total: 0, completed: 0, generating: [] });

    const {
        storyData,
        updateStoryData,
        setStoryData,
        isLoading,
        handleSetLoading,
        imageProvider,
        setImageProviderWithPersist,
        userApiKeys,
        apiKeysLoading,
        aiProvider, // Added
        perplexityModel, // Added
        googleScriptModel // Added
    } = storyState;

    // Load progress from story data on mount
    useEffect(() => {
        if (storyData.imagePromptsProgress) {
            setPromptProgress({
                completed: storyData.imagePromptsProgress.completed,
                total: storyData.imagePromptsProgress.total,
                failed: storyData.imagePromptsProgress.failed,
                error: storyData.imagePromptsProgress.error
            });
        }
    }, [storyData.imagePromptsProgress]);

    const googleKeyMissing = !apiKeysLoading && !userApiKeys?.googleApiKey;
    const picsartKeyMissing = imageProvider === 'picsart' && !apiKeysLoading && !userApiKeys?.picsartApiKey;
    const geminiKeyMissing = imageProvider === 'gemini' && !apiKeysLoading && !userApiKeys?.googleApiKey;
    const imagen3KeyMissing = imageProvider === 'imagen3' && !apiKeysLoading && !userApiKeys?.googleApiKey;
    const imagen4KeyMissing = imageProvider === 'imagen4' && !apiKeysLoading && !userApiKeys?.googleApiKey;
    const imagen4UltraKeyMissing = imageProvider === 'imagen4ultra' && !apiKeysLoading && !userApiKeys?.googleApiKey;

    const handleImagePromptsSuccess = async (data: { imagePrompts?: string[]; actionPrompts?: string[] }) => {
        const currentImagePrompts = data.imagePrompts || [];
        const currentActionDescriptions = data.actionPrompts || [];
        const calledAiForPrompts = true;

        setPromptProgress(null); // Clear progress on complete success
        // Clear progress from story data on complete success
        if (storyData.imagePromptsProgress) {
            updateStoryData({ imagePromptsProgress: undefined });
        }

        if (currentImagePrompts.length > 0) {
            const newActionPrompts = (() => {
                const actionPrompts: ActionPrompt[] = [];
                let promptIndex = 0;
                if (storyData.narrationChunks && storyData.narrationChunks.length > 0) {
                    storyData.narrationChunks.forEach((chunk, chunkIndex) => {
                        const duration = chunk.duration || 0;
                        let promptCount: number;
                        if (duration <= 5) promptCount = 1;
                        else if (duration <= 10) promptCount = chunk.text.length > 100 ? 2 : 1;
                        else if (duration <= 15) promptCount = 2;
                        else promptCount = 3;

                        for (let i = 0; i < promptCount && promptIndex < currentImagePrompts.length; i++) {
                            actionPrompts.push({
                                sceneIndex: promptIndex,
                                originalPrompt: currentImagePrompts[promptIndex],
                                actionDescription: currentActionDescriptions[promptIndex] || `Character performs action in scene ${promptIndex + 1}.`,
                                chunkText: chunk.text,
                                chunkId: chunk.id,
                                chunkIndex: chunkIndex
                            });
                            promptIndex++;
                        }
                    });
                } else {
                    currentImagePrompts.forEach((promptText, index) => {
                        actionPrompts.push({
                            sceneIndex: index,
                            originalPrompt: promptText,
                            actionDescription: currentActionDescriptions[index] || `Character performs action in scene ${index + 1}.`,
                            chunkText: "Script chunk not available"
                        });
                    });
                }
                return actionPrompts;
            })();

            console.log('--- ImageGenerationStep: Generated actionPrompts ---');

            let updatedGeneratedImages = [...(storyData.generatedImages || [])];

            if (calledAiForPrompts) {
                updatedGeneratedImages = updatedGeneratedImages.filter(img => {
                    // Keep images not tied to a sceneIndex (detail images)
                    if (img.sceneIndex === undefined) return true;
                    // Keep scene images only if their sceneIndex still exists in the new prompts
                    return img.sceneIndex < currentImagePrompts.length;
                });
                console.log('Regeneration with AI: Cleared/filtered images. Remaining images:', updatedGeneratedImages.length);
            }

            if (newActionPrompts.length > 0) {
                updatedGeneratedImages = updatedGeneratedImages.map(image => {
                    const matchingActionPrompt = newActionPrompts.find(ap => ap.sceneIndex === image.sceneIndex);
                    if (matchingActionPrompt && (image.chunkId === undefined || image.chunkIndex === undefined || image.originalPrompt !== matchingActionPrompt.originalPrompt)) {
                        return {
                            ...image,
                            originalPrompt: matchingActionPrompt.originalPrompt, // Ensure image's originalPrompt matches canonical prompt
                            chunkId: matchingActionPrompt.chunkId,
                            chunkIndex: matchingActionPrompt.chunkIndex,
                        };
                    }
                    return image;
                });
                console.log('Attempted backfill of chunk info and originalPrompt for existing images using newActionPrompts.');
            }

            const storyUpdate: Partial<typeof storyData> = {
                imagePrompts: currentImagePrompts,
                generatedImages: updatedGeneratedImages,
                actionPrompts: newActionPrompts
            };

            updateStoryData(storyUpdate);
            setIsImagePromptEditing(Array(currentImagePrompts.length).fill(false));

            // Immediate save after image prompts generation (bypass potential timeout issues)
            if (storyData.id && storyData.userId) {
                try {
                    console.log('[ImageGeneration] Starting final save of image prompts...');
                    
                    const finalStoryDataToSave = {
                        ...storyData,
                        imagePrompts: currentImagePrompts,
                        generatedImages: updatedGeneratedImages,
                        actionPrompts: newActionPrompts,
                    };
                    const saveResult = await saveStory(finalStoryDataToSave, storyData.userId);
                    
                    if (!saveResult.success) {
                        console.error('Failed to save image prompts:', saveResult.error);
                        toast({
                            title: 'Prompts Generated but Save Failed',
                            description: `Image prompts generated successfully but failed to save: ${saveResult.error}. Please try saving manually.`,
                            variant: 'destructive'
                        });
                    } else {
                        console.log('Image prompts successfully saved to database');
                    }
                } catch (error) {
                    console.error('Failed to save story after image prompts generation:', error);
                    toast({
                        title: 'Prompts Generated but Save Failed',
                        description: 'Image prompts generated successfully but failed to save to database. Please try saving manually.',
                        variant: 'destructive'
                    });
                }
            }

            toast({
                title: 'Image Prompts Generated!',
                description: `${currentImagePrompts.length} scene prompts processed. Chunk associations updated.`,
                className: 'bg-primary text-primary-foreground'
            });
        } else {
            toast({
                title: 'Error Processing Prompts',
                description: 'Could not obtain or process image prompts.',
                variant: 'destructive'
            });
        }
        
        handleSetLoading('imagePrompts', false);
        setIsRegenerateDialogOpen(false);
    };

    const handleRegenerateImagePrompts = async () => {
        await generatePromptsWithOptions(true);
        setIsRegenerateDialogOpen(false);
    };

    const generatePromptsWithOptions = async (isRegeneration = false) => {
        if (googleKeyMissing) {
            toast({ title: 'API Key Required', description: 'Please configure your Google API Key in Account Settings for prompt generation.', variant: 'destructive' });
            return;
        }
        if (!storyData.generatedScript || !storyData.detailsPrompts) return;
        handleSetLoading('imagePrompts', true);
        
        console.log('=== GENERATING IMAGE PROMPTS ===');
        const currentImagePrompts = storyData.imagePrompts || [];
        let currentActionDescriptions = storyData.actionPrompts?.map(ap => ap.actionDescription) || [];
        if (isRegeneration || !storyData.imagePrompts || storyData.imagePrompts.length === 0) {
            console.log('Calling API to generate new image prompts and action descriptions with streaming...');
            setPromptProgress(null); // Reset progress
            
            try {
                // Call the new API route for image prompt generation with streaming
                await streamingApi.callStreamingApi('/api/image-prompts', {
                    storyId: storyData.id,
                    userId: storyData.userId,
                    imageProvider: imageProvider,
                    aiProvider: aiProvider,
                    perplexityModel: perplexityModel,
                    googleScriptModel: googleScriptModel,
                    stream: true // Enable streaming
                });
            } catch (error) {
                console.error('Error calling image prompt API:', error);
                setPromptProgress(null);
                
                toast({
                    title: 'API Error',
                    description: error instanceof Error ? error.message : 'Failed to call image prompt generation API.',
                    variant: 'destructive'
                });
                handleSetLoading('imagePrompts', false);
                setIsRegenerateDialogOpen(false);
                return;
            }
        } else {
            console.log('Using existing image prompts for re-association / action prompt generation. AI will not be called for new prompts.');
            if (currentActionDescriptions.length !== currentImagePrompts.length) {
                currentActionDescriptions = currentImagePrompts.map((_, idx) => storyData.actionPrompts?.[idx]?.actionDescription || `Character performs action in scene ${idx + 1}.`);
            }
            
            // Handle the case where we're not regenerating but just updating associations
            await handleImagePromptsSuccess({
                imagePrompts: currentImagePrompts,
                actionPrompts: currentActionDescriptions
            });
        }
    };

    const handleGenerateImagePrompts = () => generatePromptsWithOptions(false);

    const handleRetryImagePrompts = async () => {
        if (googleKeyMissing) {
            toast({ title: 'API Key Required', description: 'Please configure your Google API Key in Account Settings for prompt generation.', variant: 'destructive' });
            return;
        }
        if (!storyData.generatedScript || !storyData.detailsPrompts) return;
        
        handleSetLoading('imagePrompts', true);

        console.log('=== RETRYING IMAGE PROMPTS GENERATION WITH STREAMING ===');
        
        try {
            // Call the new API route for image prompt generation with streaming
            await streamingApi.callStreamingApi('/api/image-prompts', {
                storyId: storyData.id,
                userId: storyData.userId,
                imageProvider: imageProvider,
                aiProvider: aiProvider,
                perplexityModel: perplexityModel,
                googleScriptModel: googleScriptModel,
                stream: true // Enable streaming
            });
        } catch (error) {
            console.error('Error calling image prompt API:', error);
            
            // Provide helpful error messages based on error type
            let errorTitle = 'Retry Failed';
            let errorDescription = error instanceof Error ? error.message : 'Failed to retry prompt generation.';
            
            if (error instanceof Error && error.message?.includes('timeout')) {
                errorTitle = 'Request Timeout';
                errorDescription = 'The reasoning model still needs more time. Try reducing the number of narration chunks in your story.';
            }
            
            toast({
                title: errorTitle,
                description: errorDescription,
                variant: 'destructive'
            });
        }
        
        handleSetLoading('imagePrompts', false);
    };


    const handleGenerateIndividualImage = async (promptTextForGeneration: string, sceneIdx: number) => {
        if (googleKeyMissing || (imageProvider === 'picsart' && picsartKeyMissing) || (imageProvider === 'gemini' && geminiKeyMissing) || (imageProvider === 'imagen3' && imagen3KeyMissing) || (imageProvider === 'imagen4' && imagen4KeyMissing) || (imageProvider === 'imagen4ultra' && imagen4UltraKeyMissing)) {
            toast({ title: 'API Key Required', description: `Please configure your ${imageProvider === 'picsart' ? 'Picsart' : imageProvider === 'gemini' ? 'Gemini' : 'Google'} API Key in Account Settings.`, variant: 'destructive' });
            return;
        }
        if (!storyData.imagePrompts || !storyData.imagePrompts[sceneIdx]) {
            toast({ title: 'Error', description: `Scene prompt at index ${sceneIdx} not found.`, variant: 'destructive' });
            return;
        }

        const loadingKey = `image-${sceneIdx}`;
        handleSetLoading(loadingKey, true);

        const currentProgress = imageGenerationProgress;
        setImageGenerationProgress({
            total: currentProgress.total,
            completed: currentProgress.completed,
            generating: [...currentProgress.generating, sceneIdx]
        });

        toast({
            title: 'Generating Scene Image...',
            description: `Prompt: "${promptTextForGeneration.substring(0, 50)}..."`
        });

        const styleId = storyData.imageStyleId || DEFAULT_STYLE_ID;
        const result = await generateImageFromPrompt(promptTextForGeneration, storyData.userId, storyData.id, imageProvider, styleId);

        if (result.success && result.imageUrl && result.requestPrompt) {
            const actionPrompt = storyData.actionPrompts?.find((ap: ActionPrompt) => ap.sceneIndex === sceneIdx);

            const newImage: GeneratedImage = {
                sceneIndex: sceneIdx,
                originalPrompt: promptTextForGeneration,
                requestPrompt: result.requestPrompt,
                imageUrl: result.imageUrl,
                width: result.width,
                height: result.height,
                chunkId: actionPrompt?.chunkId,
                chunkIndex: actionPrompt?.chunkIndex,
            };

            const updatedGeneratedImages = (storyData.generatedImages || []).filter(img => img.sceneIndex !== sceneIdx);
            updatedGeneratedImages.push(newImage);

            // Update enhanced prompt data
            const updatedImagePromptsData = [...(storyData.imagePromptsData || [])];

            // Ensure array is large enough
            while (updatedImagePromptsData.length <= sceneIdx) {
                updatedImagePromptsData.push({ originalPrompt: '' });
            }

            // Initialize if not exists
            if (!updatedImagePromptsData[sceneIdx]) {
                updatedImagePromptsData[sceneIdx] = { originalPrompt: promptTextForGeneration };
            }

            // Update the original prompt if not set
            if (!updatedImagePromptsData[sceneIdx].originalPrompt) {
                updatedImagePromptsData[sceneIdx].originalPrompt = promptTextForGeneration;
            }

            // Save the expanded prompt based on provider
            if (result.expandedPrompt) {
                if (storyData.imageProvider === 'picsart') {
                    updatedImagePromptsData[sceneIdx].picsartPrompt = result.expandedPrompt;
                } else if (storyData.imageProvider === 'imagen3') {
                    updatedImagePromptsData[sceneIdx].imagenPrompt = result.expandedPrompt;
                } else if (storyData.imageProvider === 'imagen4') {
                    updatedImagePromptsData[sceneIdx].imagen4Prompt = result.expandedPrompt;
                }
            }

            // Determine model based on provider
            let sceneImageModel = '';
            if (imageProvider === 'picsart') {
                sceneImageModel = 'Picsart AI Image Generator';
            } else if (imageProvider === 'imagen3') {
                sceneImageModel = 'Google Imagen 3';
            } else if (imageProvider === 'imagen4') {
                sceneImageModel = 'Google Imagen 4';
            }

            const updatedStoryData = {
                ...storyData,
                generatedImages: updatedGeneratedImages,
                imagePromptsData: updatedImagePromptsData,
                // Track the provider and model used for scene images
                sceneImageProvider: imageProvider,
                sceneImageModel: sceneImageModel
            };

            setStoryData(updatedStoryData);

            if (storyData.id && storyData.userId) {
                try {
                    await saveStory(updatedStoryData, storyData.userId);
                } catch (error) {
                    console.error('Failed to auto-save story:', error);
                }
            }

            const latestProgress = imageGenerationProgress;
            setImageGenerationProgress({
                total: latestProgress.total,
                completed: latestProgress.completed + 1,
                generating: latestProgress.generating.filter((i) => i !== sceneIdx)
            });

            toast({
                title: 'Scene Image Generated!',
                description: `Image for scene ${sceneIdx + 1} is ready.`,
                className: 'bg-green-500 text-white'
            });
        } else {
            const latestProgress = imageGenerationProgress;
            setImageGenerationProgress({
                total: latestProgress.total,
                completed: latestProgress.completed,
                generating: latestProgress.generating.filter((i) => i !== sceneIdx)
            });

            toast({
                title: 'Image Generation Error',
                description: result.error || `Failed to generate image for scene ${sceneIdx + 1}.`,
                variant: 'destructive'
            });
        }

        handleSetLoading(loadingKey, false);
    };

    const isGenerationStoppedRef = useRef(false);

    const handleGenerateAllImages = async () => {
        if (googleKeyMissing || (imageProvider === 'picsart' && picsartKeyMissing) || (imageProvider === 'gemini' && geminiKeyMissing) || (imageProvider === 'imagen3' && imagen3KeyMissing) || (imageProvider === 'imagen4' && imagen4KeyMissing) || (imageProvider === 'imagen4ultra' && imagen4UltraKeyMissing)) {
            toast({ title: 'API Key Required', description: `Please configure your ${imageProvider === 'picsart' ? 'Picsart' : imageProvider === 'gemini' ? 'Gemini' : 'Google'} API Key in Account Settings.`, variant: 'destructive' });
            return;
        }
        if (!storyData.imagePrompts || storyData.imagePrompts.length === 0 || !storyData.actionPrompts) {
            toast({ title: 'Prerequisites Missing', description: 'Ensure image prompts and action prompts are generated first.', variant: 'destructive' });
            return;
        }

        isGenerationStoppedRef.current = false;
        handleSetLoading('allImages', true);

        const imagesToActuallyGenerate = storyData.actionPrompts.filter(actionPrompt =>
            !storyData.generatedImages?.find(img => img.sceneIndex === actionPrompt.sceneIndex)
        );

        const alreadyGeneratedCount = (storyData.actionPrompts?.length || 0) - imagesToActuallyGenerate.length;

        setImageGenerationProgress({
            total: storyData.actionPrompts?.length || 0,
            completed: alreadyGeneratedCount,
            generating: []
        });

        toast({
            title: 'Generating All Scene Images...',
            description: `${alreadyGeneratedCount > 0 ? `Resuming, ${alreadyGeneratedCount} already generated. ` : ''}This may take several minutes.`
        });

        let successCount = 0;
        let errorCount = 0;
        const currentStoryData = { ...storyData };
        const newImagesThisBatch: GeneratedImage[] = [];

        for (let i = 0; i < (storyData.actionPrompts?.length || 0); i++) {
            if (isGenerationStoppedRef.current) {
                toast({
                    title: 'Generation Stopped',
                    description: `Stopped after generating ${successCount} new images. Progress saved.`,
                    className: 'bg-yellow-500 text-black'
                });
                break;
            }

            const actionPrompt = storyData.actionPrompts![i];
            const sceneIdx = actionPrompt.sceneIndex;
            const promptTextForGeneration = actionPrompt.originalPrompt;

            if (currentStoryData.generatedImages?.find(img => img.sceneIndex === sceneIdx)) {
                continue;
            }

            const currentProgressForLoop = imageGenerationProgress;
            setImageGenerationProgress({
                total: currentProgressForLoop.total || 0,
                completed: (currentStoryData.generatedImages?.filter(img => img.sceneIndex !== undefined).length || 0) + successCount,
                generating: [sceneIdx]
            });

            const styleId = storyData.imageStyleId || DEFAULT_STYLE_ID;
            const result = await generateImageFromPrompt(promptTextForGeneration, storyData.userId, storyData.id, imageProvider, styleId);

            if (result.success && result.imageUrl && result.requestPrompt) {
                const newImage: GeneratedImage = {
                    sceneIndex: sceneIdx,
                    originalPrompt: promptTextForGeneration,
                    requestPrompt: result.requestPrompt,
                    imageUrl: result.imageUrl,
                    width: result.width,
                    height: result.height,
                    chunkId: actionPrompt.chunkId,
                    chunkIndex: actionPrompt.chunkIndex,
                };
                newImagesThisBatch.push(newImage);
                successCount++;
            } else {
                errorCount++;
                console.warn(`Image generation failed for scene ${sceneIdx + 1}:`, result.error);
                toast({
                    title: `Scene ${sceneIdx + 1} Generation Failed`,
                    description: result.error || 'Unknown error occurred',
                    variant: 'destructive'
                });

                const errorMessage = result.error?.toLowerCase() || '';
                const criticalErrors = ["api key", "quota", "billing", "permission"];
                const isCriticalError = criticalErrors.some(error => errorMessage.includes(error));

                if (isCriticalError) {
                    console.warn('Critical error detected, stopping generation:', result.error);
                    toast({
                        title: 'Generation Stopped - Critical Error',
                        description: `Stopped due to: ${result.error}. Generated ${successCount} images before stopping.`,
                        variant: 'destructive'
                    });
                    isGenerationStoppedRef.current = true;
                }
            }

            const latestProgressLoop = imageGenerationProgress;
            setImageGenerationProgress({
                total: latestProgressLoop.total || 0,
                completed: alreadyGeneratedCount + successCount,
                generating: []
            });
        }

        if (newImagesThisBatch.length > 0) {
            const existingImagesNotReplaced = (currentStoryData.generatedImages || []).filter(
                existingImg => !newImagesThisBatch.some(newImg => newImg.sceneIndex === existingImg.sceneIndex)
            );
            const finalGeneratedImages = [...existingImagesNotReplaced, ...newImagesThisBatch];

            // Determine model based on provider
            let sceneImageModel = '';
            if (imageProvider === 'picsart') {
                sceneImageModel = 'Picsart AI Image Generator';
            } else if (imageProvider === 'imagen3') {
                sceneImageModel = 'Google Imagen 3';
            } else if (imageProvider === 'imagen4') {
                sceneImageModel = 'Google Imagen 4';
            }

            const updatedStoryData = {
                ...currentStoryData,
                generatedImages: finalGeneratedImages,
                // Track the provider and model used for scene images
                sceneImageProvider: imageProvider,
                sceneImageModel: sceneImageModel
            };

            setStoryData(updatedStoryData);

            if (storyData.id && storyData.userId) {
                try {
                    await saveStory(updatedStoryData, storyData.userId);
                } catch (error) {
                    console.error('Failed to auto-save story after batch image generation:', error);
                }
            }
        }

        if (!isGenerationStoppedRef.current) {
            if (successCount > 0) {
                toast({
                    title: 'Scene Images Generated!',
                    description: `${successCount} images created. ${errorCount > 0 ? `${errorCount} errors.` : ''}`,
                    className: errorCount === 0 ? 'bg-green-500 text-white' : 'bg-yellow-500 text-black'
                });
            } else if (alreadyGeneratedCount === (storyData.actionPrompts?.length || 0) && (storyData.actionPrompts?.length || 0) > 0) {
                toast({
                    title: 'All Images Already Generated!',
                    description: 'No new images needed.',
                    className: 'bg-blue-500 text-white'
                });
            } else if (errorCount > 0 && (storyData.actionPrompts?.length || 0) > 0) {
                toast({
                    title: 'Image Generation Complete with Errors',
                    description: `Finished. ${successCount} images generated, ${errorCount} errors.`,
                    variant: errorCount === imagesToActuallyGenerate.length ? 'destructive' : 'default'
                });
            } else if ((storyData.actionPrompts?.length || 0) === 0) {
                toast({ title: 'No Prompts', description: 'No image prompts available to generate images for.', variant: 'default' });
            }
        }
        handleSetLoading('allImages', false);
    };


    const handleStopGeneration = () => {
        isGenerationStoppedRef.current = true;
        handleSetLoading('allImages', false);
        const currentProgress = imageGenerationProgress;
        setImageGenerationProgress({
            total: currentProgress.total,
            completed: currentProgress.completed,
            generating: []
        });
    };

    const updateImagePromptText = async (sceneIndex: number, newPromptText: string) => {
        if (!storyData.imagePrompts) return;
        const updatedImagePrompts = [...storyData.imagePrompts];
        updatedImagePrompts[sceneIndex] = newPromptText;

        const updatedActionPrompts = (storyData.actionPrompts || []).map(ap =>
            ap.sceneIndex === sceneIndex ? { ...ap, originalPrompt: newPromptText } : ap
        );

        updateStoryData({ imagePrompts: updatedImagePrompts, actionPrompts: updatedActionPrompts });
        
        // Auto-save image prompt changes immediately (bypass debounce for production reliability)
        if (storyData.id && storyData.userId) {
            try {
                const updatedStoryData = {
                    ...storyData,
                    imagePrompts: updatedImagePrompts,
                    actionPrompts: updatedActionPrompts
                };
                const saveResult = await saveStory(updatedStoryData, storyData.userId);
                
                if (!saveResult.success) {
                    console.error('Failed to save image prompt edit:', saveResult.error);
                    toast({
                        title: 'Failed to Save Image Prompt',
                        description: `Your prompt edit failed to save: ${saveResult.error}. Please try again.`,
                        variant: 'destructive'
                    });
                } else {
                    console.log(`Successfully saved image prompt edit for scene ${sceneIndex + 1}`);
                }
            } catch (error) {
                console.error('Error saving image prompt edit:', error);
                toast({
                    title: 'Failed to Save Image Prompt',
                    description: 'Your prompt edit failed to save. Please try again.',
                    variant: 'destructive'
                });
            }
        }
    };

    const toggleImagePromptEditing = (index: number) => {
        const newState = [...isImagePromptEditing];
        newState[index] = !newState[index];
        setIsImagePromptEditing(newState);
    };

    const handleDownloadImage = async (imageUrl: string, alt: string) => {
        try {
            const response = await fetch(imageUrl);
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${alt.replace(/\s+/g, '_')}.jpg`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            toast({
                title: 'Image Downloaded',
                description: 'The image has been saved to your device.',
                className: 'bg-green-500 text-white'
            });
        } catch (error) {
            console.error('Error downloading image:', error);
            toast({
                title: 'Download Failed',
                description: 'Failed to download the image.',
                variant: 'destructive'
            });
        }
    };

    const handleDownloadScenePrompts = async () => {
        try {
            // Create content exactly like the scene_image_prompts_imagen4.txt file from general download
            const imagePrompts = storyData.imagePrompts || [];
            let imagen4PromptsText = '=== SCENE IMAGE PROMPTS - IMAGEN 4/ULTRA ===\n\n';
            
            for (let index = 0; index < imagePrompts.length; index++) {
                const prompt = imagePrompts[index];
                
                // Find narration chunk number for this scene
                const relatedActionPrompt = storyData.actionPrompts?.find(ap => ap.sceneIndex === index);
                const chunkNumber = relatedActionPrompt?.chunkIndex !== undefined ? relatedActionPrompt.chunkIndex + 1 : 'N/A';
                
                imagen4PromptsText += `Scene ${index + 1}:\n`;
                imagen4PromptsText += `Narration Chunk ${chunkNumber}\n`;
                imagen4PromptsText += `Original Prompt: ${prompt}\n`;
                imagen4PromptsText += `********************\n`;
                
                // Generate the complete Imagen 4/Ultra prompt exactly like in the general download
                try {
                    // Import the exported function from downloadStoryUtils
                    const { generateFullImagenPromptForExport } = await import('@/utils/downloadStoryUtils');
                    
                    // Get style for Imagen 4 exactly like in the general download
                    let imagen4Style = '';
                    try {
                        const { getStylePromptForProvider } = await import('@/utils/imageStyleUtils');
                        if (storyData.imageStyleId && storyData.imageStyleId !== 'undefined' && storyData.imageStyleId !== 'null') {
                            imagen4Style = getStylePromptForProvider(storyData.imageStyleId, 'imagen4');
                        } else {
                            imagen4Style = getStylePromptForProvider(undefined, 'imagen4');
                        }
                    } catch (styleError) {
                        console.warn('Failed to get style prompt for Imagen4:', styleError);
                    }
                    
                    // Generate the complete structured prompt exactly like in the general download
                    const imagen4Expanded = await generateFullImagenPromptForExport(prompt, storyData, imagen4Style);
                    imagen4PromptsText += `Imagen 4/Ultra Expanded Prompt: \n${imagen4Expanded}\n`;
                } catch (error) {
                    console.warn('Failed to generate expanded prompts for export:', error);
                    imagen4PromptsText += `Note: Could not generate expanded prompt. Original prompt preserved above.\n`;
                }
                
                imagen4PromptsText += '\n';
            }
            
            const blob = new Blob([imagen4PromptsText], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'scene_prompts_imagen4.txt';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            
            toast({
                title: 'Scene Prompts Downloaded',
                description: 'Imagen 4/Ultra expanded scene prompts have been saved to your device.',
                className: 'bg-green-500 text-white'
            });
        } catch (error) {
            console.error('Error downloading scene prompts:', error);
            toast({
                title: 'Download Failed',
                description: 'Failed to download the scene prompts.',
                variant: 'destructive'
            });
        }
    };

    const handleDownloadActionPrompts = async () => {
        try {
            const actionPrompts = storyData.actionPrompts || [];
            
            // Sort action prompts by sceneIndex to ensure correct order
            const sortedActionPrompts = [...actionPrompts].sort((a, b) => (a.sceneIndex || 0) - (b.sceneIndex || 0));
            
            // Create the "action prompt only" format exactly like in the general download (action_prompt_only.txt)
            let actionPromptsOnlyText = '=== ACTION PROMPTS  ONLY===\n\n';
            
            for (const prompt of sortedActionPrompts) {
                const sceneNumber = prompt.sceneIndex !== undefined ? prompt.sceneIndex + 1 : 'N/A';
                
                // Check if action prompt contains placeholders (@) - if so, transform them
                // If not, use the action prompt as-is (new format with descriptive terms)
                let actionText = prompt.actionDescription;
                if (prompt.actionDescription && prompt.actionDescription.includes('@')) {
                    // This is an old action prompt with placeholders, transform it
                    try {
                        const { transformActionPromptWithStoryData } = await import('@/actions/utils/storyHelpers');
                        actionText = await transformActionPromptWithStoryData(prompt.actionDescription, storyData);
                    } catch (error) {
                        console.warn('Error transforming action prompt for download:', error);
                        // Fallback to original action description
                    }
                }
                
                actionPromptsOnlyText += `Scene ${sceneNumber}:\\n${actionText}\\n\\n`;
            }
            
            const blob = new Blob([actionPromptsOnlyText], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'action_prompt_only.txt';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            
            toast({
                title: 'Action Prompts Downloaded',
                description: 'Action prompts only have been saved to your device.',
                className: 'bg-green-500 text-white'
            });
        } catch (error) {
            console.error('Error downloading action prompts:', error);
            toast({
                title: 'Download Failed',
                description: 'Failed to download the action prompts.',
                variant: 'destructive'
            });
        }
    };

    const handleUploadImage = async (file: File, sceneIndex: number) => {
        if (!file) return;

        // Validate file type
        if (!file.type.startsWith('image/')) {
            toast({
                title: 'Invalid File Type',
                description: 'Please upload an image file (PNG, JPEG, or WebP).',
                variant: 'destructive'
            });
            return;
        }

        // Validate file size (max 10MB)
        const maxSizeInBytes = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSizeInBytes) {
            toast({
                title: 'File Too Large',
                description: 'Please upload an image smaller than 10MB.',
                variant: 'destructive'
            });
            return;
        }

        if (!storyData.userId || !storyData.id) {
            toast({
                title: 'Error',
                description: 'User or story ID is missing.',
                variant: 'destructive'
            });
            return;
        }

        setUploadingSceneIndex(sceneIndex);

        try {
            // Convert file to data URI
            const fileReader = new FileReader();
            const dataUri = await new Promise<string>((resolve, reject) => {
                fileReader.onload = (e) => {
                    if (e.target?.result) {
                        resolve(e.target.result as string);
                    } else {
                        reject(new Error('Failed to read file'));
                    }
                };
                fileReader.onerror = () => reject(new Error('Failed to read file'));
                fileReader.readAsDataURL(file);
            });

            // Generate unique filename
            const timestamp = Date.now();
            const fileExtension = file.name.split('.').pop() || 'jpg';
            const filename = `uploaded_scene_${sceneIndex + 1}_${timestamp}.${fileExtension}`;

            // Upload to MinIO
            const imageUrl = await uploadImageToMinIOStorage(dataUri, storyData.userId, storyData.id, filename);

            // Create new image object
            const actionPrompt = storyData.actionPrompts?.find(ap => ap.sceneIndex === sceneIndex);
            const promptText = storyData.imagePrompts?.[sceneIndex] || `Scene ${sceneIndex + 1}`;

            const newImage: GeneratedImage = {
                sceneIndex,
                originalPrompt: promptText,
                requestPrompt: `User uploaded: ${file.name}`,
                imageUrl,
                width: undefined, // We don't know dimensions of uploaded image
                height: undefined,
                chunkId: actionPrompt?.chunkId,
                chunkIndex: actionPrompt?.chunkIndex,
            };

            // Update story data
            const updatedGeneratedImages = (storyData.generatedImages || []).filter(img => img.sceneIndex !== sceneIndex);
            updatedGeneratedImages.push(newImage);

            const updatedStoryData = {
                ...storyData,
                generatedImages: updatedGeneratedImages,
            };

            setStoryData(updatedStoryData);

            // Auto-save if story exists
            if (storyData.id && storyData.userId) {
                try {
                    await saveStory(updatedStoryData, storyData.userId);
                } catch (error) {
                    console.error('Failed to auto-save story after image upload:', error);
                }
            }

            toast({
                title: 'Image Uploaded!',
                description: `Image for scene ${sceneIndex + 1} has been uploaded successfully.`,
                className: 'bg-green-500 text-white'
            });

        } catch (error) {
            console.error('Error uploading image:', error);
            toast({
                title: 'Upload Failed',
                description: 'Failed to upload the image. Please try again.',
                variant: 'destructive'
            });
        } finally {
            setUploadingSceneIndex(null);
        }
    };

    const handleGenerateVideo = async (sceneIndex: number) => {
        const existingImage = storyData.generatedImages?.find(img => img.sceneIndex === sceneIndex);
        const actionPrompt = storyData.actionPrompts?.find(ap => ap.sceneIndex === sceneIndex);

        if (!existingImage?.imageUrl) {
            toast({
                title: 'Image Required',
                description: 'Please generate or upload an image for this scene first.',
                variant: 'destructive'
            });
            return;
        }

        if (!actionPrompt?.actionDescription) {
            toast({
                title: 'Action Prompt Missing',
                description: 'Action description is required for video generation.',
                variant: 'destructive'
            });
            return;
        }

        if (!storyData.userId || !storyData.id) {
            toast({
                title: 'Error',
                description: 'User or story ID is missing.',
                variant: 'destructive'
            });
            return;
        }

        setGeneratingVideoIndex(sceneIndex);

        try {
            toast({
                title: 'Generating Video...',
                description: `Creating video for scene ${sceneIndex + 1} using Veo 2. This process takes 5-10 minutes. Please wait...`,
                className: 'bg-blue-500 text-white'
            });

            const result = await generateVideoFromImage(
                existingImage.imageUrl,
                actionPrompt.actionDescription,
                storyData.userId,
                storyData.id
            );

            if (result.success && result.videoUrl) {
                // Update the existing image object with video data
                const updatedGeneratedImages = (storyData.generatedImages || []).map(img =>
                    img.sceneIndex === sceneIndex
                        ? { ...img, videoUrl: result.videoUrl, videoPrompt: result.requestPrompt }
                        : img
                );

                const updatedStoryData = {
                    ...storyData,
                    generatedImages: updatedGeneratedImages,
                };

                setStoryData(updatedStoryData);

                // Auto-save if story exists
                if (storyData.id && storyData.userId) {
                    try {
                        await saveStory(updatedStoryData, storyData.userId);
                    } catch (error) {
                        console.error('Failed to auto-save story after video generation:', error);
                    }
                }

                toast({
                    title: 'Video Generated!',
                    description: `Video for scene ${sceneIndex + 1} has been created successfully.`,
                    className: 'bg-green-500 text-white'
                });

            } else {
                toast({
                    title: 'Video Generation Failed',
                    description: result.error || 'Failed to generate video. Please try again.',
                    variant: 'destructive'
                });
            }

        } catch (error) {
            console.error('Error generating video:', error);
            toast({
                title: 'Video Generation Error',
                description: 'An unexpected error occurred while generating the video.',
                variant: 'destructive'
            });
        } finally {
            setGeneratingVideoIndex(null);
        }
    };

    if (!storyData.generatedScript || !storyData.detailsPrompts) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Clapperboard className="h-6 w-6 text-primary" />
                        Generate Scene Images
                    </CardTitle>
                    <CardDescription>
                        First, ensure your story script and character/location/item details are generated. Then, generate image prompts.
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <p className="text-muted-foreground">
                        Please complete the &quot;Story Details&quot; and &quot;Script&quot; steps before generating images.
                    </p>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div>
                        <CardTitle className="flex items-center gap-2">
                            <Clapperboard className="h-6 w-6 text-primary" />
                            Generate Scene Images
                        </CardTitle>
                        <CardDescription>
                            Based on your script and character details, we&apos;ll generate image prompts. Review and edit them, then generate images for each scene.
                        </CardDescription>
                    </div>
                    {(storyData.imagePrompts?.length || 0) > 0 && (
                        <div className="flex gap-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={handleDownloadScenePrompts}
                                className="flex items-center gap-2"
                            >
                                <Download className="h-4 w-4" />
                                Scene Prompts (Imagen 4/Ultra)
                            </Button>
                            {(storyData.actionPrompts?.length || 0) > 0 && (
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleDownloadActionPrompts}
                                    className="flex items-center gap-2"
                                >
                                    <Download className="h-4 w-4" />
                                    Action Prompts Only
                                </Button>
                            )}
                        </div>
                    )}
                </div>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="space-y-2">
                    <Label htmlFor="image-provider-select">Image Generation Provider</Label>
                    <div className="flex items-center gap-2">
                        <Select value={imageProvider} onValueChange={(value) => setImageProviderWithPersist(value as 'picsart' | 'gemini' | 'imagen3' | 'imagen4' | 'imagen4ultra')}>
                            <SelectTrigger id="image-provider-select" className="w-[200px]">
                                <SelectValue placeholder="Select Provider" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="picsart">Picsart (Flux Dev)</SelectItem>
                                <SelectItem value="gemini">Google Gemini</SelectItem>
                                <SelectItem value="imagen3">Google Imagen 3</SelectItem>
                                <SelectItem value="imagen4">Google Imagen 4 (Preview)</SelectItem>
                                <SelectItem value="imagen4ultra">Imagen 4 Ultra</SelectItem>
                            </SelectContent>
                        </Select>
                        <Link href="/settings?tab=apiKeys" passHref>
                            <Button variant="outline" size="sm" className="text-xs">
                                <Settings className="mr-2 h-3 w-3" /> API Keys
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="space-y-2">
                    <Label htmlFor="image-style-select">Image Style</Label>
                    <div className="flex items-center gap-2">
                        <Select
                            value={storyData.imageStyleId || DEFAULT_STYLE_ID}
                            onValueChange={(value) => updateStoryData({ imageStyleId: value as ImageStyleId })}
                        >
                            <SelectTrigger id="image-style-select" className="w-[200px]">
                                <SelectValue placeholder="Select Style" />
                            </SelectTrigger>
                            <SelectContent>
                                {Object.values(IMAGE_STYLES).map((style) => (
                                    <SelectItem key={style.id} value={style.id}>
                                        {style.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <Link href="/settings?tab=imageStyles" passHref>
                            <Button variant="outline" size="sm" className="text-xs">
                                <Settings className="mr-2 h-3 w-3" /> Manage Styles
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="space-y-3">
                    <div className="flex items-center gap-2">
                        <Button
                            onClick={handleGenerateImagePrompts}
                            disabled={isLoading.imagePrompts || googleKeyMissing}
                            variant="outline"
                        >
                            {isLoading.imagePrompts ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            ) : (
                                <ImageIcon className="mr-2 h-4 w-4" />
                            )}
                            {storyData.imagePrompts && storyData.imagePrompts.length > 0 ? 'Update Image Prompts & Associations' : 'Generate Image Prompts'}
                        </Button>

                        {promptProgress?.failed && (
                            <Button
                                onClick={handleRetryImagePrompts}
                                disabled={isLoading.imagePrompts || googleKeyMissing}
                                variant="default"
                                size="sm"
                                className="bg-orange-600 hover:bg-orange-700"
                            >
                                <RefreshCw className="mr-2 h-3 w-3" />
                                Resume Generation
                            </Button>
                        )}
                    </div>

                    {/* Enhanced Progress Bar for Prompt Generation */}
                    {promptProgress && (
                        <div className="space-y-3">
                            <div className="flex justify-between items-center">
                                <span className="text-sm font-medium">
                                    Generating Prompts: {promptProgress.completed}/{promptProgress.total} batches
                                </span>
                                <div className="flex items-center gap-2">
                                    <span className="text-xs text-muted-foreground">
                                        {Math.round((promptProgress.completed / promptProgress.total) * 100) || 0}%
                                    </span>
                                    {promptProgress.failed && (
                                        <span className="text-xs text-orange-600 flex items-center gap-1">
                                            <div className="h-2 w-2 bg-orange-500 rounded-full"></div>
                                            Partial
                                        </span>
                                    )}
                                </div>
                            </div>
                            <Progress 
                                value={(promptProgress.completed / promptProgress.total) * 100} 
                                className="w-full h-3" 
                            />
                            {!promptProgress.failed && promptProgress.completed < promptProgress.total && (
                                <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                                    <div className="animate-spin rounded-full h-3 w-3 border-2 border-primary border-t-transparent"></div>
                                    Processing batch {promptProgress.completed + 1} of {promptProgress.total}...
                                </div>
                            )}
                            {promptProgress.failed && (
                                <div className="p-3 border rounded-md bg-orange-50 border-orange-200">
                                    <div className="flex items-center gap-2 mb-2">
                                        <div className="h-2 w-2 bg-orange-500 rounded-full"></div>
                                        <p className="text-sm text-orange-800 font-medium">Generation was interrupted</p>
                                    </div>
                                    <p className="text-xs text-orange-700 mb-2">
                                        Your progress has been automatically saved. Click &quot;Resume Generation&quot; to continue from where it left off.
                                    </p>
                                    {promptProgress.error && (
                                        <p className="text-xs text-orange-600">Reason: {promptProgress.error}</p>
                                    )}
                                </div>
                            )}
                        </div>
                    )}

                    {googleKeyMissing && <p className="text-xs text-destructive">Google API Key needed for prompt generation. <Link href="/settings?tab=apiKeys" className="underline">Configure here</Link>.</p>}
                    
                    {perplexityModel?.includes('reasoning') && (
                        <div className="p-3 border rounded-md bg-blue-50 border-blue-200">
                            <p className="text-sm text-blue-800 font-medium">💭 Using Reasoning Model</p>
                            <p className="text-xs text-blue-700 mt-1">
                                Reasoning models take longer to process but provide higher quality results. Each batch may take 2-3 minutes.
                            </p>
                        </div>
                    )}

                    {storyData.imagePrompts && storyData.imagePrompts.length > 0 && (
                        <Button
                            onClick={() => setIsRegenerateDialogOpen(true)}
                            disabled={isLoading.imagePrompts || googleKeyMissing}
                            variant="outline"
                            size="sm"
                            className="ml-2"
                        >
                            <RefreshCw className="mr-2 h-3 w-3" />
                            Advanced: Regenerate Prompts (AI Call)
                        </Button>
                    )}
                </div>

                {storyData.imagePrompts && storyData.imagePrompts.length > 0 && (
                    <div className="space-y-2">
                        <div className="flex justify-between items-center">
                            <div>
                                <Label>Generated Scene Prompts ({storyData.imagePrompts.length})</Label>
                                <p className="text-xs text-muted-foreground mt-1">Generate AI images, upload your own, or create videos from images for each scene</p>
                            </div>
                            <div className="flex items-center gap-2">
                                <Button
                                    onClick={handleGenerateAllImages}
                                    disabled={isLoading.allImages || picsartKeyMissing || geminiKeyMissing || imagen3KeyMissing || imagen4KeyMissing}
                                    size="sm"
                                >
                                    {isLoading.allImages ? (
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    ) : (
                                        <ImageIcon className="mr-2 h-4 w-4" />
                                    )}
                                    Generate All Scene Images ({countSceneImages(storyData)}/{storyData.imagePrompts.length})
                                </Button>
                                {isLoading.allImages && (
                                    <Button onClick={handleStopGeneration} variant="destructive" size="sm">
                                        Stop
                                    </Button>
                                )}
                            </div>
                        </div>
                        {picsartKeyMissing && imageProvider === 'picsart' && <p className="text-xs text-destructive">Picsart API Key needed for image generation. <Link href="/settings?tab=apiKeys" className="underline">Configure here</Link>.</p>}
                        {geminiKeyMissing && imageProvider === 'gemini' && <p className="text-xs text-destructive">Gemini API Key needed for image generation. <Link href="/settings?tab=apiKeys" className="underline">Configure here</Link>.</p>}
                        {imagen3KeyMissing && imageProvider === 'imagen3' && <p className="text-xs text-destructive">Google API Key needed for Imagen 3 generation. <Link href="/settings?tab=apiKeys" className="underline">Configure here</Link>.</p>}
                        {imagen4KeyMissing && imageProvider === 'imagen4' && <p className="text-xs text-destructive">Google API Key needed for Imagen 4 generation. <Link href="/settings?tab=apiKeys" className="underline">Configure here</Link>.</p>}
                        {imagen4UltraKeyMissing && imageProvider === 'imagen4ultra' && <p className="text-xs text-destructive">Google API Key needed for Imagen 4 Ultra generation. <Link href="/settings?tab=apiKeys" className="underline">Configure here</Link>.</p>}
                        {imageProvider === 'imagen4' && <p className="text-xs text-blue-600">Note: Imagen 4 is in preview. If unavailable, it will automatically fall back to Imagen 3.</p>}


                        {/* Enhanced Progress Bar for Image Generation */}
                        {(isLoading.allImages || imageGenerationProgress.total > 0) && (
                            <div className="mt-3 space-y-2">
                                <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium">
                                        {imageGenerationProgress.completed === imageGenerationProgress.total && imageGenerationProgress.total > 0
                                            ? `✅ All Images Generated: ${imageGenerationProgress.completed}/${imageGenerationProgress.total}`
                                            : `Generating Images: ${imageGenerationProgress.completed}/${imageGenerationProgress.total}`
                                        }
                                    </span>
                                    <span className="text-xs text-muted-foreground">
                                        {Math.round((imageGenerationProgress.completed / imageGenerationProgress.total) * 100) || 0}%
                                    </span>
                                </div>
                                <Progress 
                                    value={(imageGenerationProgress.completed / imageGenerationProgress.total) * 100} 
                                    className="w-full h-3" 
                                />
                                {imageGenerationProgress.generating.length > 0 && (
                                    <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                                        <div className="animate-spin rounded-full h-3 w-3 border-2 border-primary border-t-transparent"></div>
                                        Currently processing scene {imageGenerationProgress.generating[0] + 1}
                                    </div>
                                )}
                                {isLoading.allImages && imageGenerationProgress.generating.length === 0 && imageGenerationProgress.completed < imageGenerationProgress.total && (
                                    <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                                        <div className="animate-pulse h-2 w-2 bg-primary rounded-full"></div>
                                        <div className="animate-pulse h-2 w-2 bg-primary rounded-full" style={{animationDelay: '0.2s'}}></div>
                                        <div className="animate-pulse h-2 w-2 bg-primary rounded-full" style={{animationDelay: '0.4s'}}></div>
                                        <span className="ml-2">Preparing next image...</span>
                                    </div>
                                )}
                                {imageGenerationProgress.completed === imageGenerationProgress.total && imageGenerationProgress.total > 0 && !isLoading.allImages && (
                                    <div className="flex items-center justify-center gap-2 text-xs text-green-600">
                                        <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                                        All images completed successfully!
                                    </div>
                                )}
                            </div>
                        )}
                        <ScrollArea className="h-[400px] w-full rounded-md border p-3 bg-muted/20">
                            <div className="space-y-4">
                                <TooltipProvider>
                                    {storyData.imagePrompts.map((promptText, sceneIndex) => {
                                        const existingImage = storyData.generatedImages?.find(img => img.sceneIndex === sceneIndex);
                                        const isCurrentlyGenerating = imageGenerationProgress.generating.includes(sceneIndex);
                                        const isEditing = isImagePromptEditing[sceneIndex];
                                        const actionPrompt = storyData.actionPrompts?.find(ap => ap.sceneIndex === sceneIndex);

                                        return (
                                            <Card key={`scene-card-${sceneIndex}`} className="border-l-4 border-l-primary/20">
                                                <CardContent className="p-4 space-y-3">
                                                    <div className="flex justify-between items-start">
                                                        <div>
                                                            <Label htmlFor={`prompt-${sceneIndex}`} className="text-xs font-semibold">Scene {sceneIndex + 1} Prompt</Label>
                                                        </div>
                                                        <div className="flex gap-2">
                                                            <Button variant="ghost" size="icon" className="h-7 w-7" onClick={() => toggleImagePromptEditing(sceneIndex)}>
                                                                <Edit2 className="h-3.5 w-3.5" />
                                                            </Button>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                className="h-7 w-7"
                                                                onClick={() => handleGenerateIndividualImage(storyData.imagePrompts![sceneIndex], sceneIndex)}
                                                                disabled={isLoading[`image-${sceneIndex}`] || isLoading.allImages || picsartKeyMissing || geminiKeyMissing || imagen3KeyMissing}
                                                            >
                                                                {isLoading[`image-${sceneIndex}`] || isCurrentlyGenerating ? <Loader2 className="h-3.5 w-3.5 animate-spin" /> : <RefreshCw className="h-3.5 w-3.5" />}
                                                            </Button>
                                                            <div className="relative">
                                                                <input
                                                                    type="file"
                                                                    accept="image/*"
                                                                    onChange={(e) => {
                                                                        const file = e.target.files?.[0];
                                                                        if (file) {
                                                                            handleUploadImage(file, sceneIndex);
                                                                        }
                                                                        // Reset the input value so the same file can be uploaded again
                                                                        e.target.value = '';
                                                                    }}
                                                                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                                                    disabled={uploadingSceneIndex === sceneIndex}
                                                                />
                                                                <Button
                                                                    variant="ghost"
                                                                    size="icon"
                                                                    className="h-7 w-7"
                                                                    disabled={uploadingSceneIndex === sceneIndex}
                                                                    title="Upload your own image"
                                                                >
                                                                    {uploadingSceneIndex === sceneIndex ? (
                                                                        <Loader2 className="h-3.5 w-3.5 animate-spin" />
                                                                    ) : (
                                                                        <Upload className="h-3.5 w-3.5" />
                                                                    )}
                                                                </Button>
                                                            </div>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                className="h-7 w-7"
                                                                onClick={() => handleGenerateVideo(sceneIndex)}
                                                                disabled={generatingVideoIndex === sceneIndex || !existingImage?.imageUrl}
                                                                title="Generate video from image using Veo 2"
                                                            >
                                                                {generatingVideoIndex === sceneIndex ? (
                                                                    <Loader2 className="h-3.5 w-3.5 animate-spin" />
                                                                ) : (
                                                                    <Video className="h-3.5 w-3.5" />
                                                                )}
                                                            </Button>
                                                        </div>
                                                    </div>
                                                    {isEditing ? (
                                                        <Textarea
                                                            id={`prompt-${sceneIndex}`}
                                                            value={promptText}
                                                            onChange={(e) => updateImagePromptText(sceneIndex, e.target.value)}
                                                            className="text-xs min-h-[60px]"
                                                        />
                                                    ) : (
                                                        <p className="text-xs p-2 border rounded-md bg-background min-h-[60px]">
                                                            <HighlightedPrompt prompt={promptText} />
                                                        </p>
                                                    )}

                                                    {actionPrompt && (
                                                        <div className="mt-2 space-y-2">
                                                            <Tooltip>
                                                                <TooltipTrigger asChild>
                                                                    <div className="flex items-center gap-1.5 text-xs text-muted-foreground cursor-default">
                                                                        <ListMusic className="h-3.5 w-3.5 text-blue-500" />
                                                                        <span>Narration Chunk: {actionPrompt.chunkIndex !== undefined ? actionPrompt.chunkIndex + 1 : 'N/A'}</span>
                                                                    </div>
                                                                </TooltipTrigger>
                                                                <TooltipContent side="bottom" align="start" className="max-w-xs">
                                                                    <p className="text-xs leading-relaxed">{actionPrompt.chunkText || "No narration text available for this chunk."}</p>
                                                                </TooltipContent>
                                                            </Tooltip>
                                                            <div className="flex items-start gap-1.5 text-xs text-muted-foreground">
                                                                <ChevronsRight className="h-3.5 w-3.5 text-green-500 mt-0.5 flex-shrink-0" />
                                                                <ActionDisplay actionDescription={actionPrompt.actionDescription} storyData={storyData} />
                                                            </div>
                                                        </div>
                                                    )}

                                                    {existingImage?.imageUrl && (
                                                        <div className="mt-2">
                                                            <Label className="text-xs font-medium">Generated Image:</Label>
                                                            <div className="relative aspect-video w-full max-w-sm overflow-hidden rounded-md border mt-1 group">
                                                                <Image
                                                                    src={existingImage.imageUrl}
                                                                    alt={`Generated image for scene ${sceneIndex + 1}`}
                                                                    fill
                                                                    sizes="(max-width: 768px) 100vw, 400px"
                                                                    style={{ objectFit: "contain" }}
                                                                    className="bg-muted cursor-pointer transition-transform hover:scale-105"
                                                                    priority={sceneIndex < 3}
                                                                    unoptimized
                                                                    onClick={() => setPopupImage({ src: existingImage.imageUrl, alt: `Scene ${sceneIndex + 1}: ${promptText.substring(0, 30)}` })}
                                                                />
                                                                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                                                    <Button
                                                                        size="sm"
                                                                        variant="secondary"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            handleDownloadImage(existingImage.imageUrl, `Scene_${sceneIndex + 1}`);
                                                                        }}
                                                                        className="bg-white/90 hover:bg-white text-black"
                                                                    >
                                                                        <Download className="h-3 w-3" />
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                            <p className="text-xs text-muted-foreground mt-1 whitespace-pre-wrap">Request to AI: &quot;{existingImage.requestPrompt}&quot;</p>
                                                        </div>
                                                    )}

                                                    {existingImage?.videoUrl && (
                                                        <div className="mt-2">
                                                            <Label className="text-xs font-medium">Generated Video:</Label>
                                                            <div className="relative aspect-video w-full max-w-sm overflow-hidden rounded-md border mt-1 group">
                                                                <video
                                                                    src={existingImage.videoUrl}
                                                                    controls
                                                                    className="w-full h-full object-contain bg-muted"
                                                                    preload="metadata"
                                                                >
                                                                    Your browser does not support the video tag.
                                                                </video>
                                                                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                                                    <Button
                                                                        size="sm"
                                                                        variant="secondary"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            const link = document.createElement('a');
                                                                            link.href = existingImage.videoUrl!;
                                                                            link.download = `Scene_${sceneIndex + 1}_video.mp4`;
                                                                            document.body.appendChild(link);
                                                                            link.click();
                                                                            document.body.removeChild(link);
                                                                        }}
                                                                        className="bg-white/90 hover:bg-white text-black"
                                                                    >
                                                                        <Download className="h-3 w-3" />
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                            {existingImage.videoPrompt && (
                                                                <p className="text-xs text-muted-foreground mt-1 whitespace-pre-wrap">Video prompt: &quot;{existingImage.videoPrompt}&quot;</p>
                                                            )}
                                                        </div>
                                                    )}
                                                </CardContent>
                                            </Card>
                                        );
                                    })}
                                </TooltipProvider>
                            </div>
                        </ScrollArea>
                    </div>
                )}
            </CardContent>
            <ImagePopup
                src={popupImage?.src || ''}
                alt={popupImage?.alt || ''}
                isOpen={!!popupImage}
                onClose={() => setPopupImage(null)}
            />

            <AlertDialog open={isRegenerateDialogOpen} onOpenChange={setIsRegenerateDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Prompt Regeneration</AlertDialogTitle>
                        <AlertDialogDescription>
                            This will call the AI to generate new image prompts and action descriptions based on your current script and detail prompts.
                            Existing images will need to be regenerated for these new prompts. Are you sure you want to proceed?
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleRegenerateImagePrompts}>Regenerate Prompts</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </Card>
    );
}

