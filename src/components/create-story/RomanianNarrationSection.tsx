import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { ConvertibleAudioPlayer } from '@/components/ConvertibleAudioPlayer';

import { Languages, Loader2, RefreshCw, Mic, Edit, Save, X, StopCircle, Download } from 'lucide-react';
import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useStreamingApi } from '@/hooks/useStreamingApi';
import { saveStory } from '@/actions/baserowStoryActions';
import { generateNarrationAudio } from '@/actions/storyActions';
import { debounce, type DebouncedFunction } from '@/utils/debounce';
import type { UseStoryStateReturn } from '@/hooks/useStoryState';

interface RomanianNarrationSectionProps {
  storyState: UseStoryStateReturn;
}

interface RomanianChunk {
  id: string;
  text: string;
  audioUrl?: string;
  duration?: number;
  index: number;
}

export function RomanianNarrationSection({ storyState }: RomanianNarrationSectionProps) {
  const { toast } = useToast();
  const streamingApi = useStreamingApi({
    onMessage: (data) => {
      if (data.type === 'success' && data.data && 'romanianChunks' in data.data && data.data.romanianChunks) {
        handleTranslationSuccess(data.data.romanianChunks);
      }
    },
    onError: (error) => {
      console.error('Translation streaming error:', error);
      toast({
        title: 'Translation Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred during translation.',
        variant: 'destructive'
      });
      setIsTranslating(false);
      handleSetLoading('romanianTranslation', false);
    }
  });
  
  const {
    storyData,
    updateStoryData,
    userApiKeys,
    aiProvider,
    googleScriptModel,
    perplexityModel,
    selectedTtsModel,
    selectedVoiceId,
    selectedGoogleVoiceId,
    selectedGoogleApiModel,
    handleSetLoading
  } = storyState;

  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editText, setEditText] = useState<string>('');
  const [isTranslating, setIsTranslating] = useState(false);
  const [generatingAudio, setGeneratingAudio] = useState<{ [key: string]: boolean }>({});
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSavedRomanianChunks, setLastSavedRomanianChunks] = useState<RomanianChunk[] | null>(null);
  
  // Sequential generation state (similar to English)
  const [romanianProcessingAllMode, setRomanianProcessingAllMode] = useState(false);
  const [currentRomanianChunkIndex, setCurrentRomanianChunkIndex] = useState(-1);

  const debouncedSaveFnRef = useRef<DebouncedFunction<[RomanianChunk[], typeof storyData]> | null>(null);

  const romanianChunks = useMemo(() => storyData.romanianNarrationChunks || [], [storyData.romanianNarrationChunks]);
  const englishChunks = storyData.narrationChunks || [];

  const completedRomanianChunks = romanianChunks.filter(chunk => chunk.audioUrl).length;
  const progressPercentage = romanianChunks.length > 0 ? (completedRomanianChunks / romanianChunks.length) * 100 : 0;

  // Autosave effect for Romanian narration chunks
  useEffect(() => {
    debouncedSaveFnRef.current = debounce(async (chunksToSave: RomanianChunk[], currentStoryData: typeof storyData) => {
      if (!currentStoryData.userId || !currentStoryData.id || chunksToSave.length === 0) return;
      
      // Prevent multiple simultaneous saves of the same content
      if (lastSavedRomanianChunks && JSON.stringify(lastSavedRomanianChunks) === JSON.stringify(chunksToSave)) {
        console.log('Skipping Romanian chunks save - content already saved');
        return;
      }
      
      // Prevent save if already saving
      if (isAutoSaving) {
        console.log('Skipping Romanian chunks save - already in progress');
        return;
      }

      setIsAutoSaving(true);
      try {
        const updatedStoryData = {
          ...currentStoryData,
          romanianNarrationChunks: chunksToSave
        };

        const saveResult = await saveStory(updatedStoryData, currentStoryData.userId);
        if (saveResult.success) {
          setLastSavedRomanianChunks([...chunksToSave]);
          toast({
            title: 'Romanian Chunks Saved!',
            description: 'Your Romanian narration changes have been automatically saved.',
            className: 'bg-green-500 text-white'
          });
        } else {
          toast({
            title: 'Error Saving Romanian Chunks',
            description: saveResult.error || 'An unknown error occurred.',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error("Error in Romanian chunks autoSave:", error);
        toast({
          title: 'Error',
          description: 'An unexpected error occurred during Romanian chunks auto-save.',
          variant: 'destructive',
        });
      } finally {
        setIsAutoSaving(false);
      }
    }, 3000); // 3 second debounce time

    return () => {
      if (debouncedSaveFnRef.current) {
        debouncedSaveFnRef.current.cancel();
      }
    };
  }, [isAutoSaving, lastSavedRomanianChunks, toast]); // Removed storyState to prevent debounce cancellation

  const autoSaveRomanianChunks = useCallback((chunks: RomanianChunk[]) => {
    if (debouncedSaveFnRef.current && chunks.length > 0) {
      debouncedSaveFnRef.current(chunks, storyData); // Pass current storyData as parameter
    }
  }, [storyData]); // Add storyData dependency to callback

  const handleTranslationSuccess = async (romanianChunks: { id: string; text: string; index: number }[]) => {
    const newRomanianChunks: RomanianChunk[] = romanianChunks.map(chunk => ({
      id: chunk.id,
      text: chunk.text,
      index: chunk.index,
      audioUrl: undefined,
      duration: undefined
    }));

    // Update the story data with the new Romanian chunks
    updateStoryData({
      romanianNarrationChunks: newRomanianChunks
    });

    // Force a small delay to ensure state update propagates
    await new Promise(resolve => setTimeout(resolve, 100));

    // Immediate save after translation (bypass debounce for critical save)
    try {
      const updatedStoryData = {
        ...storyData,
        romanianNarrationChunks: newRomanianChunks
      };

      console.log('[RomanianNarrationSection] About to save story with Romanian chunks:', {
        storyId: updatedStoryData.id,
        userId: updatedStoryData.userId,
        chunksCount: newRomanianChunks.length,
        chunksPreview: newRomanianChunks.slice(0, 2)
      });

      const saveResult = await saveStory(updatedStoryData, storyData.userId);
      console.log('[RomanianNarrationSection] Save result:', saveResult);
      
      if (saveResult.success) {
        setLastSavedRomanianChunks([...newRomanianChunks]);
        toast({
          title: 'Romanian Translation Complete',
          description: `Successfully translated ${newRomanianChunks.length} chunks to Romanian and saved to database.`,
          className: 'bg-green-500 text-white'
        });
        
        // Force UI refresh by triggering a state update
        updateStoryData({
          romanianNarrationChunks: [...newRomanianChunks]
        });
      } else {
        console.error('[RomanianNarrationSection] Save failed:', saveResult.error);
        toast({
          title: 'Translation Complete but Save Failed',
          description: `Translation successful but failed to save: ${saveResult.error}. Please try saving manually.`,
          variant: 'destructive'
        });
      }
    } catch (saveError) {
      console.error('[RomanianNarrationSection] Error saving Romanian translation:', saveError);
      toast({
        title: 'Translation Complete but Save Failed', 
        description: 'Translation successful but failed to save. Please try saving manually.',
        variant: 'destructive'
      });
    }
    
    setIsTranslating(false);
    handleSetLoading('romanianTranslation', false);
  };

  const handleTranslateToRomanian = async () => {
    if (!englishChunks.length) {
      toast({
        title: 'No English chunks found',
        description: 'Please generate English narration chunks first.',
        variant: 'destructive'
      });
      return;
    }

    // Prevent concurrent translation calls
    if (isTranslating) {
      console.log('[RomanianNarrationSection] Translation already in progress, ignoring duplicate call');
      return;
    }

    setIsTranslating(true);
    handleSetLoading('romanianTranslation', true);

    try {
      console.log('[RomanianNarrationSection] About to call translation API with streaming:', {
        storyId: storyData.id,
        userId: storyData.userId,
        language: 'romanian',
        aiProvider,
        googleScriptModel,
        perplexityModel,
        stream: true
      });

      // Call the new API route for translation with streaming
      await streamingApi.callStreamingApi('/api/translation', {
        storyId: storyData.id,
        userId: storyData.userId,
        language: 'romanian',
        aiProvider: aiProvider || 'google',
        googleScriptModel,
        perplexityModel,
        stream: true // Enable streaming
      });
    } catch (error) {
      console.error('Error translating to Romanian:', error);
      // Check if the error is related to a timeout
      if (error instanceof Error && (error.message.includes('timeout') || error.message.includes('fetch'))) {
        toast({
          title: 'Translation Taking Longer Than Expected',
          description: 'The translation is still processing in the background. Please refresh the page in a few minutes to see the results.',
          className: 'bg-yellow-500 text-white'
        });
      } else {
        toast({
          title: 'Translation Error',
          description: error instanceof Error ? error.message : 'An unexpected error occurred during translation. Please try again.',
          variant: 'destructive'
        });
      }
      setIsTranslating(false);
      handleSetLoading('romanianTranslation', false);
    }
  };

  const handleEditChunk = (index: number, text: string) => {
    setEditingIndex(index);
    setEditText(text);
  };

  const handleSaveEdit = () => {
    if (editingIndex === null) return;

    const updatedChunks = [...romanianChunks];
    updatedChunks[editingIndex] = {
      ...updatedChunks[editingIndex],
      text: editText
    };

    updateStoryData({
      romanianNarrationChunks: updatedChunks
    });

    // Trigger autosave
    autoSaveRomanianChunks(updatedChunks);

    setEditingIndex(null);
    setEditText('');

    toast({
      title: 'Chunk Updated',
      description: 'Romanian chunk text has been updated.',
      className: 'bg-green-500 text-white'
    });
  };

  const handleCancelEdit = () => {
    setEditingIndex(null);
    setEditText('');
  };

  const handleGenerateRomanianAudio = useCallback(async (chunk: RomanianChunk, index: number) => {
    if (!storyData.id || !storyData.userId) {
      toast({
        title: 'Story Not Saved',
        description: 'Please save your story before generating Romanian audio.',
        variant: 'destructive'
      });
      return;
    }

    const chunkKey = `romanian_${chunk.id}`;
    setGeneratingAudio(prev => ({ ...prev, [chunkKey]: true }));

    try {
      // Use the EXACT same voice logic as English narration
      let voiceIdToUse: string | undefined = undefined;
      if (selectedTtsModel === 'elevenlabs') {
        voiceIdToUse = selectedVoiceId || storyData.elevenLabsVoiceId;
      } else if (selectedTtsModel === 'google') {
        voiceIdToUse = selectedGoogleVoiceId;
      }

      const result = await generateNarrationAudio({
        script: chunk.text,
        voiceId: selectedTtsModel === 'elevenlabs' ? voiceIdToUse : selectedGoogleVoiceId,
        ttsModel: selectedTtsModel || 'elevenlabs',
        googleApiModel: selectedTtsModel === 'google' ? selectedGoogleApiModel : undefined,
        languageCode: selectedTtsModel === 'google' ? 'ro-RO' : undefined, // Romanian language code only for Google TTS
        userId: storyData.userId,
        storyId: storyData.id,
        chunkId: chunk.id,
        isRomanian: true // Flag for Romanian narration
      });

      if (result.success && result.data?.audioStorageUrl) {
        const updatedChunks = [...romanianChunks];
        updatedChunks[index] = {
          ...updatedChunks[index],
          audioUrl: result.data.audioStorageUrl,
          duration: result.data.duration
        };

        // Use the latest storyData state to ensure we don't lose other chunks
        updateStoryData({
          romanianNarrationChunks: updatedChunks
        });

        // Trigger autosave
        autoSaveRomanianChunks(updatedChunks);

        toast({
          title: 'Romanian Audio Generated',
          description: `Generated Romanian audio for chunk ${index + 1}.`,
          className: 'bg-green-500 text-white'
        });

        // Handle sequential processing (similar to English)
        if (romanianProcessingAllMode) {
          const nextUnprocessed = updatedChunks.findIndex((c, idx) => idx > index && !c.audioUrl);
          if (nextUnprocessed !== -1) {
            setCurrentRomanianChunkIndex(nextUnprocessed);
          } else {
            // All chunks completed
            setRomanianProcessingAllMode(false);
            setCurrentRomanianChunkIndex(-1);
            toast({
              title: 'All Romanian Audio Generated!',
              description: 'Audio for all Romanian chunks is ready.',
              className: 'bg-green-500 text-white'
            });
          }
        }
      } else {
        toast({
          title: 'Audio Generation Failed',
          description: result.error || 'Failed to generate Romanian audio.',
          variant: 'destructive'
        });
        // Stop processing on error
        if (romanianProcessingAllMode) {
          setRomanianProcessingAllMode(false);
          setCurrentRomanianChunkIndex(-1);
        }
      }
    } catch (error) {
      console.error('Error generating Romanian audio:', error);
      toast({
        title: 'Audio Generation Error',
        description: 'An unexpected error occurred during Romanian audio generation.',
        variant: 'destructive'
      });
      // Stop processing on error
      if (romanianProcessingAllMode) {
        setRomanianProcessingAllMode(false);
        setCurrentRomanianChunkIndex(-1);
      }
    } finally {
      setGeneratingAudio(prev => ({ ...prev, [chunkKey]: false }));
    }
  }, [storyData.id, storyData.userId, storyData.elevenLabsVoiceId, selectedTtsModel, selectedVoiceId, selectedGoogleVoiceId, selectedGoogleApiModel, romanianChunks, updateStoryData, autoSaveRomanianChunks, toast, romanianProcessingAllMode, setCurrentRomanianChunkIndex, setRomanianProcessingAllMode]);

  const handleGenerateAllRomanianAudio = () => {
    if (!storyData.id || !storyData.userId) {
      toast({
        title: 'Story Not Saved',
        description: 'Please save your story before generating Romanian audio.',
        variant: 'destructive'
      });
      return;
    }

    // Start sequential processing (similar to English)
    setRomanianProcessingAllMode(true);
    const firstUnprocessed = romanianChunks.findIndex(chunk => !chunk.audioUrl);
    if (firstUnprocessed !== -1) {
      setCurrentRomanianChunkIndex(firstUnprocessed);
    } else {
      toast({
        title: 'All Chunks Already Generated',
        description: 'All Romanian chunks already have audio.',
        className: 'bg-green-500 text-white'
      });
      setRomanianProcessingAllMode(false);
    }
  };

  const handleStopRomanianGeneration = () => {
    setRomanianProcessingAllMode(false);
    setCurrentRomanianChunkIndex(-1);
    toast({
      title: 'Romanian Generation Stopped',
      description: 'Romanian audio generation has been stopped.',
      className: 'bg-primary text-primary-foreground'
    });
  };

  // Auto-continue Romanian generation when in processing mode (similar to English)
  useEffect(() => {
    if (romanianProcessingAllMode && currentRomanianChunkIndex >= 0 && !generatingAudio[`romanian_${romanianChunks[currentRomanianChunkIndex]?.id}`]) {
      const timer = setTimeout(() => {
        if (currentRomanianChunkIndex < romanianChunks.length) {
          const chunk = romanianChunks[currentRomanianChunkIndex];
          handleGenerateRomanianAudio(chunk, currentRomanianChunkIndex);
        }
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [romanianProcessingAllMode, currentRomanianChunkIndex, generatingAudio, romanianChunks, handleGenerateRomanianAudio]);

  const canTranslate = !isTranslating && englishChunks.length > 0 && 
    ((aiProvider === 'google' && userApiKeys?.googleApiKey) || 
     (aiProvider === 'perplexity' && userApiKeys?.perplexityApiKey));

  const canGenerateAudio = !isTranslating && 
    ((selectedTtsModel === 'elevenlabs' && userApiKeys?.elevenLabsApiKey) || 
     (selectedTtsModel === 'google' && userApiKeys?.googleApiKey));

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Languages className="h-5 w-5" />
          Romanian Translation & Narration
          {isAutoSaving && (
            <div className="flex items-center gap-1 ml-auto">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span className="text-xs text-muted-foreground">Auto-saving...</span>
            </div>
          )}
        </CardTitle>
        <CardDescription>
          Generate Romanian translations of your story chunks and create Romanian audio narration.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {romanianChunks.length === 0 ? (
          <div className="text-center py-6">
            <div className="bg-blue-50 border border-blue-200 rounded p-4 mb-4">
              <h4 className="text-sm font-medium text-blue-800">Generate Romanian Translation</h4>
              <p className="text-xs text-blue-700 mt-1">
                Translate your English narration chunks to Romanian for bilingual storytelling.
              </p>
            </div>
            <Button 
              onClick={handleTranslateToRomanian}
              disabled={!canTranslate || isTranslating}
              className="w-full"
            >
              {isTranslating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Translating to Romanian...
                </>
              ) : (
                <>
                  <Languages className="mr-2 h-4 w-4" />
                  Generate Romanian Translation
                </>
              )}
            </Button>
            {!canTranslate && (
              <p className="text-xs text-destructive mt-2">
                API key required for translation. Please configure in Account Settings.
              </p>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">
                Romanian Audio Progress ({completedRomanianChunks}/{romanianChunks.length} chunks completed)
              </Label>
              <span className="text-sm text-muted-foreground">
                {Math.round(progressPercentage)}%
              </span>
            </div>
            <Progress value={progressPercentage} className="w-full" />

            {/* Download Romanian Audio Button */}
            {romanianChunks.some(chunk => chunk.audioUrl) ? (
              <Button 
                onClick={async () => {
                  try {
                    // Import the download function dynamically to avoid circular dependencies
                    const { downloadLanguageAudioAsZip } = await import('@/utils/downloadLanguageAudioUtils');
                    await downloadLanguageAudioAsZip(storyData, 'ro');
                    toast({
                      title: "Download Started",
                      description: "Romanian audio files are being downloaded as a ZIP archive.",
                    });
                  } catch (error) {
                    toast({
                      title: "Download Failed",
                      description: error instanceof Error ? error.message : "Failed to download Romanian audio files.",
                      variant: "destructive",
                    });
                  }
                }}
                variant="outline"
                className="w-full"
              >
                <Download className="mr-2 h-4 w-4" />
                Download Romanian Audio Files
              </Button>
            ) : (
              <div className="text-center py-2">
                <p className="text-sm text-muted-foreground">
                  Generate Romanian audio chunks to enable download
                </p>
              </div>
            )}

            <div className="flex gap-2">
              <Button 
                onClick={handleTranslateToRomanian}
                disabled={!canTranslate || isTranslating}
                variant="outline"
                size="sm"
              >
                {isTranslating ? (
                  <>
                    <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                    Retranslating...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-3 w-3" />
                    Retranslate
                  </>
                )}
              </Button>
              
              {!romanianProcessingAllMode ? (
                <Button 
                  onClick={handleGenerateAllRomanianAudio}
                  disabled={!canGenerateAudio}
                  className="flex-1"
                >
                  <Mic className="mr-2 h-4 w-4" />
                  Generate All Romanian Audio
                </Button>
              ) : (
                <>
                  <Button 
                    disabled
                    className="flex-1"
                  >
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating All... ({(currentRomanianChunkIndex + 1)}/{romanianChunks.length})
                  </Button>
                  <Button 
                    onClick={handleStopRomanianGeneration}
                    variant="destructive"
                    size="default"
                  >
                    <StopCircle className="mr-2 h-4 w-4" />
                    Stop
                  </Button>
                </>
              )}
            </div>

            <div className="space-y-3">
              <Label className="text-sm font-medium">Romanian Chunks</Label>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {romanianChunks.map((chunk, index) => (
                  <Card key={chunk.id} className="p-3">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-medium text-muted-foreground">
                          Romanian Chunk {index + 1}
                        </span>
                        <div className="flex items-center gap-1">
                          {editingIndex === index ? (
                            <>
                              <Button
                                onClick={handleSaveEdit}
                                size="sm"
                                variant="outline"
                              >
                                <Save className="h-3 w-3" />
                              </Button>
                              <Button
                                onClick={handleCancelEdit}
                                size="sm"
                                variant="outline"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </>
                          ) : (
                            <Button
                              onClick={() => handleEditChunk(index, chunk.text)}
                              size="sm"
                              variant="outline"
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                      
                      {editingIndex === index ? (
                        <Textarea
                          value={editText}
                          onChange={(e) => setEditText(e.target.value)}
                          className="min-h-[60px]"
                          placeholder="Edit Romanian text..."
                        />
                      ) : (
                        <p className="text-sm text-gray-700 bg-gray-50 p-2 rounded">
                          {chunk.text}
                        </p>
                      )}

                      <div className="flex items-center justify-between">
                        {chunk.audioUrl ? (
                          <div className="flex-1">
                            <ConvertibleAudioPlayer
                              src={chunk.audioUrl}
                              className="w-full"
                              chunkId={`ro_chunk_${chunk.id}`}
                            />
                          </div>
                        ) : (
                          <span className="text-xs text-muted-foreground">No audio generated</span>
                        )}
                        
                        <Button
                          onClick={() => handleGenerateRomanianAudio(chunk, index)}
                          disabled={!canGenerateAudio || generatingAudio[`romanian_${chunk.id}`] || romanianProcessingAllMode}
                          size="sm"
                          variant={chunk.audioUrl ? "outline" : "default"}
                        >
                          {generatingAudio[`romanian_${chunk.id}`] || (romanianProcessingAllMode && currentRomanianChunkIndex === index) ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : (
                            <Mic className="h-3 w-3" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        )}

        {!canGenerateAudio && romanianChunks.length > 0 && (
          <p className="text-xs text-destructive text-center">
            Audio generation API key required. Please configure in Account Settings.
          </p>
        )}
      </CardContent>
    </Card>
  );
}