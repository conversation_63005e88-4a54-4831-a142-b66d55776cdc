import { defineFlow } from '@genkit-ai/flow';
import { z } from 'zod';
import { 
    AISpanishTranslationInputSchema, 
    AISpanishTranslationOutputSchema 
} from '../../actions/storyActionSchemas';
import { generateAndSaveSpanishTranslation } from '../../actions/story/translationActions';

// Input schema for the flow (includes storyId)
const SpanishTranslationFlowInputSchema = AISpanishTranslationInputSchema.extend({
    storyId: z.string().describe('Story ID for saving the translation')
});

export const generateSpanishTranslationFlow = defineFlow(
    {
        name: 'generateSpanishTranslationFlow',
        inputSchema: SpanishTranslationFlowInputSchema,
        outputSchema: AISpanishTranslationOutputSchema,
    },
    async (input) => {
        console.log(`[generateSpanishTranslationFlow] Starting translation for story ${input.storyId} with ${input.chunks.length} chunks`);
        
        const result = await generateAndSaveSpanishTranslation(input.storyId, input);
        
        if (!result.success) {
            throw new Error(result.error || 'Spanish translation failed');
        }
        
        if (!result.data) {
            throw new Error('No translation data returned');
        }
        
        console.log(`[generateSpanishTranslationFlow] Successfully completed translation for story ${input.storyId}`);
        return result.data;
    }
);