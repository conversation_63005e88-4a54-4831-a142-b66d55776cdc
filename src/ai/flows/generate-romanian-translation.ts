import { defineFlow } from '@genkit-ai/flow';
import { z } from 'zod';
import { 
    AIRomanianTranslationInputSchema, 
    AIRomanianTranslationOutputSchema 
} from '../../actions/storyActionSchemas';
import { generateAndSaveRomanianTranslation } from '../../actions/story/translationActions';

// Input schema for the flow (includes storyId)
const RomanianTranslationFlowInputSchema = AIRomanianTranslationInputSchema.extend({
    storyId: z.string().describe('Story ID for saving the translation')
});

export const generateRomanianTranslationFlow = defineFlow(
    {
        name: 'generateRomanianTranslationFlow',
        inputSchema: RomanianTranslationFlowInputSchema,
        outputSchema: AIRomanianTranslationOutputSchema,
    },
    async (input) => {
        console.log(`[generateRomanianTranslationFlow] Starting translation for story ${input.storyId} with ${input.chunks.length} chunks`);
        
        const result = await generateAndSaveRomanianTranslation(input.storyId, input);
        
        if (!result.success) {
            throw new Error(result.error || 'Romanian translation failed');
        }
        
        if (!result.data) {
            throw new Error('No translation data returned');
        }
        
        console.log(`[generateRomanianTranslationFlow] Successfully completed translation for story ${input.storyId}`);
        return result.data;
    }
);