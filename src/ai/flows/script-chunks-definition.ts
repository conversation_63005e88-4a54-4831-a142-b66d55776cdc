import {ai} from '@/ai/genkit';
import {
  GenerateScriptChunksInputSchema,
  GenerateScriptChunksOutputSchema,
} from './generate-script-chunks-types';

export const generateScriptChunksPrompt = ai.definePrompt(
  {
    name: 'generateScriptChunksPrompt',
    input: { schema: GenerateScriptChunksInputSchema },
    output: { schema: GenerateScriptChunksOutputSchema },
    prompt: `You are a professional story animator director who understands visual storytelling. Your task is to split the following story script into meaningful visual scenes/chunks for animated video production. Each chunk will have a corresponding image generated and narration audio.

🚨 **CRITICAL REQUIREMENT - MANDATORY:** 
ALL words AND PUNCTUATION from the original script MUST appear in your chunks. When your chunks are joined together, they MUST recreate the EXACT original script. NO words, periods, commas, quotes, or any punctuation can be lost, changed, added, or modified. This is absolutely critical.

⚠️ **PUNCTUATION RULE:** 
Keep ALL periods, commas, quotes, and punctuation EXACTLY as they appear in the original. Do NOT remove periods from the end of sentences when splitting.

**Step-by-step process:**
1. **Read the entire script** and visualize it as an animated story
2. **Identify visual scenes** - moments that would be shown as distinct images/frames
3. **Group related content** that describes the same visual setting, character action, or moment
4. **Create coherent chunks** that each tell a complete visual moment
5. **VERIFY that all chunks together = exact original script**

**What makes a good chunk:**
- Represents ONE distinct visual scene or moment  
- Can be illustrated with a single coherent image
- Contains 1-3 sentences that belong together visually
- Has natural story flow and pacing
- Groups dialogue with related action/description
- **PRESERVES EVERY SINGLE WORD from the original**

**Examples of GOOD chunking:**
❌ Bad (sentence-by-sentence): "Max walked to the door." | "He turned the handle." | "The door creaked open."
✅ Good (visual scene): "Max walked to the door, turned the handle, and the door creaked open."

❌ Bad: "Lilly smiled." | "'I love adventures!' she said."  
✅ Good: "Lilly smiled. 'I love adventures!' she said."

❌ CRITICAL ERROR - Missing periods: "In the world lived a mouse named Pip" | "Pip had the best laugh in the meadow."
✅ CORRECT - All punctuation preserved: "In the world lived a mouse named Pip." | "Pip had the best laugh in the meadow."

**VALIDATION RULE:** 
Your chunks joined with spaces must recreate the original script EXACTLY. Any deviation will be rejected.

Script to split:
{{{script}}}

Return a JSON object with one key "scriptChunks" containing an array of strings. Each string is one visual chunk.

**REMEMBER:** Every word AND every punctuation mark (periods, commas, quotes) must be preserved exactly!
`,
    config: {
      temperature: 0.2, // Lower temperature for more deterministic and structured output
      maxOutputTokens: 8192, // Increased from 2048 to handle larger stories
      topP: 0.8,
    },
  }
);
