import { useState, useRef } from 'react';
import type { StreamingApiResponse } from '@/types/streamingApi';

interface StreamingState<T = unknown> {
  isLoading: boolean;
  error: string | null;
  data: T | null;
}

interface UseStreamingApiOptions<T = unknown> {
  onMessage?: (data: T) => void;
  onComplete?: () => void;
  onError?: (error: unknown) => void;
}

export function useStreamingApi<T = StreamingApiResponse>(options?: UseStreamingApiOptions<T>) {
  const [state, setState] = useState<StreamingState<T>>({
    isLoading: false,
    error: null,
    data: null
  });
  
  const abortControllerRef = useRef<AbortController | null>(null);

  const callStreamingApi = async (url: string, body: Record<string, unknown>) => {
    setState({ isLoading: true, error: null, data: null });
    
    try {
      // Cancel any ongoing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      // Create new abort controller
      abortControllerRef.current = new AbortController();
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('Response body is empty');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            options?.onComplete?.();
            break;
          }

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6)) as T;
                
                if (data && typeof data === 'object' && 'type' in data && data.type === 'error') {
                  const errorMessage = (data as unknown as { message: string }).message;
                  throw new Error(errorMessage);
                }
                
                if (data && typeof data === 'object' && 'type' in data && data.type === 'success') {
                  setState({ isLoading: false, error: null, data });
                }
                
                // Handle other message types
                options?.onMessage?.(data);
              } catch (parseError) {
                console.warn('Failed to parse streaming message:', line, parseError);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error: unknown) {
      // Ignore abort errors
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }
      
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setState({ isLoading: false, error: errorMessage, data: null });
      options?.onError?.(error);
    }
  };

  const abort = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  };

  return {
    ...state,
    callStreamingApi,
    abort
  };
}