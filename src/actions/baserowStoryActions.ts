"use server";

import { baserowService } from '@/lib/baserow';
import { minioService } from '@/lib/minio';
import type { Story, PageTimelineTrack, ActionPrompt } from '@/types/story';
import { revalidatePath } from 'next/cache';
import { populateStructuredMappingsFromPrompts, extractAnimalType } from './utils/storyHelpers';

interface BaserowErrorWithCode extends Error {
  code?: string | number;
}

/**
 * Transform Firebase Story structure to Baserow row format
 */
function transformStoryToBaserow(story: Story): Record<string, unknown> {
  console.log('[transformStoryToBaserow] Saving story with data:', {
    id: story.id,
    userId: story.userId,
    title: story.title,
    spanishChunksCount: story.spanishNarrationChunks?.length || 0,
    romanianChunksCount: story.romanianNarrationChunks?.length || 0,
    selectedTtsModel: story.selectedTtsModel,
    selectedGoogleTtsModel: story.selectedGoogleTtsModel,
    selectedGoogleVoiceId: story.selectedGoogleVoiceId,
    narrationVoice: story.narrationVoice
  });
  const baserowRow: Record<string, unknown> = {
    firebase_story_id: story.id || '',
    user_id: story.userId,
    Title: story.title,
    content: story.userPrompt,
    'Single select': story.status && ['draft', 'generating', 'completed', 'error'].includes(story.status) ? story.status : 'draft', // Map to Baserow status field
    workflow_status: story.workflowStatus || 'wip', // New workflow status field
    created_at: story.createdAt instanceof Date ? story.createdAt.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    updated_at: story.updatedAt instanceof Date ? story.updatedAt.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    narration_audio_url: story.narrationAudioUrl || '',
    generated_images: story.generatedImages ? JSON.stringify(story.generatedImages) : '',
    narration_chunks: story.narrationChunks ? JSON.stringify(story.narrationChunks) : '',
    spanish_narration_chunks: story.spanishNarrationChunks ? JSON.stringify(story.spanishNarrationChunks) : '',
    romanian_narration_chunks: story.romanianNarrationChunks ? JSON.stringify(story.romanianNarrationChunks) : '',
    timeline_tracks: story.timelineTracks ? JSON.stringify(story.timelineTracks) : '',
    // TODO: Add these fields to Baserow table
    // image_prompts: story.imagePrompts ? JSON.stringify(story.imagePrompts) : '',
    // action_prompts: story.actionPrompts ? JSON.stringify(story.actionPrompts) : '',
    image_style_id: story.imageStyleId || '',
    eleven_labs_voice_id: story.elevenLabsVoiceId || '',
    narration_voice: story.narrationVoice || '',
    // YouTube optimization fields - now using dedicated columns
    youtube_title: story.youtubeTitle || '',
    youtube_description: story.youtubeDescription || '',
    youtube_thumbnail_prompt: story.youtubeThumbnailPrompt || '',
    youtube_thumbnail_image_url: story.youtubeThumbnailImageUrl || '',
    // details_prompts: story.detailsPrompts ? JSON.stringify(story.detailsPrompts) : '',
    // Structured entity mappings (new columns)
    character_mappings: story.character_mappings || '',
    item_mappings: story.item_mappings || '',
    location_mappings: story.location_mappings || '',
    settings: JSON.stringify({
      narrationAudioDurationSeconds: story.narrationAudioDurationSeconds,
      imageProvider: story.imageProvider,
      aiProvider: story.aiProvider,
      perplexityModel: story.perplexityModel,
      googleScriptModel: story.googleScriptModel,
      imagePromptsData: story.imagePromptsData,
      generatedScript: story.generatedScript, // Store generated script in settings JSON
      detailsPrompts: story.detailsPrompts, // Store details prompts in settings JSON
      imagePrompts: story.imagePrompts, // Store image prompts in settings JSON
      actionPrompts: story.actionPrompts, // Store action prompts in settings JSON
      audioGenerationService: story.audioGenerationService, // Track TTS service used
      audioModel: story.audioModel, // Track audio model used
      detailImageProvider: story.detailImageProvider, // Track detail image provider
      detailImageModel: story.detailImageModel, // Track detail image model
      sceneImageProvider: story.sceneImageProvider, // Track scene image provider
      sceneImageModel: story.sceneImageModel, // Track scene image model
      selectedTtsModel: story.selectedTtsModel, // TTS model choice (elevenlabs/google)
      selectedGoogleTtsModel: story.selectedGoogleTtsModel, // Google TTS model
      selectedGoogleVoiceId: story.selectedGoogleVoiceId, // Google voice ID
      workflowStatus: story.workflowStatus // New workflow status field
    })
  };

  // Remove empty/undefined fields
  Object.keys(baserowRow).forEach(key => {
    if (baserowRow[key] === undefined || baserowRow[key] === null || baserowRow[key] === '') {
      delete baserowRow[key];
    }
  });

  return baserowRow;
}

/**
 * Transform Baserow row to Firebase Story structure
 */
function transformBaserowToStory(row: Record<string, unknown>): Story {
  const story: Story = {
    id: (row.firebase_story_id as string) || (row.id as number)?.toString(),
    userId: row.user_id as string,
    title: row.Title as string,
    userPrompt: row.content as string,
    status: row['Single select'] as string,
    workflowStatus: (row.workflow_status as 'wip' | 'completed' | 'published') || 'wip', // New workflow status field
    // Parse dates properly handling the YYYY-MM-DD format from Baserow
    createdAt: row.created_at ? (() => {
      if (typeof row.created_at === 'string' && row.created_at.match(/^\d{4}-\d{2}-\d{2}$/)) {
        // Handle YYYY-MM-DD format by creating a date at noon in local timezone
        // This prevents timezone issues and ensures consistent date display
        const [year, month, day] = (row.created_at as string).split('-').map(Number);
        return new Date(year, month - 1, day, 12, 0, 0); // Noon local time
      }
      return new Date(row.created_at as string);
    })() : new Date(),
    updatedAt: row.updated_at ? (() => {
      if (typeof row.updated_at === 'string' && row.updated_at.match(/^\d{4}-\d{2}-\d{2}$/)) {
        // Handle YYYY-MM-DD format by creating a date at noon in local timezone
        // This prevents timezone issues and ensures consistent date display
        const [year, month, day] = (row.updated_at as string).split('-').map(Number);
        return new Date(year, month - 1, day, 12, 0, 0); // Noon local time
      }
      return new Date(row.updated_at as string);
    })() : new Date(),
    narrationAudioUrl: row.narration_audio_url as string,
    generatedImages: row.generated_images ? JSON.parse(row.generated_images as string) : [],
    narrationChunks: row.narration_chunks ? JSON.parse(row.narration_chunks as string) : [],
    spanishNarrationChunks: row.spanish_narration_chunks ? JSON.parse(row.spanish_narration_chunks as string) : [],
    romanianNarrationChunks: row.romanian_narration_chunks ? JSON.parse(row.romanian_narration_chunks as string) : [],
    timelineTracks: row.timeline_tracks ? JSON.parse(row.timeline_tracks as string) : [],
    // Initialize empty arrays, will be populated from columns or JSON settings
    imagePrompts: [],
    actionPrompts: [],
    imageStyleId: row.image_style_id as string,
    elevenLabsVoiceId: row.eleven_labs_voice_id as string,
    narrationVoice: row.narration_voice as string,
    // YouTube optimization fields - read from dedicated columns
    youtubeTitle: row.youtube_title as string,
    youtubeDescription: row.youtube_description as string,
    youtubeThumbnailPrompt: row.youtube_thumbnail_prompt as string,
    youtubeThumbnailImageUrl: row.youtube_thumbnail_image_url as string,
    // detailsPrompts: row.details_prompts ? JSON.parse(row.details_prompts) : undefined
    // Structured entity mappings (new columns)
    character_mappings: row.character_mappings as string,
    item_mappings: row.item_mappings as string,
    location_mappings: row.location_mappings as string,
  };

  // Parse image prompts from dedicated columns first (prioritized over settings)
  try {
    if (row.image_prompts && typeof row.image_prompts === 'string' && row.image_prompts.trim()) {
      const parsedImagePrompts = JSON.parse(row.image_prompts as string);
      if (Array.isArray(parsedImagePrompts)) {
        story.imagePrompts = parsedImagePrompts;
      }
    }
  } catch (error) {
    console.warn('[transformBaserowToStory] Failed to parse image_prompts column:', error);
  }

  try {
    if (row.action_prompts && typeof row.action_prompts === 'string' && row.action_prompts.trim()) {
      const parsedActionPrompts = JSON.parse(row.action_prompts as string);
      if (Array.isArray(parsedActionPrompts)) {
        // Convert strings to ActionPrompt objects if needed
        if (parsedActionPrompts.length > 0 && typeof parsedActionPrompts[0] === 'string') {
          // If we have strings, convert them to ActionPrompt objects
          // We need to associate them with narration chunks if available
          if (story.narrationChunks && story.narrationChunks.length > 0) {
            // Create ActionPrompt objects with chunk information
            const actionPrompts: ActionPrompt[] = [];
            let promptIndex = 0;
            
            story.narrationChunks.forEach((chunk, chunkIndex) => {
              const duration = chunk.duration || 0;
              let promptCount: number;
              
              // Determine how many image prompts this chunk should have based on duration
              if (duration <= 5) promptCount = 1;
              else if (duration <= 10) promptCount = chunk.text.length > 100 ? 2 : 1;
              else if (duration <= 15) promptCount = 2;
              else promptCount = 3;
              
              // Create action prompts for this chunk
              for (let i = 0; i < promptCount && promptIndex < parsedActionPrompts.length; i++) {
                actionPrompts.push({
                  sceneIndex: promptIndex,
                  originalPrompt: '', // Will be updated when image prompts are available
                  actionDescription: parsedActionPrompts[promptIndex] || `Character performs action in scene ${promptIndex + 1}.`,
                  chunkText: chunk.text,
                  chunkId: chunk.id,
                  chunkIndex: chunkIndex
                });
                promptIndex++;
              }
            });
            
            story.actionPrompts = actionPrompts;
          } else {
            // If no narration chunks, create simple action prompts
            story.actionPrompts = parsedActionPrompts.map((actionDescription: string, index: number) => ({
              sceneIndex: index,
              originalPrompt: '',
              actionDescription: actionDescription,
              chunkText: 'Script chunk not available'
            }));
          }
        } else {
          // Check if these are already ActionPrompt objects or need conversion
          const firstItem = parsedActionPrompts[0];
          if (firstItem && typeof firstItem === 'object' && firstItem.hasOwnProperty('actionDescription')) {
            // Already proper ActionPrompt objects
            story.actionPrompts = parsedActionPrompts as ActionPrompt[];
          } else if (firstItem && typeof firstItem === 'object') {
            // Partial ActionPrompt objects, need to ensure all required fields
            story.actionPrompts = parsedActionPrompts.map((item: unknown, index: number) => {
              const obj = item as Record<string, unknown>;
              return {
                sceneIndex: typeof obj.sceneIndex === 'number' ? obj.sceneIndex : index,
                originalPrompt: typeof obj.originalPrompt === 'string' ? obj.originalPrompt : '',
                actionDescription: typeof obj.actionDescription === 'string' ? obj.actionDescription : '',
                chunkText: typeof obj.chunkText === 'string' ? obj.chunkText : '',
                chunkId: typeof obj.chunkId === 'string' ? obj.chunkId : undefined,
                chunkIndex: typeof obj.chunkIndex === 'number' ? obj.chunkIndex : undefined
              };
            });
          } else {
            // Unexpected format, create minimal ActionPrompt objects
            story.actionPrompts = parsedActionPrompts.map((actionDescription: string, index: number) => ({
              sceneIndex: index,
              originalPrompt: '',
              actionDescription: actionDescription,
              chunkText: 'Script chunk not available'
            }));
          }
        }
      }
    }
  } catch (error) {
    console.warn('[transformBaserowToStory] Failed to parse action_prompts column:', error);
  }



  // Parse settings JSON
  if (row.settings) {
    try {
      const settings = JSON.parse(row.settings as string);
      console.log('[transformBaserowToStory] Parsed settings:', {
        selectedTtsModel: settings.selectedTtsModel,
        selectedGoogleTtsModel: settings.selectedGoogleTtsModel,
        selectedGoogleVoiceId: settings.selectedGoogleVoiceId
      });
      story.narrationAudioDurationSeconds = settings.narrationAudioDurationSeconds;
      story.imageProvider = settings.imageProvider;
      story.aiProvider = settings.aiProvider;
      story.perplexityModel = settings.perplexityModel;
      story.googleScriptModel = settings.googleScriptModel;
      story.imagePromptsData = settings.imagePromptsData;
      story.generatedScript = settings.generatedScript; // Read generated script from settings
      story.detailsPrompts = settings.detailsPrompts; // Read details prompts from settings
      // Only read image prompts from settings if not available in dedicated columns
      if (!story.imagePrompts || story.imagePrompts.length === 0) {
        story.imagePrompts = settings.imagePrompts || []; // Read image prompts from settings as fallback
      }
      if (!story.actionPrompts || story.actionPrompts.length === 0) {
        story.actionPrompts = settings.actionPrompts || []; // Read action prompts from settings as fallback
      }
      story.audioGenerationService = settings.audioGenerationService; // Read TTS service used
      story.audioModel = settings.audioModel; // Read audio model used
      story.detailImageProvider = settings.detailImageProvider; // Read detail image provider
      story.detailImageModel = settings.detailImageModel; // Read detail image model
      story.sceneImageProvider = settings.sceneImageProvider; // Read scene image provider
      story.sceneImageModel = settings.sceneImageModel; // Read scene image model
      story.selectedTtsModel = settings.selectedTtsModel; // Read TTS model choice
      story.selectedGoogleTtsModel = settings.selectedGoogleTtsModel; // Read Google TTS model
      story.selectedGoogleVoiceId = settings.selectedGoogleVoiceId; // Read Google voice ID
      story.workflowStatus = settings.workflowStatus || 'wip'; // Read workflow status
      
      // YouTube optimization fields - fallback support for existing stories stored in JSON
      // If the dedicated columns are empty but JSON has data, use JSON data for migration
      if (!story.youtubeTitle && settings.youtubeTitle) {
        story.youtubeTitle = settings.youtubeTitle;
        console.log('[transformBaserowToStory] Migrated youtubeTitle from JSON settings');
      }
      if (!story.youtubeDescription && settings.youtubeDescription) {
        story.youtubeDescription = settings.youtubeDescription;
        console.log('[transformBaserowToStory] Migrated youtubeDescription from JSON settings');
      }
      if (!story.youtubeThumbnailPrompt && settings.youtubeThumbnailPrompt) {
        story.youtubeThumbnailPrompt = settings.youtubeThumbnailPrompt;
        console.log('[transformBaserowToStory] Migrated youtubeThumbnailPrompt from JSON settings');
      }
      if (!story.youtubeThumbnailImageUrl && settings.youtubeThumbnailImageUrl) {
        story.youtubeThumbnailImageUrl = settings.youtubeThumbnailImageUrl;
        console.log('[transformBaserowToStory] Migrated youtubeThumbnailImageUrl from JSON settings');
      }
      
      console.log('[transformBaserowToStory] Assigned to story:', {
        selectedTtsModel: story.selectedTtsModel,
        selectedGoogleTtsModel: story.selectedGoogleTtsModel,
        selectedGoogleVoiceId: story.selectedGoogleVoiceId,
        youtubeTitle: story.youtubeTitle,
        youtubeDescription: story.youtubeDescription,
        youtubeThumbnailPrompt: story.youtubeThumbnailPrompt
      });
    } catch (error) {
      console.warn('Failed to parse settings JSON:', error);
    }
  }

  return story;
}

/**
 * Save image prompts to dedicated Baserow columns
 */
export async function saveImagePromptsToBaserow(
  storyId: string, 
  userId: string, 
  imagePrompts: string[], 
  actionPrompts: string[]
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('[saveImagePromptsToBaserow] Saving prompts to dedicated columns:', {
      storyId,
      imagePromptsCount: imagePrompts.length,
      actionPromptsCount: actionPrompts.length
    });

    // Find the actual Baserow row ID - same logic as in getStory function
    let baserowRowId: string | null = null;
    let existingStory: Story | null = null;
    
    console.log('[saveImagePromptsToBaserow] Looking for story with ID:', storyId);
    
    // Always search by firebase_story_id first since that's what we typically have
    const stories = await baserowService.getStories(userId); // Only get stories for this user
    console.log('[saveImagePromptsToBaserow] Found', stories.length, 'stories for user:', userId);
    
    const matchingRow = stories.find((row: Record<string, unknown>) => row.firebase_story_id === storyId);
    
    if (matchingRow) {
      existingStory = transformBaserowToStory(matchingRow);
      baserowRowId = (matchingRow.id as number).toString(); // Store the actual Baserow row ID for updates
      console.log('[saveImagePromptsToBaserow] Found story by firebase_story_id, Baserow row ID:', baserowRowId);
    } else {
      // Fallback: try as Baserow row ID (for legacy compatibility)
      try {
        console.log('[saveImagePromptsToBaserow] Trying as Baserow row ID:', storyId);
        const row = await baserowService.getStory(storyId);
        if (row && row.user_id === userId) { // Security check here too
          existingStory = transformBaserowToStory(row);
          baserowRowId = storyId; // It's already a Baserow row ID
          console.log('[saveImagePromptsToBaserow] Found story by Baserow row ID');
        }
      } catch (error) {
        console.log('[saveImagePromptsToBaserow] Not found by Baserow row ID:', error instanceof Error ? error.message : error);
        // If both approaches fail, story doesn't exist
      }
    }
    
    if (!baserowRowId || !existingStory) {
      console.warn('[saveImagePromptsToBaserow] Story not found:', storyId);
      return { success: false, error: "Story not found. Cannot save image prompts." };
    }

    if (existingStory.userId !== userId) {
      console.warn('[saveImagePromptsToBaserow] Unauthorized access attempt:', { storyId, userId, storyUserId: existingStory.userId });
      return { success: false, error: "Unauthorized: You can only update your own stories." };
    }

    const updates = {
      image_prompts: JSON.stringify(imagePrompts),
      action_prompts: JSON.stringify(actionPrompts),
      updated_at: new Date().toISOString().split('T')[0] // Format as YYYY-MM-DD
    };

    await baserowService.updateStory(baserowRowId, updates);
    
    console.log('[saveImagePromptsToBaserow] Successfully saved prompts to dedicated columns');
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to save image prompts';
    
    // If columns don't exist, log warning but don't fail the operation
    if (errorMessage.includes('field') && errorMessage.includes('does not exist')) {
      console.warn('[saveImagePromptsToBaserow] Dedicated columns not available yet, skipping save to columns');
      return { success: true }; // Don't fail the operation
    }
    
    console.error('[saveImagePromptsToBaserow] Error saving prompts:', error);
    return { 
      success: false, 
      error: errorMessage
    };
  }
}

/**
 * Upload audio data URI to MinIO and return the URL
 */
async function uploadAudioToMinIO(audioDataUri: string, userId: string, storyId: string, filename: string): Promise<string> {
  let base64Data: string;
  let contentType: string;

  if (audioDataUri.startsWith('data:audio/mpeg;base64,')) {
    base64Data = audioDataUri.substring('data:audio/mpeg;base64,'.length);
    contentType = 'audio/mpeg';
  } else if (audioDataUri.startsWith('data:audio/wav;base64,')) {
    base64Data = audioDataUri.substring('data:audio/wav;base64,'.length);
    contentType = 'audio/wav';
  } else {
    throw new Error('Invalid audio data URI format.');
  }

  const audioBuffer = Buffer.from(base64Data, 'base64');
  const filePath = minioService.generateFilePath(userId, storyId, filename, 'audio');
  
  await minioService.uploadFile(filePath, audioBuffer, contentType);
  
  // Return a presigned URL that expires in 7 days (matching Firebase behavior)
  return await minioService.getSignedUrl(filePath, 7 * 24 * 60 * 60);
}

// Refresh MinIO URLs when they are expired
async function refreshMinIOUrl(url: string): Promise<string | null> {
  try {
    const { getValidMinIOUrl } = await import('@/utils/signedUrlGenerator');
    return await getValidMinIOUrl(url);
  } catch (error) {
    console.error('Error refreshing MinIO URL:', error);
    return null;
  }
}

export async function getStory(storyId: string, userId: string): Promise<{ success: boolean; data?: Story; error?: string }> {
  if (!userId) {
    console.warn("[getStory Action] Attempt to fetch story without userId.");
    return { success: false, error: "User not authenticated." };
  }

  try {
    // Find the story by searching through all stories for the firebase_story_id
    let story: Story | null = null;
    let baserowRowId: string | null = null;
    
    // Always search by firebase_story_id first since that's what we typically have
    const stories = await baserowService.getStories(userId); // Only get stories for this user
    const matchingRow = stories.find((row: Record<string, unknown>) => row.firebase_story_id === storyId);
    
    if (matchingRow) {
      story = transformBaserowToStory(matchingRow);
      baserowRowId = (matchingRow.id as number).toString(); // Store the actual Baserow row ID for updates
    } else {
      // Fallback: try as Baserow row ID (for legacy compatibility)
      try {
        const row = await baserowService.getStory(storyId);
        if (row && row.user_id === userId) { // Security check here too
          story = transformBaserowToStory(row);
          baserowRowId = storyId; // It's already a Baserow row ID
        }
      } catch {
        // If both approaches fail, story doesn't exist
      }
    }

    if (!story) {
      return { success: false, error: "Story not found." };
    }

    // Security check (redundant but safe)
    if (story.userId !== userId) {
      return { success: false, error: "Unauthorized: You can only access your own stories." };
    }

    let needsUpdate = false;
    const updates: Record<string, unknown> = {};

    // Refresh signed URLs for narration audio
    if (story.narrationAudioUrl) {
      const refreshedUrl = await refreshMinIOUrl(story.narrationAudioUrl);
      if (refreshedUrl && refreshedUrl !== story.narrationAudioUrl) {
        story.narrationAudioUrl = refreshedUrl;
        updates.narration_audio_url = refreshedUrl;
        needsUpdate = true;
      }
    }

    // Refresh signed URLs for narration chunks
    if (story.narrationChunks && Array.isArray(story.narrationChunks) && story.narrationChunks.length > 0) {
      let hasUpdatedChunks = false;
      const refreshedChunks = await Promise.all(story.narrationChunks.map(async (chunk) => {
        if (chunk && chunk.audioUrl) {
          const refreshedUrl = await refreshMinIOUrl(chunk.audioUrl);
          if (refreshedUrl && refreshedUrl !== chunk.audioUrl) {
            hasUpdatedChunks = true;
            return { ...chunk, audioUrl: refreshedUrl };
          }
        }
        return chunk;
      }));

      if (hasUpdatedChunks) {
        story.narrationChunks = refreshedChunks;
        updates.narration_chunks = JSON.stringify(refreshedChunks);
        needsUpdate = true;
      }
    }

    // Refresh signed URLs for generated images
    if (story.generatedImages && Array.isArray(story.generatedImages) && story.generatedImages.length > 0) {
      let hasUpdatedImages = false;
      const refreshedImages = await Promise.all(story.generatedImages.map(async (image) => {
        if (image && image.imageUrl) {
          const refreshedUrl = await refreshMinIOUrl(image.imageUrl);
          if (refreshedUrl && refreshedUrl !== image.imageUrl) {
            hasUpdatedImages = true;
            return { ...image, imageUrl: refreshedUrl };
          }
        }
        return image;
      }));

      if (hasUpdatedImages) {
        story.generatedImages = refreshedImages;
        updates.generated_images = JSON.stringify(refreshedImages);
        needsUpdate = true;
      }
    }

    // Refresh signed URLs for Spanish narration chunks
    if (story.spanishNarrationChunks && Array.isArray(story.spanishNarrationChunks) && story.spanishNarrationChunks.length > 0) {
      let hasUpdatedSpanishChunks = false;
      const refreshedSpanishChunks = await Promise.all(story.spanishNarrationChunks.map(async (chunk) => {
        if (chunk && chunk.audioUrl) {
          const refreshedUrl = await refreshMinIOUrl(chunk.audioUrl);
          if (refreshedUrl && refreshedUrl !== chunk.audioUrl) {
            hasUpdatedSpanishChunks = true;
            return { ...chunk, audioUrl: refreshedUrl };
          }
        }
        return chunk;
      }));

      if (hasUpdatedSpanishChunks) {
        story.spanishNarrationChunks = refreshedSpanishChunks;
        updates.spanish_narration_chunks = JSON.stringify(refreshedSpanishChunks);
        needsUpdate = true;
      }
    }

    // Refresh signed URLs for Romanian narration chunks
    if (story.romanianNarrationChunks && Array.isArray(story.romanianNarrationChunks) && story.romanianNarrationChunks.length > 0) {
      let hasUpdatedRomanianChunks = false;
      const refreshedRomanianChunks = await Promise.all(story.romanianNarrationChunks.map(async (chunk) => {
        if (chunk && chunk.audioUrl) {
          const refreshedUrl = await refreshMinIOUrl(chunk.audioUrl);
          if (refreshedUrl && refreshedUrl !== chunk.audioUrl) {
            hasUpdatedRomanianChunks = true;
            return { ...chunk, audioUrl: refreshedUrl };
          }
        }
        return chunk;
      }));

      if (hasUpdatedRomanianChunks) {
        story.romanianNarrationChunks = refreshedRomanianChunks;
        updates.romanian_narration_chunks = JSON.stringify(refreshedRomanianChunks);
        needsUpdate = true;
      }
    }

    // Update story in Baserow if URLs were refreshed (use the correct Baserow row ID)
    if (needsUpdate && baserowRowId) {
      try {
        updates.updated_at = new Date().toISOString().split('T')[0];
        await baserowService.updateStory(baserowRowId, updates);
        console.log(`[getStory Action] Refreshed and updated URLs for story ${storyId} (row ${baserowRowId})`);
      } catch (updateError) {
        console.error(`[getStory Action] Failed to update story ${storyId} with refreshed URLs:`, updateError);
      }
    }

    return { success: true, data: story };
    
  } catch (error) {
    console.error("[getStory Action] Error fetching story from Baserow:", error);
    let errorMessage = "Failed to fetch story.";
    
    const baserowError = error as BaserowErrorWithCode;
    if (baserowError && baserowError.code) {
      errorMessage = `Failed to fetch story (Baserow Error): ${baserowError.message} (Code: ${baserowError.code})`;
    } else if (error instanceof Error) {
      errorMessage = `Failed to fetch story: ${error.message}`;
    }
    
    return { success: false, error: errorMessage };
  }
}

export async function saveStory(storyData: Story, userId: string): Promise<{ success: boolean; storyId?: string; error?: string; data?: { narrationAudioUrl?: string } }> {
  if (!userId || typeof userId !== 'string' || userId.trim() === '') {
    return { success: false, error: "User not authenticated or user ID is invalid." };
  }

  const storyIdForPath = storyData.id || `story_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const processedStoryData = { ...storyData };
  let newNarrationUrl: string | undefined = undefined;

  // Handle audio upload to MinIO
  if (processedStoryData.narrationAudioUrl && processedStoryData.narrationAudioUrl.startsWith('data:audio/')) {
    try {
      const fileExtension = processedStoryData.narrationAudioUrl.startsWith('data:audio/wav') ? 'wav' : 'mp3';
      const defaultFilename = `uploaded_narration.${fileExtension}`;
      const storageUrl = await uploadAudioToMinIO(processedStoryData.narrationAudioUrl, userId, storyIdForPath, defaultFilename);
      processedStoryData.narrationAudioUrl = storageUrl;
      newNarrationUrl = storageUrl;
    } catch (uploadError: unknown) {
      let detailedErrorMessage = "Failed to upload narration audio";
      if (uploadError instanceof Error) {
        detailedErrorMessage += `: ${uploadError.message}`;
      } else {
        detailedErrorMessage += `: ${String(uploadError)}`;
      }
      return { success: false, error: detailedErrorMessage };
    }
  }

  // Set timestamps
  processedStoryData.updatedAt = new Date();
  if (!storyData.id && !processedStoryData.createdAt) {
    processedStoryData.createdAt = new Date();
  }

  try {
    if (storyData.id) {
      // Update existing story - need to find the Baserow row ID
      try {
        // First try to get by Baserow ID
        let baserowRowId: string | null = null;
        let existingStory: Story | null = null;
        
        try {
          const row = await baserowService.getStory(storyData.id);
          if (row) {
            baserowRowId = storyData.id; // It's already a Baserow row ID
            existingStory = transformBaserowToStory(row);
          }
        } catch {
          // If not found by Baserow ID, try by firebase_story_id
          const stories = await baserowService.getStories();
          const matchingRow = stories.find((row: Record<string, unknown>) => row.firebase_story_id === storyData.id);
          if (matchingRow) {
            baserowRowId = (matchingRow.id as number).toString(); // Use the Baserow row ID
            existingStory = transformBaserowToStory(matchingRow);
          }
        }
        
        if (!baserowRowId || !existingStory) {
          return { success: false, error: "Story not found. Cannot update." };
        }

        if (existingStory.userId !== userId) {
          return { success: false, error: "Unauthorized: You can only update your own stories." };
        }

        // Preserve original creation timestamp
        processedStoryData.createdAt = existingStory.createdAt;

        // Populate structured mappings if detailsPrompts are present but structured mappings are not
        if (processedStoryData.detailsPrompts && 
            (!processedStoryData.character_mappings || !processedStoryData.item_mappings || !processedStoryData.location_mappings)) {
          console.log('[saveStory] Populating structured mappings from detailsPrompts');
          const structuredMappings = populateStructuredMappingsFromPrompts(processedStoryData.detailsPrompts);
          processedStoryData.character_mappings = structuredMappings.character_mappings;
          processedStoryData.item_mappings = structuredMappings.item_mappings;
          processedStoryData.location_mappings = structuredMappings.location_mappings;
        }

        const baserowData = transformStoryToBaserow(processedStoryData);
        
        // Add retry logic for production network issues
        let retryCount = 0;
        const maxRetries = 3;
        
        while (retryCount <= maxRetries) {
          try {
            await baserowService.updateStory(baserowRowId, baserowData);
            break; // Success, exit retry loop
          } catch (updateError) {
            retryCount++;
            console.error(`[saveStory] Update attempt ${retryCount} failed:`, updateError);
            
            if (retryCount > maxRetries) {
              throw updateError; // Re-throw after max retries
            }
            
            // Wait before retry (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retryCount - 1)));
          }
        }
      } catch (updateError) {
        console.error('Error updating story:', updateError);
        return { success: false, error: `Failed to update story: ${updateError instanceof Error ? updateError.message : 'Unknown error'}` };
      }

      revalidatePath('/dashboard');
      revalidatePath(`/create-story?storyId=${storyData.id}`);
      return { 
        success: true, 
        storyId: storyData.id, 
        data: { narrationAudioUrl: newNarrationUrl || storyData.narrationAudioUrl } 
      };
    } else {
      // Create new story - first check for duplicates
      try {
        const existingStories = await baserowService.getStories(userId);
        const isDuplicate = existingStories.some((story: Record<string, unknown>) => 
          story.user_id === userId && 
          story.Title === processedStoryData.title?.trim() &&
          story.content === processedStoryData.userPrompt?.trim()
        );
        
        if (isDuplicate) {
          console.warn(`[saveStory] Duplicate story detected for user ${userId}: "${processedStoryData.title}"`);
          return { success: false, error: "A story with this title and content already exists." };
        }
      } catch (duplicateCheckError) {
        console.warn('[saveStory] Failed to check for duplicates, proceeding with creation:', duplicateCheckError);
        // Continue with creation if duplicate check fails
      }
      
      // Populate structured mappings if detailsPrompts are present but structured mappings are not
      if (processedStoryData.detailsPrompts && 
          (!processedStoryData.character_mappings || !processedStoryData.item_mappings || !processedStoryData.location_mappings)) {
        console.log('[saveStory] Populating structured mappings from detailsPrompts for new story');
        const structuredMappings = populateStructuredMappingsFromPrompts(processedStoryData.detailsPrompts);
        processedStoryData.character_mappings = structuredMappings.character_mappings;
        processedStoryData.item_mappings = structuredMappings.item_mappings;
        processedStoryData.location_mappings = structuredMappings.location_mappings;
      }
      
      const baserowData = transformStoryToBaserow(processedStoryData);
      baserowData.firebase_story_id = storyIdForPath; // Set the ID for new stories
      
      // Add retry logic for production network issues
      let retryCount = 0;
      const maxRetries = 3;
      
      while (retryCount <= maxRetries) {
        try {
          await baserowService.createStory(baserowData);
          break; // Success, exit retry loop
        } catch (createError) {
          retryCount++;
          console.error(`[saveStory] Create attempt ${retryCount} failed:`, createError);
          
          if (retryCount > maxRetries) {
            throw createError; // Re-throw after max retries
          }
          
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retryCount - 1)));
        }
      }
      
      revalidatePath('/dashboard');
      return { 
        success: true, 
        storyId: storyIdForPath, 
        data: { narrationAudioUrl: newNarrationUrl } 
      };
    }
  } catch (error) {
    let errorMessage = "Failed to save story.";
    const baserowError = error as BaserowErrorWithCode;
    
    if (baserowError && baserowError.code) {
      errorMessage = `Failed to save story (Baserow Error): ${baserowError.message} (Code: ${baserowError.code})`;
    } else if (error instanceof Error) {
      errorMessage = `Failed to save story: ${error.message}`;
    }
    
    return { success: false, error: errorMessage };
  }
}

export async function updateStoryTimeline(
  storyId: string,
  userId: string,
  timelineTracks: PageTimelineTrack[]
): Promise<{ success: boolean; error?: string }> {
  if (!userId || typeof userId !== 'string' || userId.trim() === '') {
    return { success: false, error: "User not authenticated or user ID is invalid." };
  }
  
  if (!storyId) {
    return { success: false, error: "Story ID is required to update the timeline." };
  }

  try {
    // Find the actual Baserow row ID - same logic as in saveStory and deleteStory
    let baserowRowId: string | null = null;
    let existingStory: Story | null = null;
    
    try {
      const row = await baserowService.getStory(storyId);
      if (row) {
        baserowRowId = storyId; // It's already a Baserow row ID
        existingStory = transformBaserowToStory(row);
      }
    } catch {
      // If not found by Baserow ID, try by firebase_story_id
      const stories = await baserowService.getStories();
      const matchingRow = stories.find((row: Record<string, unknown>) => row.firebase_story_id === storyId);
      if (matchingRow) {
        baserowRowId = (matchingRow.id as number).toString(); // Use the Baserow row ID
        existingStory = transformBaserowToStory(matchingRow);
      }
    }
    
    if (!baserowRowId || !existingStory) {
      return { success: false, error: "Story not found. Cannot update timeline." };
    }

    if (existingStory.userId !== userId) {
      return { success: false, error: "Unauthorized: You can only update the timeline of your own stories." };
    }

    const updates = {
      timeline_tracks: JSON.stringify(timelineTracks),
      updated_at: new Date().toISOString().split('T')[0]
    };

    await baserowService.updateStory(baserowRowId, updates);

    revalidatePath(`/assemble-video?storyId=${storyId}`);
    revalidatePath('/dashboard');
    return { success: true };
  } catch (error) {
    let errorMessage = "Failed to update story timeline.";
    const baserowError = error as BaserowErrorWithCode;
    
    if (baserowError && baserowError.code) {
      errorMessage = `Failed to update story timeline (Baserow Error): ${baserowError.message} (Code: ${baserowError.code})`;
    } else if (error instanceof Error) {
      errorMessage = `Failed to update story timeline: ${error.message}`;
    }
    
    return { success: false, error: errorMessage };
  }
}

export async function deleteStory(
  storyId: string,
  userId: string
): Promise<{ success: boolean; error?: string }> {
  if (!userId || typeof userId !== 'string' || userId.trim() === '') {
    return { success: false, error: "User not authenticated or user ID is invalid." };
  }
  
  if (!storyId) {
    return { success: false, error: "Story ID is required to delete the story." };
  }

  try {
    // Find the actual Baserow row ID - same logic as in saveStory
    let baserowRowId: string | null = null;
    let existingStory: Story | null = null;
    
    try {
      const row = await baserowService.getStory(storyId);
      if (row) {
        baserowRowId = storyId; // It's already a Baserow row ID
        existingStory = transformBaserowToStory(row);
      }
    } catch {
      // If not found by Baserow ID, try by firebase_story_id
      const stories = await baserowService.getStories();
      const matchingRow = stories.find((row: Record<string, unknown>) => row.firebase_story_id === storyId);
      if (matchingRow) {
        baserowRowId = (matchingRow.id as number).toString(); // Use the Baserow row ID
        existingStory = transformBaserowToStory(matchingRow);
      }
    }
    
    if (!baserowRowId || !existingStory) {
      return { success: false, error: "Story not found." };
    }

    if (existingStory.userId !== userId) {
      return { success: false, error: "Unauthorized: You can only delete your own stories." };
    }

    // Delete associated files from MinIO (non-blocking)
    try {
      console.log(`[deleteStory] Attempting to clean up storage files for story: ${storyId}`);
      const files = await minioService.listFiles(`users/${userId}/stories/${storyId}/`);
      if (files.length > 0) {
        await Promise.all(files.map(filePath => minioService.deleteFile(filePath)));
        console.log(`[deleteStory] Successfully deleted ${files.length} storage files`);
      } else {
        console.log(`[deleteStory] No storage files found to delete`);
      }
    } catch (storageError) {
      console.warn('[deleteStory] Failed to delete some storage files (this does not affect story deletion):', storageError);
      // Continue with story deletion even if storage cleanup fails
    }

    await baserowService.deleteStory(baserowRowId);

    revalidatePath('/dashboard');
    revalidatePath(`/create-story?storyId=${storyId}`);
    return { success: true };
  } catch (error) {
    let errorMessage = "Failed to delete story.";
    const baserowError = error as BaserowErrorWithCode;
    
    if (baserowError && baserowError.code) {
      errorMessage = `Failed to delete story (Baserow Error): ${baserowError.message} (Code: ${baserowError.code})`;
    } else if (error instanceof Error) {
      errorMessage = `Failed to delete story: ${error.message}`;
    }
    
    return { success: false, error: errorMessage };
  }
}

export async function getUserStories(userId: string): Promise<{ success: boolean; data?: Story[]; error?: string }> {
  if (!userId) {
    return { success: false, error: "User ID is required" };
  }

  try {
    const rows = await baserowService.getStories(userId);
    const stories = rows.map(transformBaserowToStory);
    
    // Refresh expired MinIO URLs for all stories
    const storiesWithRefreshedUrls = await Promise.all(
      stories.map(async (story) => {
        // Refresh image URLs if they exist and are expired
        if (story.generatedImages && Array.isArray(story.generatedImages) && story.generatedImages.length > 0) {
          const refreshedImages = await Promise.all(story.generatedImages.map(async (image) => {
            if (image && image.imageUrl) {
              const refreshedUrl = await refreshMinIOUrl(image.imageUrl);
              if (refreshedUrl && refreshedUrl !== image.imageUrl) {
                return { ...image, imageUrl: refreshedUrl };
              }
            }
            return image;
          }));
          story.generatedImages = refreshedImages;
        }
        
        return story;
      })
    );
    
    // Return stories without sorting them here to let the dashboard handle sorting
    return { success: true, data: storiesWithRefreshedUrls };
  } catch (error) {
    console.error('Error fetching user stories:', error);
    return { success: false, error: 'Failed to fetch stories' };
  }
}

export async function cleanupBrokenImages(storyId: string): Promise<{ success: boolean; error?: string }> {
  try {
    // Find the actual Baserow row ID - same logic as in other functions
    let baserowRowId: string | null = null;
    let story: Record<string, unknown> | null = null;
    
    try {
      const row = await baserowService.getStory(storyId);
      if (row) {
        baserowRowId = storyId; // It's already a Baserow row ID
        story = row;
      }
    } catch {
      // If not found by Baserow ID, try by firebase_story_id
      const stories = await baserowService.getStories();
      const matchingRow = stories.find((row: Record<string, unknown>) => row.firebase_story_id === storyId);
      if (matchingRow) {
        baserowRowId = (matchingRow.id as number).toString(); // Use the Baserow row ID
        story = matchingRow;
      }
    }
    
    if (!baserowRowId || !story) {
      return { success: false, error: "Story not found" };
    }

    let updated = false;
    const updates: Record<string, unknown> = {};

    // Parse and clean generated images
    if (story.generated_images) {
      try {
        const generatedImages = JSON.parse(story.generated_images as string);
        if (Array.isArray(generatedImages)) {
          const cleanGeneratedImages = generatedImages.filter((img: { imageUrl?: string }) => {
            if (img && img.imageUrl && typeof img.imageUrl === 'string') {
              if (img.imageUrl.includes('aicdn.picsart.com')) { return false; }
              if (img.imageUrl.includes('.mp3')) { return false; }
            }
            return true;
          });
          
          if (cleanGeneratedImages.length !== generatedImages.length) {
            updates.generated_images = JSON.stringify(cleanGeneratedImages);
            updated = true;
          }
        }
      } catch (parseError) {
        console.warn('Failed to parse generated_images JSON:', parseError);
      }
    }

    if (updated) {
      updates.updated_at = new Date().toISOString().split('T')[0];
      await baserowService.updateStory(baserowRowId, updates);
    }

    return { success: true };
  } catch (error: unknown) {
    let errorMessage = "Failed to cleanup broken images";
    if (error instanceof Error) {
      errorMessage += `: ${error.message}`;
    } else {
      errorMessage += `: ${String(error)}`;
    }
    return { success: false, error: errorMessage };
  }
}

/**
 * Update structured entity mappings in Baserow when users edit character/item/location prompts
 * This function updates the dedicated Baserow columns (character_mappings, item_mappings, location_mappings)
 * with the latest structured mappings derived from the detailsPrompts
 */
export async function updateStructuredEntityMappingsInBaserow(
  storyId: string,
  userId: string,
  detailsPrompts: Record<string, unknown> | { characterPrompts?: string; itemPrompts?: string; locationPrompts?: string }
): Promise<{ success: boolean; error?: string }> {
  try {
    // Find the actual Baserow row ID
    let baserowRowId: string | null = null;
    let existingStory: Story | null = null;
    
    try {
      const row = await baserowService.getStory(storyId);
      if (row) {
        baserowRowId = storyId; // It's already a Baserow row ID
        existingStory = transformBaserowToStory(row);
      }
    } catch {
      // If not found by Baserow ID, try by firebase_story_id
      const stories = await baserowService.getStories();
      const matchingRow = stories.find((row: Record<string, unknown>) => row.firebase_story_id === storyId);
      if (matchingRow) {
        baserowRowId = (matchingRow.id as number).toString(); // Use the Baserow row ID
        existingStory = transformBaserowToStory(matchingRow);
      }
    }
    
    if (!baserowRowId || !existingStory) {
      return { success: false, error: "Story not found. Cannot update entity mappings." };
    }

    if (existingStory.userId !== userId) {
      return { success: false, error: "Unauthorized: You can only update your own stories." };
    }

    // Generate structured mappings from the updated detailsPrompts
    const structuredMappings = populateStructuredMappingsFromPrompts(detailsPrompts);
    
    // Prepare updates for Baserow
    const updates = {
      character_mappings: structuredMappings.character_mappings,
      item_mappings: structuredMappings.item_mappings,
      location_mappings: structuredMappings.location_mappings,
      updated_at: new Date().toISOString().split('T')[0] // Format as YYYY-MM-DD
    };

    // Update the story in Baserow
    await baserowService.updateStory(baserowRowId, updates);
    
    console.log('[updateStructuredEntityMappingsInBaserow] Successfully updated entity mappings in Baserow');
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to update entity mappings';
    console.error('[updateStructuredEntityMappingsInBaserow] Error updating entity mappings:', error);
    return { 
      success: false, 
      error: errorMessage
    };
  }
}

/**
 * Fix character types in Baserow for existing stories
 * This function updates character mappings to ensure they have correct animal types
 * instead of generic "character" types
 */
export async function fixCharacterTypesInBaserow(
  storyId: string,
  userId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Find the actual Baserow row ID
    let baserowRowId: string | null = null;
    let existingStory: Story | null = null;
    
    try {
      const row = await baserowService.getStory(storyId);
      if (row) {
        baserowRowId = storyId; // It's already a Baserow row ID
        existingStory = transformBaserowToStory(row);
      }
    } catch {
      // If not found by Baserow ID, try by firebase_story_id
      const stories = await baserowService.getStories();
      const matchingRow = stories.find((row: Record<string, unknown>) => row.firebase_story_id === storyId);
      if (matchingRow) {
        baserowRowId = (matchingRow.id as number).toString(); // Use the Baserow row ID
        existingStory = transformBaserowToStory(matchingRow);
      }
    }
    
    if (!baserowRowId || !existingStory) {
      return { success: false, error: "Story not found. Cannot fix character types." };
    }

    if (existingStory.userId !== userId) {
      return { success: false, error: "Unauthorized: You can only update your own stories." };
    }

    // Check if we have structured character mappings that need fixing
    if (existingStory.character_mappings) {
      let characterMappings;
      try {
        characterMappings = JSON.parse(existingStory.character_mappings);
      } catch (parseError) {
        console.error('[fixCharacterTypesInBaserow] Error parsing character mappings:', parseError);
        return { success: false, error: "Failed to parse character mappings" };
      }

      let mappingsUpdated = false;
      
      // Fix character types that are incorrectly set to "character"
      for (const [placeholder, entityData] of Object.entries(characterMappings)) {
        const entity = entityData as { name: string; type: string; description: string };
        // Check if type is "character" but we can extract a better type
        if (entity.type === 'character') {
          const extractedType = extractAnimalType(entity.name.toLowerCase(), entity.description.toLowerCase());
          if (extractedType && extractedType !== 'character') {
            entity.type = extractedType;
            mappingsUpdated = true;
            console.log(`[fixCharacterTypesInBaserow] Fixed type for ${placeholder}: character -> ${extractedType}`);
          }
        }
      }

      // If we updated any mappings, save them back to Baserow
      if (mappingsUpdated) {
        const updates = {
          character_mappings: JSON.stringify(characterMappings, null, 2),
          updated_at: new Date().toISOString().split('T')[0] // Format as YYYY-MM-DD
        };

        // Update the story in Baserow
        await baserowService.updateStory(baserowRowId, updates);
        
        console.log('[fixCharacterTypesInBaserow] Successfully fixed character types in Baserow');
        return { success: true };
      } else {
        console.log('[fixCharacterTypesInBaserow] No character types needed fixing');
        return { success: true };
      }
    }

    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to fix character types';
    console.error('[fixCharacterTypesInBaserow] Error fixing character types:', error);
    return { 
      success: false, 
      error: errorMessage
    };
  }
}
