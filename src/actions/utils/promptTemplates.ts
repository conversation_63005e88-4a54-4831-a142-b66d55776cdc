/**
 * Shared prompt templates for story generation
 */

export const titlePromptTemplate = 'You are an expert at creating catchy and concise titles for stories.\n' +
    'Based on the user\'s story prompt, generate a short title (ideally 3-7 words, maximum 10 words) that captures the essence of the story.\n' +
    'User Prompt: "{{userPrompt}}"\n' +
    'Generated Title:';

export const scriptPromptTemplate = 'Write a complete story based on the user\'s prompt. This story will be narrated as an animated video.\n' +
    'Requirements:\n' +
    '- Write ONLY the story content itself, no production instructions, camera directions, or narrator cues\n' +
    '- Write in third person narrative style, as if telling a story to someone\n' +
    '- Make it engaging for both children and adults\n' +
    '- Follow the themes, characters, and story elements from the prompt\n' +
    '- Length: approximately 300-500 words\n' +
    '- Do not include any stage directions, narrator instructions, or formatting like "Scene 1:", "Camera:", etc.\n' +
    '- Write it as pure storytelling prose that flows naturally\n\n' +
    'User Prompt: {{{prompt}}}\n\n' +
    'Story:';

export const characterPromptsPromptTemplate = 'You are an expert prompt engineer specializing in creating descriptions for text-to-image AI models (like DALL-E, Midjourney, or Flux Dex model).\n' +
    'Based on the following story script, generate detailed visual descriptions for the main characters, key items, and important locations.\n' +
    'These descriptions will be used as an AI image generator to create visuals for the story.\n\n' +
    '{{#if stylePrompt}}\n' +
    '**ARTISTIC STYLE REQUIREMENTS:**\n' +
    'Incorporate these style characteristics into all descriptions: {{{stylePrompt}}}\n' +
    'Ensure that character, item, and location descriptions align with this artistic style while maintaining their unique features.\n' +
    '{{/if}}\n\n' +
    'Script:\n' +
    '{{{script}}}\n\n' +
    'Instructions for output:\n' +
    '1.  For each category (Characters, Items, Locations), provide a heading (e.g., "Character Prompts:", "Item Prompts:", "Location Prompts:"). This heading MUST be part of the string for that category and appear at the very beginning of that category\'s section.\n' +
    '2.  Under each heading, list the entities. For each entity:\n' +
    '    *   **First line:** The name of the character, item, or location, followed immediately by " - " and then the @placeholder (e.g., "Rosie Recycle - @RosieRecycle", "Old Man Grumbles - @OldManGrumbles", "The Magic Sword - @TheMagicSword").\n' +
    '    *   **Subsequent lines:** Starting on the line immediately following the name, provide a detailed visual description suitable for a text-to-image model. This description MUST:\n' +
    '        *   Be entirely in **lowercase**.\n' +
    '        *   Be a **single sentence**.\n' +
    '        *   **Not end** with any punctuation marks like \'.\', \'?\', or \'!\'.\n' +
    '        *   **MANDATORY for characters**: Include specific physical traits for consistency:\n' +
    '            - Hair color and style (e.g., "brown hair", "blonde hair", "curly red hair", "long black hair")\n' +
    '            - Eye color (e.g., "blue eyes", "green eyes", "brown eyes")\n' +
    '            - Skin tone if relevant (e.g., "pale skin", "tan skin", "dark skin")\n' +
    '            - Age descriptors (e.g., "young girl", "elderly man", "teenage boy")\n' +
    '            - Key identifying features (clothing, accessories, distinctive marks)\n' +
    '            - DO NOT include facial expressions (smiles, frowns, worried looks, etc.) as these will be contextual to each scene\n' +
    '        *   Focus on visual attributes: appearance, attire, textures, colors, age, specific features, and any other visual details. Be descriptive and evocative. For characters, avoid permanent facial expressions or moods as these should be contextual to each scene.\n' +
    '3.  Ensure **exactly one blank line** separates each complete entity\'s entry (name + description) from the next entity within the same category. Do not use more than one blank line.\n\n' +
    'Example of desired output format and style (the content below is an example of the style and formatting you should follow, generate your own content based on the script):\n\n' +
    'Character Prompts:\n' +
    'Ember - @Ember\n' +
    'a tiny house cat-sized dragon with dull smoky grey scales, large hopeful bright orange eyes, small crumpled wings, blunt claws and tiny teeth\n\n' +
    'Ignis - @Ignis\n' +
    'an ancient wise dragon with cooled lava-colored scales cracked and weathered with age, glowing inner fire eyes, long braided beard with glittering obsidian beads, carrying a gnarled staff of petrified wood\n\n' +
    'Rosie Recycle - @RosieRecycle\n' +
    'a young girl with curly brown hair and bright green eyes, wearing goggles made from repurposed clear soda bottles, a cape fashioned from colorful recycled newspapers, pale skin and her outfit adorned with recycling symbols\n\n' +
    'Item Prompts:\n' +
    'Gnarled Staff - @GnarledStaff\n' +
    'a staff made of dark petrified wood gnarled and twisted with age it might have a faintly glowing crystal or ancient rune carved at its tip emitting a soft light\n\n' +
    'Obsidian Beads - @ObsidianBeads\n' +
    'small polished beads of pure black obsidian reflecting light with a glassy sheen they could be intricately braided into a character\'s beard or hair or part of a necklace\n\n' +
    'Location Prompts:\n' +
    'Desolate Village - @DesolateVillage\n' +
    'a small somber village with simple run-down huts made of rough-hewn wood and deteriorating thatch the surrounding landscape is barren and dusty perhaps with a few dead trees a sense of gloom and despair hangs heavy in the air colors are muted primarily browns greys and faded earth tones\n\n' +
    'Volcanic Peak - @VolcanicPeak\n' +
    'a towering jagged mountain its peak perpetually wreathed in thick dark smoke and a faint ominous red glow from within the slopes are steep and treacherous covered in loose scree sharp volcanic rock and patches of grey ash no vegetation is visible\n\n' +
    'Now, generate the character, item, and location prompts based on the provided script, adhering strictly to the format, style, and level of detail exemplified above. For characters, ensure you include specific physical traits (hair color/style, eye color, skin tone, age) for consistency across image generations. Use lowercase, single-sentence, no-punctuation-ending descriptions.\n';

export const imagePromptsPromptTemplate = `You are an expert at creating detailed image prompts optimized for various AI image generation models.
Your goal is to generate prompts that VISUALIZE specific NARRATION CHUNKS from a story.

**REFERENCES (Use these for @EntityName placeholders):**
CHARACTER REFERENCE:
{{{characterPrompts}}}

LOCATION REFERENCE:
{{{locationPrompts}}}

ITEM REFERENCE:
{{{itemPrompts}}}

**FULL STORY SCRIPT (for context):**
{{{script}}}

**INSTRUCTIONS FOR IMAGE PROMPT GENERATION:**

⚠️  **CRITICAL RULE**: NEVER use vague group references like "friends", "the group", "companions", "others", etc. ALWAYS use specific @CharacterName placeholders for every character mentioned.

{{#if chunksData}}
**SOUND CHUNK CORRELATION MODE:**
You MUST generate prompts that DIRECTLY VISUALIZE the content of EACH narration chunk provided below.
For each chunk, you need to generate a specific number of image prompts as indicated.

{{#each chunksData}}
**Narration Chunk {{@index}} (Duration: {{duration}}s, Required prompts: {{promptCount}}):**
"{{text}}"

**For THIS CHUNK, generate {{promptCount}} image prompt(s). Each prompt MUST:**
1.  **Visualize THIS CHUNK's content**: The image should depict characters, actions, and settings explicitly mentioned or clearly implied in THIS narration chunk.
2.  **Include a SPECIFIC Location**: Use an @LocationName from the LOCATION REFERENCE. If no location is directly mentioned in the chunk, infer the most logical @LocationName based on the chunk's content, the overall story script, and available location references. DO NOT invent new locations; use only those in the LOCATION REFERENCE.
3.  **Follow Prompt Structure**: "[Camera shot, e.g., Wide shot, Close-up] of @CharacterName [action/emotion/pose, e.g., looking thoughtful, running quickly] in @LocationName. [Interaction with @ItemName if relevant, e.g., holding @MagicWand]. [Lighting/mood, e.g., Sunny morning, Dark and stormy night]. [Key visual details from THIS narration chunk]."
    *   Example: "Eye-level medium shot of @Rusty trotting through @ForestPath in @WhisperingWoods. He is sniffing the ground curiously while @Bella and @Max watch from behind. Morning light filters through the canopy."
4.  **Use @Placeholders Correctly**: ONLY use @placeholders for entities listed in the CHARACTER, LOCATION, and ITEM REFERENCE sections. Convert entity names to PascalCase for @references (e.g., "Old Man Grumbles" becomes @OldManGrumbles). Do NOT include descriptions alongside @placeholders; they will be expanded automatically.
5.  **AVOID VAGUE REFERENCES**: NEVER use generic terms like "her friends", "his companions", "other characters", "the group", "everyone", "the four friends", "the other friends", "friends", etc. Instead, ALWAYS use specific @CharacterName placeholders. For example:
    *   BAD: "looking down towards her friends"
    *   GOOD: "looking down towards @Shelly, @Ollie, and @Stella"
    *   BAD: "the other animals gather around"
    *   GOOD: "@Rabbit, @Squirrel, and @Bear gather around"
    *   BAD: "The four friends are visible in the background"
    *   GOOD: "@Barnaby, @Ollie, @Stella, and @Luna are visible in the background"
    *   BAD: "The other friends are playing instruments"
    *   GOOD: "@Stella, @Ollie, and @Luna are playing instruments"
    *   CRITICAL: If you reference multiple characters, you MUST name each one individually with their @placeholder
6.  **No Style Descriptors**: ABSOLUTELY DO NOT include artistic style descriptors (like "3D rendered", "cartoon style", "photorealistic", "watercolor"). Style is handled separately.
7.  **Natural Language**: Write prompts as if describing a scene to a human. Use present tense.
---
{{/each}}

**ALSO GENERATE CORRESPONDING ACTION PROMPTS:**
For EACH image prompt you create above, also generate a simple action description for animation purposes.
Action prompts should be:
- Simple, clear descriptions of character movements/actions in that specific scene.
- Focus on physical actions and use descriptive character information directly from the CHARACTER REFERENCE (e.g., "the small wise-looking young owl with large intelligent amber-yellow eyes blinks slowly", "the tiny young field mouse with soft brown fur scratches his head").
- NEVER use @CharacterName placeholders in action prompts. Instead, use the descriptive character information from the CHARACTER REFERENCE.
- Keep them concise and animation-focused.
- Example: Instead of "@Rusty is walking", generate "the energetic young otter with sleek dark brown fur is walking confidently".

{{else}}
**FALLBACK MODE (No narration chunks provided):**
Analyze the full STORY SCRIPT and identify {{numImages}} key scenes that need visualization.
For each scene, generate one image prompt and one corresponding action prompt, following all the rules above (especially including a @LocationName, adhering to the prompt structure, and AVOIDING vague references by using specific @CharacterName placeholders in image prompts, but descriptive character terms in action prompts).
{{/if}}

**OUTPUT FORMAT (Strict JSON):**
Return your response as a JSON object with two keys:
1.  "imagePrompts": An array of strings, where each string is an image prompt. The total number of image prompts must be exactly {{numImages}}.
2.  "actionPrompts": An array of strings, where each string is an action prompt corresponding to the image prompt at the same index. The total number of action prompts must also be exactly {{numImages}}.
`;

export const scriptChunksPromptTemplate = 'You are a movie director and script editor who thinks visually. Your task is to split the following story script into meaningful visual scenes/chunks. Each chunk will have a corresponding image generated and narration audio, so think like you\'re creating an animated storybook.\n\n' +
    'Think like a movie director analyzing a script:\n' +
    '- What would each scene look like visually?\n' +
    '- Where are the natural visual transitions?\n' +
    '- What moments need their own "frame" or "shot"?\n' +
    '- How can you group sentences that paint the same visual picture?\n\n' +
    'CRITICAL REQUIREMENTS:\n' +
    '1. COMPLETE SENTENCE INTEGRITY: NEVER split in the middle of a sentence. Each chunk must contain only complete, full sentences.\n' +
    '2. COMPLETE COVERAGE: Every single word, sentence, and detail from the original script MUST appear in the chunks.\n' +
    '3. VISUAL COHERENCE: Group sentences that form a coherent visual scene. Think about what the camera would see.\n' +
    '4. LOGICAL FLOW: Preserve the narrative flow and pacing. Don\'t break up connected actions or dialogue.\n' +
    '5. PRACTICAL LENGTH: Aim for chunks that will be 15-45 seconds when narrated (roughly 30-120 words each).\n' +
    '6. CHARACTER CONSISTENCY: Keep dialogue and actions of the same character together when they happen in the same scene.\n' +
    '7. SCENE BOUNDARIES: Split at natural scene transitions, location changes, or significant time jumps.\n' +
    '8. DIALOGUE HANDLING: If there\'s dialogue, keep related speech together unless there\'s a clear visual transition.\n' +
    '9. ANALYZE FULL STORY: Read the entire script first to understand the complete visual flow before splitting.\n\n' +
    'Script to split:\n' +
    '{{{script}}}\n\n' +
    'Return your response as a JSON object with a single key "scriptChunks". The value of "scriptChunks" MUST be an array of strings, where each string is one of the generated script chunks. Do not include numbering or any other formatting within the chunk strings themselves.\n' +
    'Example of a good split for a segment:\n' +
    'Original: "Lilly\'s eyes sparkled. \'Does the Rainbow Route have puddles?!\' \'Oh, yes,\' Mama Duck chuckled, \'plenty of puddles. But it\'s also full of surprises.\'"\n' +
    'Split into:\n' +
    '- "Lilly\'s eyes sparkled. \'Does the Rainbow Route have puddles?!\'"\n' +
    '- "\'Oh, yes,\' Mama Duck chuckled, \'plenty of puddles. But it\'s also full of surprises.\'"\n';

export const spanishTranslationPromptTemplate = `You are a professional translator specializing in English to Spanish translation for children's stories and animated content.

Your task is to translate the provided English text chunks into natural, engaging Spanish that maintains the story's tone and appeal for young audiences.

**Key Requirements:**
1. **Natural Flow**: Translate for natural Spanish rhythm and cadence suitable for narration
2. **Child-Friendly**: Use vocabulary and expressions appropriate for children aged 4-12
3. **Emotional Tone**: Preserve the emotional impact and excitement of the original story
4. **Cultural Adaptation**: Make culturally appropriate adjustments when needed for Spanish-speaking audiences
5. **Character Voices**: Maintain distinct personality in dialogue and character descriptions
6. **Storytelling Style**: Keep the narrative engaging and easy to follow when spoken aloud

**Input Format:** You will receive an array of English text chunks from a story.

**Output Format:** Return a JSON object with a single key "translatedChunks" containing an array of Spanish translations. Each translation should correspond to the input chunk at the same array index.

**Example:**
Input chunks: ["Once upon a time, there was a brave little mouse.", "The mouse loved to explore the garden."]
Expected output: {"translatedChunks": ["Había una vez un ratoncito muy valiente.", "Al ratoncito le encantaba explorar el jardín."]}

Text chunks to translate:
{{{chunks}}}`;

export const spanishTranslationPromptTemplateGemini = `Translate the following English text chunks into Spanish for children's stories. Maintain natural Spanish flow, emotional tone, and cultural appropriateness.

Return as JSON with "translatedChunks" array.

English chunks: {{{chunks}}}`;

export const romanianTranslationPromptTemplate = `You are a professional translator specializing in English to Romanian translation for children's stories and animated content.

Your task is to translate the provided English text chunks into natural, engaging Romanian that maintains the story's tone and appeal for young audiences.

**Key Requirements:**
1. **Natural Flow**: Translate for natural Romanian rhythm and cadence suitable for narration
2. **Child-Friendly**: Use vocabulary and expressions appropriate for children aged 4-12
3. **Emotional Tone**: Preserve the emotional impact and excitement of the original story
4. **Cultural Adaptation**: Make culturally appropriate adjustments when needed for Romanian-speaking audiences
5. **Character Voices**: Maintain distinct personality in dialogue and character descriptions
6. **Storytelling Style**: Keep the narrative engaging and easy to follow when spoken aloud

**Input Format:** You will receive an array of English text chunks from a story.

**Output Format:** Return a JSON object with a single key "translatedChunks" containing an array of Romanian translations. Each translation should correspond to the input chunk at the same array index.

**Example:**
Input chunks: ["Once upon a time, there was a brave little mouse.", "The mouse loved to explore the garden."]
Expected output: {"translatedChunks": ["A fost odată un șoricel mic și curajos.", "Șoricelului îi plăcea să exploreze grădina."]}

Text chunks to translate:
{{{chunks}}}`;

export const romanianTranslationPromptTemplateGemini = `Translate the following English text chunks into Romanian for children's stories. Maintain natural Romanian flow, emotional tone, and cultural appropriateness.

Return as JSON with "translatedChunks" array.

English chunks: {{{chunks}}}`;
