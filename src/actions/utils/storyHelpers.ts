/**
 * Utility functions for story processing
 */

// Types for structured entity mappings
export interface EntityMapping {
    name: string;
    type: string;
    description: string;
}

export interface StructuredEntityMappings {
    [placeholder: string]: EntityMapping;
}

/**
 * Transforms action prompts for video generation
 * Converts "@CharacterName" references to simple words for better AI understanding
 * @deprecated Use transformActionPromptWithStoryData instead for more accurate conversion
 */
export function transformActionPromptForVideo(prompt: string): string {
    if (!prompt) return prompt;

    // Transform @references to simple character names
    // Example: "@OllieOtter" -> "Otter", "@SplashDuck" -> "<PERSON>"
    const transformedPrompt = prompt
        // Handle specific character patterns first
        .replace(/@(\w*[Oo]tter\w*)/g, 'Otter')
        .replace(/@(\w*[Dd]uck\w*)/g, 'Duck')
        .replace(/@(\w*[Ff]rog\w*)/g, 'Frog')
        .replace(/@(\w*[Bb]ear\w*)/g, 'Bear')
        .replace(/@(\w*[Cc]at\w*)/g, 'Cat')
        .replace(/@(\w*[Dd]og\w*)/g, 'Dog')
        .replace(/@(\w*[Bb]ird\w*)/g, 'Bird')
        .replace(/@(\w*[Ff]ish\w*)/g, 'Fish')
        .replace(/@(\w*[Ll]ion\w*)/g, 'Lion')
        .replace(/@(\w*[Tt]iger\w*)/g, 'Tiger')
        .replace(/@(\w*[Ee]lephant\w*)/g, 'Elephant')
        .replace(/@(\w*[Mm]ouse\w*)/g, 'Mouse')
        .replace(/@(\w*[Rr]abbit\w*)/g, 'Rabbit')
        .replace(/@(\w*[Hh]orse\w*)/g, 'Horse')
        .replace(/@(\w*[Pp]ig\w*)/g, 'Pig')
        .replace(/@(\w*[Ss]heep\w*)/g, 'Sheep')
        .replace(/@(\w*[Cc]ow\w*)/g, 'Cow')
        .replace(/@(\w*[Gg]oat\w*)/g, 'Goat')
        // Handle location/item references
        .replace(/@(\w*[Pp]ond\w*)/g, 'pond')
        .replace(/@(\w*[Ll]ake\w*)/g, 'lake')
        .replace(/@(\w*[Ff]orest\w*)/g, 'forest')
        .replace(/@(\w*[Tt]ree\w*)/g, 'tree')
        .replace(/@(\w*[Hh]ouse\w*)/g, 'house')
        .replace(/@(\w*[Cc]astle\w*)/g, 'castle')
        .replace(/@(\w*[Bb]ridge\w*)/g, 'bridge')
        .replace(/@(\w*[Rr]iver\w*)/g, 'river')
        .replace(/@(\w*[Mm]ountain\w*)/g, 'mountain')
        .replace(/@(\w*[Bb]each\w*)/g, 'beach')
        // Generic fallback: extract the main word from compound names
        .replace(/@[A-Z][a-z]*([A-Z][a-z]+)/g, '$1') // CamelCase -> last word
        .replace(/@(\w+)/g, '$1'); // Any remaining @ references

    return transformedPrompt;
}

/**
 * Parse structured entity mappings from JSON columns (new format)
 */
export function parseStructuredEntityMappings(storyData: Record<string, unknown>): {
    characters: StructuredEntityMappings;
    items: StructuredEntityMappings;
    locations: StructuredEntityMappings;
} {
    const characters: StructuredEntityMappings = {};
    const items: StructuredEntityMappings = {};
    const locations: StructuredEntityMappings = {};

    try {
        // Parse character mappings
        if (storyData.character_mappings) {
            const parsed = typeof storyData.character_mappings === 'string' 
                ? JSON.parse(storyData.character_mappings) 
                : storyData.character_mappings;
            Object.assign(characters, parsed);
        }

        // Parse item mappings
        if (storyData.item_mappings) {
            const parsed = typeof storyData.item_mappings === 'string' 
                ? JSON.parse(storyData.item_mappings) 
                : storyData.item_mappings;
            Object.assign(items, parsed);
        }

        // Parse location mappings
        if (storyData.location_mappings) {
            const parsed = typeof storyData.location_mappings === 'string' 
                ? JSON.parse(storyData.location_mappings) 
                : storyData.location_mappings;
            Object.assign(locations, parsed);
        }
    } catch (error) {
        console.warn('[parseStructuredEntityMappings] Error parsing structured mappings:', error);
    }

    return { characters, items, locations };
}

/**
 * Convert legacy detailsPrompts to structured mappings (for backwards compatibility)
 */
export function convertLegacyToStructuredMappings(detailsPrompts: Record<string, unknown> | { characterPrompts?: string; itemPrompts?: string; locationPrompts?: string }): {
    characters: StructuredEntityMappings;
    items: StructuredEntityMappings;
    locations: StructuredEntityMappings;
} {
    const characters: StructuredEntityMappings = {};
    const items: StructuredEntityMappings = {};
    const locations: StructuredEntityMappings = {};

    // Convert character prompts
    if (detailsPrompts?.characterPrompts && typeof detailsPrompts.characterPrompts === 'string') {
        const characterMappings = extractEntityMappings(detailsPrompts.characterPrompts);
        for (const [placeholder, description] of characterMappings) {
            const [name, desc] = description.split(': ', 2);
            const type = extractAnimalType(name.toLowerCase(), desc.toLowerCase()) || 'character';
            characters[placeholder] = { name, type, description: desc };
        }
    }

    // Convert item prompts
    if (detailsPrompts?.itemPrompts && typeof detailsPrompts.itemPrompts === 'string') {
        const itemMappings = extractEntityMappings(detailsPrompts.itemPrompts);
        for (const [placeholder, description] of itemMappings) {
            const [name, desc] = description.split(': ', 2);
            const type = extractItemType(name.toLowerCase(), desc.toLowerCase()) || name.toLowerCase();
            items[placeholder] = { name, type, description: desc };
        }
    }

    // Convert location prompts
    if (detailsPrompts?.locationPrompts && typeof detailsPrompts.locationPrompts === 'string') {
        const locationMappings = extractEntityMappings(detailsPrompts.locationPrompts);
        for (const [placeholder, description] of locationMappings) {
            const [name, desc] = description.split(': ', 2);
            const type = extractLocationType(name.toLowerCase(), desc.toLowerCase()) || name.toLowerCase();
            locations[placeholder] = { name, type, description: desc };
        }
    }

    return { characters, items, locations };
}

/**
 * Get entity mappings with backwards compatibility
 * Uses structured mappings if available, falls back to legacy parsing
 */
export function getEntityMappings(storyData: Record<string, unknown>): {
    characters: StructuredEntityMappings;
    items: StructuredEntityMappings;
    locations: StructuredEntityMappings;
} {
    // Try structured mappings first (new format)
    const structured = parseStructuredEntityMappings(storyData);
    
    // If we have structured data, use it
    if (Object.keys(structured.characters).length > 0 || 
        Object.keys(structured.items).length > 0 || 
        Object.keys(structured.locations).length > 0) {
        console.log('[getEntityMappings] Using structured mappings');
        return structured;
    }

    // Fall back to legacy format
    console.log('[getEntityMappings] Falling back to legacy detailsPrompts parsing');
    return convertLegacyToStructuredMappings(storyData.detailsPrompts as Record<string, unknown>);
}

/**
 * Convert structured mappings to legacy detailsPrompts format (for backwards compatibility)
 */
export function convertStructuredToLegacyPrompts(mappings: {
    characters: StructuredEntityMappings;
    items: StructuredEntityMappings;
    locations: StructuredEntityMappings;
}): Record<string, unknown> {
    const detailsPrompts: Record<string, unknown> = {};

    // Convert characters
    if (Object.keys(mappings.characters).length > 0) {
        let characterPrompts = 'Character Prompts:\n\n';
        for (const [placeholder, entity] of Object.entries(mappings.characters)) {
            characterPrompts += `Character: ${entity.name} - ${placeholder}\n${entity.description}\n\n`;
        }
        detailsPrompts.characterPrompts = characterPrompts.trim();
    }

    // Convert items
    if (Object.keys(mappings.items).length > 0) {
        let itemPrompts = 'Item Prompts:\n\n';
        for (const [placeholder, entity] of Object.entries(mappings.items)) {
            itemPrompts += `Item: ${entity.name} - ${placeholder}\n${entity.description}\n\n`;
        }
        detailsPrompts.itemPrompts = itemPrompts.trim();
    }

    // Convert locations
    if (Object.keys(mappings.locations).length > 0) {
        let locationPrompts = 'Location Prompts:\n\n';
        for (const [placeholder, entity] of Object.entries(mappings.locations)) {
            locationPrompts += `Location: ${entity.name} - ${placeholder}\n${entity.description}\n\n`;
        }
        detailsPrompts.locationPrompts = locationPrompts.trim();
    }

    return detailsPrompts;
}

/**
 * Populate structured mappings from detailsPrompts (for Step 2 - when generating characters/items/locations)
 * This function extracts entities from the AI-generated prompts and creates structured mappings
 */
export function populateStructuredMappingsFromPrompts(detailsPrompts: Record<string, unknown> | { characterPrompts?: string; itemPrompts?: string; locationPrompts?: string }): {
    character_mappings: string;
    item_mappings: string;
    location_mappings: string;
} {
    const mappings = convertLegacyToStructuredMappings(detailsPrompts);
    
    return {
        character_mappings: JSON.stringify(mappings.characters, null, 2),
        item_mappings: JSON.stringify(mappings.items, null, 2),
        location_mappings: JSON.stringify(mappings.locations, null, 2)
    };
}

/**
 * Transforms action prompts using actual story data for accurate placeholder conversion
 * Converts "@CharacterName" to descriptive terms based on actual character descriptions from Step 2
 * Also handles generic terms like "character" by mapping them to specific animal types
 * Now supports both structured mappings (new) and legacy detailsPrompts (backwards compatibility)
 */
export async function transformActionPromptWithStoryData(prompt: string, storyData: Record<string, unknown> | { detailsPrompts?: unknown; character_mappings?: string; item_mappings?: string; location_mappings?: string }): Promise<string> {
    if (!prompt) {
        return prompt;
    }

    // If no story data at all, fall back to basic pattern matching
    if (!storyData?.detailsPrompts && !storyData?.character_mappings && !storyData?.item_mappings && !storyData?.location_mappings) {
        return transformActionPromptForVideo(prompt);
    }

    let transformedPrompt = prompt;

    // Get entity mappings (structured or legacy)
    const entityMappings = getEntityMappings(storyData);

    // Step 1: Transform explicit placeholders (e.g., @ProfessorHedgehog -> the hedgehog)
    console.log(`[transformActionPrompt] Processing ${Object.keys(entityMappings.characters).length} character mappings`);
    
    // Transform characters
    for (const [placeholder, entity] of Object.entries(entityMappings.characters)) {
        const descriptivePhrase = `the ${entity.type}`;
        const placeholderRegex = new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
        transformedPrompt = transformedPrompt.replace(placeholderRegex, descriptivePhrase);
    }

    // Transform items
    for (const [placeholder, entity] of Object.entries(entityMappings.items)) {
        const descriptivePhrase = `the ${entity.type}`;
        const placeholderRegex = new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
        transformedPrompt = transformedPrompt.replace(placeholderRegex, descriptivePhrase);
    }

    // Transform locations
    for (const [placeholder, entity] of Object.entries(entityMappings.locations)) {
        const descriptivePhrase = `the ${entity.type}`;
        const placeholderRegex = new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
        transformedPrompt = transformedPrompt.replace(placeholderRegex, descriptivePhrase);
    }

    // Step 2: Handle generic terms like "character" by mapping them to specific animal types
    // This is needed when AI generates action prompts with generic terms instead of placeholders
    transformedPrompt = replaceGenericTermsWithSpecificAnimalsStructured(transformedPrompt, entityMappings.characters);

    return transformedPrompt;
}

/**
 * Replace generic terms like "character" with specific animal types using structured mappings
 * This handles cases where AI generates action prompts with generic terms instead of placeholders
 */
function replaceGenericTermsWithSpecificAnimalsStructured(prompt: string, characterMappings: StructuredEntityMappings): string {
    let transformedPrompt = prompt;
    
    const characterInfo = Object.values(characterMappings);
    
    // If we have exactly one character, replace all generic terms with it
    if (characterInfo.length === 1) {
        const char = characterInfo[0];
        const descriptivePhrase = `the ${char.type}`;
        transformedPrompt = transformedPrompt.replace(/\bthe character\b/gi, descriptivePhrase);
        transformedPrompt = transformedPrompt.replace(/\bcharacter\b/gi, char.type);
        transformedPrompt = transformedPrompt.replace(/\bthe animal\b/gi, descriptivePhrase);
        transformedPrompt = transformedPrompt.replace(/\banimal\b/gi, char.type);
        transformedPrompt = transformedPrompt.replace(/\bthe creature\b/gi, descriptivePhrase);
        transformedPrompt = transformedPrompt.replace(/\bcreature\b/gi, char.type);
    } else if (characterInfo.length > 1) {
        // For multiple characters, use context clues to determine which one
        const lowerPrompt = transformedPrompt.toLowerCase();
        
        // Create a priority list of context clues
        const contextClues = [
            // Teaching/academic context
            {
                keywords: ['professor', 'teacher', 'teaching', 'chalkboard', 'lesson', 'classroom', 'academic', 'spectacles', 'glasses', 'educator', 'instructor', 'lecturer'],
                characterTraits: ['professor', 'teacher', 'academic', 'spectacles', 'glasses', 'scholarly', 'educator', 'instructor']
            },
            // Authority/leadership context
            {
                keywords: ['points', 'instructs', 'leads', 'guides', 'explains', 'demonstrates', 'commands', 'directs'],
                characterTraits: ['professor', 'teacher', 'wise', 'elder', 'authority', 'leader', 'commander']
            },
            // Wise/elder context
            {
                keywords: ['wise', 'wisdom', 'elder', 'experienced', 'thoughtful', 'deliberate', 'knowledgeable'],
                characterTraits: ['wise', 'old', 'elder', 'experienced', 'weathered', 'thoughtful', 'knowledgeable']
            },
            // Small/energetic context
            {
                keywords: ['small', 'tiny', 'quick', 'energetic', 'scurries', 'darts', 'twitching', 'nimble'],
                characterTraits: ['small', 'tiny', 'energetic', 'quick', 'mouse', 'bright', 'curious', 'nimble']
            },
            // Playful context
            {
                keywords: ['playful', 'mischief', 'curious', 'exploring', 'fluffy', 'playfully'],
                characterTraits: ['playful', 'curious', 'cat', 'fluffy', 'mischief']
            }
        ];
        
        // Score each character based on context clues
        let bestMatch: EntityMapping | null = null;
        let bestScore = 0;
        const characterScores: Array<{char: EntityMapping, score: number, index: number}> = [];
        
        for (let i = 0; i < characterInfo.length; i++) {
            const char = characterInfo[i];
            let score = 0;
            const lowerName = char.name.toLowerCase();
            const lowerDesc = char.description.toLowerCase();
            
            for (const clue of contextClues) {
                // Check if prompt contains any of the keywords
                const promptHasKeyword = clue.keywords.some(keyword => lowerPrompt.includes(keyword));
                
                if (promptHasKeyword) {
                    // Check if character has matching traits
                    const characterHasTraits = clue.characterTraits.some(trait => 
                        lowerName.includes(trait) || lowerDesc.includes(trait)
                    );
                    
                    if (characterHasTraits) {
                        score += clue.keywords.filter(keyword => lowerPrompt.includes(keyword)).length;
                    }
                }
            }
            
            // Boost score for characters that appear first (often the main character)
            if (i === 0) {
                score += 0.5;
            }
            
            characterScores.push({char, score, index: i});
            
            if (score > bestScore) {
                bestScore = score;
                bestMatch = char;
            }
        }
        
        // Sort characters by score (descending) to help with fallback logic
        characterScores.sort((a, b) => b.score - a.score);
        
        // Determine which character to use as the main character
        let mainCharacter: EntityMapping;
        if (bestMatch && bestScore > 0) {
            // We found a good match based on context clues
            mainCharacter = bestMatch;
        } else if (bestMatch && bestScore > 0.1) { // Allow for small scores (like the 0.5 boost for first character)
            // We have a match with a reasonable score
            mainCharacter = bestMatch;
        } else {
            // No good matches found, try to identify a main character by looking for indicators
            // such as "professor", "teacher", "leader", etc. in character names or descriptions
            let potentialMainChar: EntityMapping | null = null;
            
            // First, check characters with the highest scores for strong indicators
            for (const {char} of characterScores) {
                const lowerName = char.name.toLowerCase();
                const lowerDesc = char.description.toLowerCase();
                // Look for strong indicators of a main/leading character
                if (lowerName.includes('professor') || lowerName.includes('teacher') || 
                    lowerName.includes('leader') || lowerName.includes('master') ||
                    lowerName.includes('elder') || lowerName.includes('wise') ||
                    lowerDesc.includes('professor') || lowerDesc.includes('teacher') || 
                    lowerDesc.includes('leader') || lowerDesc.includes('master') ||
                    lowerDesc.includes('wise') || lowerDesc.includes('elder') ||
                    lowerDesc.includes('knowledgeable') || lowerDesc.includes('experienced') ||
                    lowerDesc.includes('authoritative') || lowerDesc.includes('commander')) {
                    potentialMainChar = char;
                    break;
                }
            }
            
            // If we found a potential main character, use it
            if (potentialMainChar) {
                mainCharacter = potentialMainChar;
            } else {
                // Try to identify characters that are likely to be the main/central character
                // by looking for more general indicators
                for (const {char} of characterScores) {
                    const lowerDesc = char.description.toLowerCase();
                    // Look for general indicators of importance
                    if (lowerDesc.includes('main') || lowerDesc.includes('central') || 
                        lowerDesc.includes('primary') || lowerDesc.includes('key')) {
                        potentialMainChar = char;
                        break;
                    }
                }
                
                // If still no potential main character found, use the highest scoring character
                // or if all scores are 0, use the first character (index 0)
                if (characterScores.length > 0) {
                    mainCharacter = potentialMainChar || characterScores[0].char;
                } else {
                    // Fallback: use the first character
                    mainCharacter = characterInfo[0];
                }
            }
        }
        
        // Replace generic terms with the selected main character
        const descriptivePhrase = `the ${mainCharacter.type}`;
        transformedPrompt = transformedPrompt.replace(/\bthe character\b/gi, descriptivePhrase);
        transformedPrompt = transformedPrompt.replace(/\bcharacter\b/gi, mainCharacter.type);
        transformedPrompt = transformedPrompt.replace(/\bthe animal\b/gi, descriptivePhrase);
        transformedPrompt = transformedPrompt.replace(/\banimal\b/gi, mainCharacter.type);
        transformedPrompt = transformedPrompt.replace(/\bthe creature\b/gi, descriptivePhrase);
        transformedPrompt = transformedPrompt.replace(/\bcreature\b/gi, mainCharacter.type);
    }
    
    return transformedPrompt;
}



/**
 * Extract entity mappings from prompts text (Character Name - @Placeholder format)
 */
function extractEntityMappings(promptsText: string): Map<string, string> {
    const mappings = new Map<string, string>();
    
    if (!promptsText) return mappings;

    // Remove the header line (e.g., "Character Prompts:")
    const cleanedText = promptsText.replace(/^(Character Prompts:|Item Prompts:|Location Prompts:)\s*\n*/i, '').trim();
    
    // Split by double newlines to get each entity block
    const entityBlocks = cleanedText.split(/\n\s*\n/);
    
    for (const block of entityBlocks) {
        const lines = block.trim().split('\n').map(l => l.trim()).filter(l => l);
        if (lines.length === 0) continue;
        
        const firstLine = lines[0];
        // Match format: "Character: Character Name - @Placeholder" or "Character Name - @Placeholder"
        let match = firstLine.match(/^(?:Character|Item|Location):\s*(.+?)\s*-\s*(@[A-Za-z0-9_]+)$/);
        if (!match) {
            // Fallback to simpler format: "Character Name - @Placeholder"
            match = firstLine.match(/^(.+?)\s*-\s*(@[A-Za-z0-9_]+)$/);
        }
        
        if (match) {
            const entityName = match[1].trim();
            const placeholder = match[2].trim();
            const description = lines.slice(1).join(' ').trim();
            
            // Store both the name and description for analysis
            mappings.set(placeholder, `${entityName}: ${description}`);
        }
    }
    
    return mappings;
}



/**
 * Dynamically extract animal type from name or description
 * This works by looking for common animal words in the text
 */
export function extractAnimalType(lowerName: string, lowerDesc: string): string | null {
    const combinedText = `${lowerName} ${lowerDesc}`;
    
    // Common animal patterns - ordered by specificity (more specific first)
    const animalPatterns = [
        // Specific animals
        'field mouse', 'guinea pig', 'sea turtle', 'tree frog', 'barn owl',
        
        // Regular animals (alphabetical for easy maintenance)
        'ant', 'badger', 'bat', 'bear', 'bee', 'bird', 'bunny', 'butterfly', 
        'cat', 'caterpillar', 'chameleon', 'chipmunk', 'cow', 'cricket', 'deer', 
        'dog', 'dragonfly', 'duck', 'elephant', 'elk', 'ferret', 'firefly', 'fish', 
        'fox', 'frog', 'gecko', 'goat', 'grasshopper', 'hamster', 'hedgehog', 
        'horse', 'iguana', 'ladybug', 'lion', 'lizard', 'mole', 'moth', 'mouse', 
        'newt', 'opossum', 'otter', 'owl', 'pig', 'porcupine', 'possum', 'rabbit', 
        'raccoon', 'salamander', 'sheep', 'skunk', 'slug', 'snail', 'snake', 
        'spider', 'squirrel', 'tiger', 'toad', 'tortoise', 'turtle', 'weasel', 
        'wolf', 'worm'
    ];
    
    // Find the first matching animal type using word boundaries for better accuracy
    for (const animal of animalPatterns) {
        // Use word boundaries to avoid partial matches (e.g., "ant" in "anthropomorphic")
        const regex = new RegExp(`\\b${animal}\\b`, 'i');
        if (regex.test(combinedText)) {
            return animal;
        }
    }
    
    return null;
}

/**
 * Dynamically extract location type from name or description
 */
function extractLocationType(lowerName: string, lowerDesc: string): string | null {
    const combinedText = `${lowerName} ${lowerDesc}`;
    
    const locationPatterns = [
        // Specific locations
        'classroom', 'learning school', 'school', 'clearing',
        
        // General locations
        'beach', 'bridge', 'castle', 'forest', 'garden', 'house', 'lake', 
        'meadow', 'mountain', 'pond', 'river', 'stone', 'tree'
    ];
    
    for (const location of locationPatterns) {
        if (combinedText.includes(location)) {
            return location;
        }
    }
    
    return null;
}

/**
 * Dynamically extract item type from name or description
 */
function extractItemType(lowerName: string, lowerDesc: string): string | null {
    const combinedText = `${lowerName} ${lowerDesc}`;
    
    const itemPatterns = [
        // Specific items
        'inspection badge', 'spectacles', 'podium', 'acorns',
        
        // General items
        'acorn', 'badge', 'book', 'crystal', 'fence', 'gem', 'glasses', 
        'key', 'lantern', 'stone', 'tumbler', 'twig'
    ];
    
    for (const item of itemPatterns) {
        if (combinedText.includes(item)) {
            return item;
        }
    }
    
    return null;
}

/**
 * Extract JSON from Perplexity responses that may contain reasoning tags and markdown code blocks
 */
export function extractJsonFromPerplexityResponse(responseText: string): string {
    let cleanedText = responseText;

    // Step 1: If the response has <think> tags, extract content after </think>
    if (cleanedText.includes('<think>') && cleanedText.includes('</think>')) {
        const afterThinkMatch = cleanedText.match(/<\/think>\s*([\s\S]*)/);
        if (afterThinkMatch && afterThinkMatch[1]) {
            cleanedText = afterThinkMatch[1].trim();
        }
    }

    // Step 2: Remove markdown code block formatting if present
    if (cleanedText.includes('```json') || cleanedText.includes('```')) {
        // Extract content between code block markers
        const codeBlockMatch = cleanedText.match(/```(?:json)?\s*([\s\S]*?)```/);
        if (codeBlockMatch && codeBlockMatch[1]) {
            cleanedText = codeBlockMatch[1].trim();
        } else {
            // If there's a starting ``` but no ending, just remove the starting marker
            cleanedText = cleanedText.replace(/```(?:json)?\s*/, '').trim();
        }
    }

    // Step 3: Try to find JSON within the cleaned response by looking for { and }
    const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
        const jsonStr = jsonMatch[0];
        
        // Step 4: Try to fix common JSON parsing issues
        try {
            // Test if it parses as-is first
            JSON.parse(jsonStr);
            return jsonStr;
        } catch {
            // Try to fix common issues with quotes and escaped characters
            console.log('JSON parsing failed, attempting to fix common issues...');
            
            // Fix unescaped quotes inside strings (basic fix)
            // This is a very basic approach - for production you might want a more robust solution
            try {
                // Try a simple quote escaping fix
                const fixedJson = jsonStr.replace(/"([^"]*)"([^"]*)"([^"]*)":/g, '"$1\\"$2\\"$3":');
                JSON.parse(fixedJson);
                return fixedJson;
            } catch {
                // If that doesn't work, return the original
                return jsonStr;
            }
        }
    }

    // If no JSON patterns found, return the cleaned text
    return cleanedText;
}

/**
 * Extract valid JSON from AI model text responses
 */
export function extractValidJsonFromText(text: string): string | null {
    // Step 1: Remove common markdown code block markers
    let cleanedText = text;
    if (cleanedText.includes('```json') || cleanedText.includes('```')) {
        const codeBlockMatch = cleanedText.match(/```(?:json)?\s*([\s\S]*?)```/);
        if (codeBlockMatch && codeBlockMatch[1]) {
            cleanedText = codeBlockMatch[1].trim();
        } else {
            // If there's a starting ``` but no ending, just remove the starting marker
            cleanedText = cleanedText.replace(/```(?:json)?\s*/, '').trim();
        }
    }

    // Step 2: Try to find valid JSON by counting braces
    let bracesCount = 0;
    let startIndex = -1;
    let endIndex = -1;

    for (let i = 0; i < cleanedText.length; i++) {
        const char = cleanedText[i];
        if (char === '{') {
            if (bracesCount === 0) {
                startIndex = i; // Start of potential JSON object
            }
            bracesCount++;
        } else if (char === '}') {
            bracesCount--;
            if (bracesCount === 0 && startIndex !== -1) {
                endIndex = i; // End of balanced JSON object
                break;
            }
        }
    }

    if (startIndex !== -1 && endIndex !== -1) {
        const possibleJson = cleanedText.substring(startIndex, endIndex + 1);

        // Step 3: Validate that it's actually valid JSON
        try {
            JSON.parse(possibleJson);
            return possibleJson;
        } catch {
            // If parsing fails, continue to fallback
        }
    }

    // Step 4: Fallback to simple regex match (original behavior)
    const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
        try {
            JSON.parse(jsonMatch[0]);
            return jsonMatch[0];
        } catch {
            // If even the regex match isn't valid JSON, return null
            return null;
        }
    }

    return null;
}

/**
 * Estimate duration from audio data URI (MP3 or WAV)
 */
export function getMp3DurationFromDataUri(dataUri: string): number {
    try {
        let base64Data: string;
        let estimatedBytesPerSecond: number;

        if (dataUri.startsWith('data:audio/mpeg;base64,')) {
            base64Data = dataUri.substring('data:audio/mpeg;base64,'.length);
            const estimatedBitrateKbps = 128;
            estimatedBytesPerSecond = (estimatedBitrateKbps * 1000) / 8;
        } else if (dataUri.startsWith('data:audio/wav;base64,')) {
            base64Data = dataUri.substring('data:audio/wav;base64,'.length);
            estimatedBytesPerSecond = 48000;
        } else {
            console.warn('Cannot estimate duration: Unsupported audio format in data URI.');
            return 30;
        }

        const binaryData = Buffer.from(base64Data, 'base64');
        const durationSeconds = binaryData.length / estimatedBytesPerSecond;

        if (base64Data === 'UklGRiQAAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQAAAAAA') {
            console.warn('Placeholder WAV audio detected, setting duration to 1s.');
            return 1;
        }
        if (binaryData.length < 1000 && durationSeconds < 1) {
            console.warn('Very short audio detected, possibly placeholder or error. Setting duration to 1s.');
            return 1;
        }

        return Math.max(1, parseFloat(durationSeconds.toFixed(2)));
    } catch (e) {
        console.error('Error estimating MP3 duration:', e);
        return 30;
    }
}

/**
 * Fix missing periods in script chunks
 */
export function fixMissingPeriods(chunks: string[], originalScript: string): string[] | null {
    try {
        const fixedChunks = chunks.map((chunk, index) => {
            let fixedChunk = chunk.trim();
            
            // If this chunk doesn't end with punctuation and isn't the last chunk
            if (!fixedChunk.match(/[.!?]$/) && index < chunks.length - 1) {
                // Check if the next chunk starts with a capital letter (new sentence)
                const nextChunk = chunks[index + 1]?.trim();
                if (nextChunk && /^[A-Z]/.test(nextChunk)) {
                    fixedChunk += '.';
                }
            }
            
            return fixedChunk;
        });

        // Verify that we didn't break anything
        const fixedText = fixedChunks.join(' ').replace(/\s+/g, ' ').trim();
        const originalText = originalScript.replace(/\s+/g, ' ').trim();
        
        // Allow for minor differences due to added periods
        const similarityThreshold = 0.95;
        const similarity = fixedText.length / originalText.length;
        
        if (similarity >= similarityThreshold && similarity <= 1.05) {
            return fixedChunks;
        }
        
        return null; // Return original chunks if fixing failed
    } catch (error) {
        console.error('Error fixing missing periods:', error);
        return null;
    }
}

/**
 * Validate script chunks against original script
 */
export function validateScriptChunks(originalScript: string, chunks: string[]): { isValid: boolean, error?: string } {
    try {
        // Combine chunks and normalize whitespace
        const combinedText = chunks.join(' ').replace(/\s+/g, ' ').trim().toLowerCase();
        const originalText = originalScript.replace(/\s+/g, ' ').trim().toLowerCase();
        
        // Check if all words from original are in combined
        const originalWords = originalText.split(/\s+/);
        const combinedWords = combinedText.split(/\s+/);
        
        // Allow for slight differences in word count due to punctuation handling
        const wordCountDiff = Math.abs(originalWords.length - combinedWords.length);
        const maxAllowedDiff = Math.ceil(originalWords.length * 0.02); // 2% tolerance
        
        if (wordCountDiff > maxAllowedDiff) {
            return {
                isValid: false,
                error: `Word count mismatch: original ${originalWords.length}, chunks ${combinedWords.length}`
            };
        }
        
        // Check for major content differences
        const similarity = combinedText.length / originalText.length;
        if (similarity < 0.9 || similarity > 1.1) {
            return {
                isValid: false,
                error: `Content length mismatch: ${(similarity * 100).toFixed(1)}% of original`
            };
        }
        
        return { isValid: true };
    } catch (error) {
        return {
            isValid: false,
            error: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
    }
}
