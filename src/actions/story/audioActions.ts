"use server";

import {
    type GenerateNarrationAudioInput as AINarrationAudioInputType,
    type GenerateNarrationAudioOutput as AINarrationAudioOutputType
} from '@/ai/flows/generate-narration-audio-types';
import { getUserApiKeys } from '../baserowApiKeyActions';
import { uploadAudioToMinIOStorage } from '../minioStorageActions';
import { getMp3DurationFromDataUri } from '../utils/storyHelpers';
import type { ElevenLabsVoice } from '@/types/story';

export interface GenerateNarrationAudioActionInput extends AINarrationAudioInputType {
    userId?: string;
    storyId?: string;
    chunkId?: string;
    isSpanish?: boolean; // Flag to indicate Spanish narration
    isRomanian?: boolean; // Flag to indicate Romanian narration
}

export interface VoicePreviewInput {
    voiceId: string;
    ttsModel: 'elevenlabs' | 'google';
    googleApiModel?: string;
    languageCode?: string;
    demoText?: string;
    userId?: string;
}

export async function generateNarrationAudio(actionInput: GenerateNarrationAudioActionInput): Promise<{ success: boolean; data?: { audioStorageUrl?: string; voices?: ElevenLabsVoice[]; duration?: number }; error?: string }> {
    if (!actionInput.userId) {
        return { success: false, error: "User ID is required for narration generation." };
    }

    try {
        const userKeysResult = await getUserApiKeys(actionInput.userId);
        if (!userKeysResult.success || !userKeysResult.data) {
            return { success: false, error: "Could not fetch user API keys. " + (userKeysResult.error || "") };
        }
        const userApiKeys = userKeysResult.data;

        let serviceApiKey: string | undefined;
        const modelToUse = actionInput.ttsModel || 'elevenlabs';

        if (modelToUse === 'elevenlabs') {
            serviceApiKey = userApiKeys.elevenLabsApiKey;
            if (!serviceApiKey) {
                return { success: false, error: "ElevenLabs API key not configured by user. Please set it in Account Settings." };
            }
        } else if (modelToUse === 'google') {
            serviceApiKey = userApiKeys.googleApiKey;
            if (!serviceApiKey) {
                return { success: false, error: "Google API key for TTS not configured by user. Please set it in Account Settings." };
            }
        }

        const aiFlowInput: AINarrationAudioInputType = {
            script: actionInput.script,
            voiceId: actionInput.voiceId,
            ttsModel: modelToUse,
            googleApiModel: actionInput.googleApiModel,
            languageCode: actionInput.languageCode,
            apiKey: serviceApiKey
        };

        const { generateNarrationAudio: aiGenerateNarrationAudioFlow } = await import('@/ai/flows/generate-narration-audio');
        const result: AINarrationAudioOutputType = await aiGenerateNarrationAudioFlow(aiFlowInput);

        if (result.error) {
            return { success: false, error: result.error };
        }

        if (result.audioDataUri) {
            const duration = getMp3DurationFromDataUri(result.audioDataUri);
            if (actionInput.userId && actionInput.storyId && actionInput.chunkId) {
                const languageLabel = actionInput.isSpanish ? 'Spanish ' : actionInput.isRomanian ? 'Romanian ' : '';
                try {
                    const fileExtension = result.audioDataUri.startsWith('data:audio/wav;base64,') ? 'wav' : 'mp3';
                    const prefix = actionInput.isSpanish ? 'es_' : actionInput.isRomanian ? 'ro_' : '';
                    const filename = `${prefix}narration_chunk_${actionInput.chunkId}.${fileExtension}`;
                    const storageUrl = await uploadAudioToMinIOStorage(result.audioDataUri, actionInput.userId, actionInput.storyId, filename);
                    console.log(`Uploaded ${languageLabel}narration chunk ${actionInput.chunkId} to: ${storageUrl}`);
                    return { success: true, data: { audioStorageUrl: storageUrl, duration } };
                } catch (uploadError) {
                    console.error(`Failed to upload ${languageLabel}narration chunk ${actionInput.chunkId} to MinIO Storage:`, uploadError);
                    return { success: false, error: `Failed to upload audio for chunk ${actionInput.chunkId}: ${(uploadError as Error).message}` };
                }
            } else {
                console.warn("generateNarrationAudio action: audioDataUri present but missing userId, storyId, or chunkId for storage.");
                return { success: true, data: { audioStorageUrl: result.audioDataUri, duration } };
            }
        }

        if (result.voices) {
            return { success: true, data: { voices: result.voices as ElevenLabsVoice[] } };
        }

        return { success: false, error: "Unknown error from narration generation." };

    } catch (error) {
        console.error("Error in generateNarrationAudio action:", error);
        return { success: false, error: "Failed to process narration audio request." };
    }
}

export async function generateVoicePreview(input: VoicePreviewInput): Promise<{ success: boolean; audioDataUri?: string; error?: string }> {
    if (!input.userId) {
        return { success: false, error: "User ID is required for voice preview." };
    }

    try {
        const userKeysResult = await getUserApiKeys(input.userId);
        if (!userKeysResult.success || !userKeysResult.data) {
            return { success: false, error: "Could not fetch user API keys for preview. " + (userKeysResult.error || "") };
        }
        const userApiKeys = userKeysResult.data;

        let serviceApiKey: string | undefined;
        if (input.ttsModel === 'elevenlabs') {
            serviceApiKey = userApiKeys.elevenLabsApiKey;
            if (!serviceApiKey) {
                return { success: false, error: "ElevenLabs API key not configured by user for preview. Please set it in Account Settings." };
            }
        } else if (input.ttsModel === 'google') {
            serviceApiKey = userApiKeys.googleApiKey;
            if (!serviceApiKey) {
                return { success: false, error: "Google API key for TTS preview not configured by user. Please set it in Account Settings." };
            }
        }

        const demoText = input.demoText || "Hello! This is a preview of how this voice sounds. I hope you like it!";

        const aiFlowInput: AINarrationAudioInputType = {
            script: demoText,
            voiceId: input.voiceId,
            ttsModel: input.ttsModel,
            googleApiModel: input.googleApiModel,
            languageCode: input.languageCode,
            apiKey: serviceApiKey,
        };

        const { generateNarrationAudio: aiGenerateNarrationAudioFlow } = await import('@/ai/flows/generate-narration-audio');
        const result: AINarrationAudioOutputType = await aiGenerateNarrationAudioFlow(aiFlowInput);

        if (result.error) {
            return { success: false, error: result.error };
        }

        if (result.audioDataUri) {
            return { success: true, audioDataUri: result.audioDataUri };
        }

        return { success: false, error: "No audio data returned from voice preview generation." };
    } catch (error) {
        console.error("Error in generateVoicePreview action:", error);
        return { success: false, error: "Failed to generate voice preview." };
    }
}
