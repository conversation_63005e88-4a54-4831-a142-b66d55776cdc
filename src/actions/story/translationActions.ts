"use server";

import { genkit } from 'genkit';
import { googleAI } from '@genkit-ai/googleai';
import { z } from 'zod';
import { runFlow } from '@genkit-ai/flow';

import {
    AISpanishTranslationOutputSchema,
    type GenerateSpanishTranslationInput,
    AIRomanianTranslationOutputSchema,
    type GenerateRomanianTranslationInput
} from '../storyActionSchemas';

import { getUserApiKeys } from '../baserowApiKeyActions';
import { 
    spanishTranslationPromptTemplate,
    spanishTranslationPromptTemplateGemini,
    romanianTranslationPromptTemplate,
    romanianTranslationPromptTemplateGemini
} from '../utils/promptTemplates';
import { extractJsonFromPerplexityResponse, extractValidJsonFromText } from '../utils/storyHelpers';
import { listGoogleScriptModels } from './modelActions';

// Auto-save function to find and update story with translation results
async function autoSaveTranslationToBaserow(
    userId: string, 
    originalChunks: Array<{ id?: string; text: string; index: number }>,
    translatedChunks: Array<{ id: string; text: string; index: number }>,
    language: 'spanish' | 'romanian'
): Promise<void> {
    try {
        console.log(`[autoSaveTranslationToBaserow] Attempting to save ${language} translation for user ${userId}`);
        
        // Import here to avoid circular dependencies
        const { getStory, saveStory } = await import('../baserowStoryActions');
        const { baserowService } = await import('../../lib/baserow');
        
        // Get all stories for this user
        const stories = await baserowService.getStories(userId);
        
        // Find the story that matches the original chunks
        let storyId = null;
        
        for (const storyRow of stories) {
            try {
                if (storyRow.narration_chunks) {
                    const narrationChunks = JSON.parse(storyRow.narration_chunks as string);
                    
                    // Check if this story's narration chunks match the input chunks
                    if (Array.isArray(narrationChunks) && narrationChunks.length === originalChunks.length) {
                        const matches = originalChunks.every((chunk, index) => {
                            const storyChunk = narrationChunks[index];
                            return storyChunk && storyChunk.text === chunk.text;
                        });
                        
                        if (matches) {
                            storyId = storyRow.firebase_story_id as string || (storyRow.id as number).toString();
                            console.log(`[autoSaveTranslationToBaserow] Found matching story: ${storyId}`);
                            break;
                        }
                    }
                }
            } catch {
                // Skip stories with invalid JSON
                continue;
            }
        }
        
        if (!storyId) {
            console.warn(`[autoSaveTranslationToBaserow] Could not find matching story for ${language} translation`);
            return;
        }
        
        // Get the full story
        const storyResult = await getStory(storyId, userId);
        
        if (!storyResult.success || !storyResult.data) {
            console.error(`[autoSaveTranslationToBaserow] Failed to get story ${storyId}:`, storyResult.error);
            return;
        }
        
        // Update the story with the translation
        const updatedStory = {
            ...storyResult.data,
            ...(language === 'spanish' 
                ? { spanishNarrationChunks: translatedChunks }
                : { romanianNarrationChunks: translatedChunks }
            )
        };
        
        // Save the updated story
        const saveResult = await saveStory(updatedStory, userId);
        
        if (saveResult.success) {
            console.log(`[autoSaveTranslationToBaserow] Successfully saved ${language} translation to Baserow for story ${storyId}`);
        } else {
            console.error(`[autoSaveTranslationToBaserow] Failed to save ${language} translation:`, saveResult.error);
        }
        
    } catch (error) {
        console.error(`[autoSaveTranslationToBaserow] Error during auto-save:`, error);
    }
}

// Helper function for batch processing large translations
async function processBatchTranslation(
    input: GenerateSpanishTranslationInput | GenerateRomanianTranslationInput,
    userApiKeys: { googleApiKey?: string; perplexityApiKey?: string },
    language: 'spanish' | 'romanian'
): Promise<{ success: boolean, data?: { spanishChunks?: Array<{ id: string; text: string; index: number }>; romanianChunks?: Array<{ id: string; text: string; index: number }> }, error?: string }> {
    const batchSize = 3; // Reduced batch size to 3 chunks to reduce processing time per batch
    const allTranslatedChunks: Array<{ id: string; text: string; index: number }> = [];

    console.log(`[processBatchTranslation] Processing ${input.chunks.length} chunks in batches of ${batchSize} for ${language}`);

    for (let i = 0; i < input.chunks.length; i += batchSize) {
        const batch = input.chunks.slice(i, i + batchSize);
        console.log(`[processBatchTranslation] Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(input.chunks.length/batchSize)} (${batch.length} chunks)`);

        // Create a smaller input for this batch
        const batchInput = { ...input, chunks: batch };

        try {
            // Reduce delay between batches to minimize total processing time
            if (i > 0) {
                console.log(`[processBatchTranslation] Adding 0.5s delay between batches...`);
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // Process this batch using the original translation logic (without batching to avoid infinite recursion)
            const batchResult = await (language === 'spanish'
                ? processOriginalSpanishTranslation(batchInput, userApiKeys)
                : processOriginalRomanianTranslation(batchInput, userApiKeys));

            if (batchResult.success && batchResult.data) {
                const chunks = language === 'spanish'
                    ? (batchResult.data as z.infer<typeof AISpanishTranslationOutputSchema>).spanishChunks
                    : (batchResult.data as z.infer<typeof AIRomanianTranslationOutputSchema>).romanianChunks;
                allTranslatedChunks.push(...chunks);
                console.log(`[processBatchTranslation] Batch ${Math.floor(i/batchSize) + 1} completed: ${chunks.length} chunks translated`);
            } else {
                console.error(`[processBatchTranslation] Batch ${Math.floor(i/batchSize) + 1} failed:`, batchResult.error);
                return { success: false, error: `Batch translation failed: ${batchResult.error}` };
            }
        } catch (error) {
            console.error(`[processBatchTranslation] Error processing batch ${Math.floor(i/batchSize) + 1}:`, error);
            return { success: false, error: `Batch processing error: ${error instanceof Error ? error.message : 'Unknown error'}` };
        }
    }

    console.log(`[processBatchTranslation] All batches completed: ${allTranslatedChunks.length} total chunks translated`);

    return {
        success: true,
        data: language === 'spanish'
            ? { spanishChunks: allTranslatedChunks }
            : { romanianChunks: allTranslatedChunks }
    };
}

// Original Spanish translation logic for batch processing
async function processOriginalSpanishTranslation(
    input: GenerateSpanishTranslationInput,
    userApiKeys: { googleApiKey?: string; perplexityApiKey?: string }
): Promise<{ success: boolean, data?: z.infer<typeof AISpanishTranslationOutputSchema>, error?: string }> {
    // Format chunks for the prompt
    const chunksText = JSON.stringify(input.chunks, null, 2);

    if (input.aiProvider === 'perplexity') {
        // [Perplexity logic would go here - keeping original for now]
        return { success: false, error: "Perplexity not implemented in batch helper" };
    } else { // Google AI
        if (!userApiKeys.googleApiKey) {
            return { success: false, error: "Google API key not configured by user. Please set it in Account Settings." };
        }
        try {
            // Define model fallback strategy
            const primaryModel = input.googleScriptModel || 'gemini-2.5-pro';
            const fallbackModels = ['gemini-2.5-pro', 'gemini-2.0-flash', 'gemini-1.5-flash', 'gemini-1.5-pro'];
            const modelsToTry = [primaryModel, ...fallbackModels.filter(m => m !== primaryModel)];

            // Use Gemini-optimized prompt and config
            const prompt = spanishTranslationPromptTemplateGemini.replace('{{chunks}}', chunksText);
            let lastError: string = '';

            for (const modelName of modelsToTry) {
                try {
                    console.log(`[processOriginalSpanishTranslation] Trying model: ${modelName} for ${input.chunks.length} chunks`);

                    const localAi = genkit({ plugins: [googleAI({ apiKey: userApiKeys.googleApiKey })], model: `googleai/${modelName}` });
                    const config = { temperature: 0.1, maxOutputTokens: 8192 };

                    // For Gemini 2.5 Pro, use even higher token limit
                    const isGemini25Pro = modelName.includes('2.5-pro');
                    if (isGemini25Pro) {
                        config.maxOutputTokens = 16384; // Higher limit for 2.5 Pro
                    }

                    // Try with schema validation first
                    try {
                        console.log(`[processOriginalSpanishTranslation] Attempting schema validation for ${modelName}...`);
                        const { output } = await localAi.generate({
                            prompt,
                            output: { schema: AISpanishTranslationOutputSchema, format: 'json' },
                            config
                        });

                        if (output?.spanishChunks && Array.isArray(output.spanishChunks) && output.spanishChunks.length > 0) {
                            console.log(`[processOriginalSpanishTranslation] Schema validation successful, ${output.spanishChunks.length} chunks translated`);
                            // Note: Auto-save is handled by the main function, not the batch helper
                            return { success: true, data: { spanishChunks: output.spanishChunks } };
                        } else {
                            throw new Error("Invalid schema output");
                        }
                    } catch {
                        console.log(`[processOriginalSpanishTranslation] Schema validation failed for ${modelName}, trying next model`);
                        lastError = `Schema validation failed for ${modelName}`;
                        continue; // Try next model
                    }
                } catch (modelError) {
                    const errorMsg = modelError instanceof Error ? modelError.message : 'Unknown error';
                    console.error(`[processOriginalSpanishTranslation] Model ${modelName} failed:`, errorMsg);
                    lastError = `Model ${modelName}: ${errorMsg}`;
                    continue;
                }
            }

            return { success: false, error: `All Google models failed. Last error: ${lastError}` };
        } catch (error) {
            console.error('[processOriginalSpanishTranslation] Error:', error);
            return { success: false, error: error instanceof Error ? error.message : "An unexpected error occurred during Spanish translation." };
        }
    }
}

// Original Romanian translation logic for batch processing  
async function processOriginalRomanianTranslation(
    input: GenerateRomanianTranslationInput,
    userApiKeys: { googleApiKey?: string; perplexityApiKey?: string }
): Promise<{ success: boolean, data?: z.infer<typeof AIRomanianTranslationOutputSchema>, error?: string }> {
    // Format chunks for the prompt
    const chunksText = JSON.stringify(input.chunks, null, 2);

    if (input.aiProvider === 'perplexity') {
        // [Perplexity logic would go here - keeping original for now]
        return { success: false, error: "Perplexity not implemented in batch helper" };
    } else { // Google AI
        if (!userApiKeys.googleApiKey) {
            return { success: false, error: "Google API key not configured by user. Please set it in Account Settings." };
        }
        try {
            // Define model fallback strategy
            const primaryModel = input.googleScriptModel || 'gemini-2.5-pro';
            const fallbackModels = ['gemini-2.5-pro', 'gemini-2.0-flash', 'gemini-1.5-flash', 'gemini-1.5-pro'];
            const modelsToTry = [primaryModel, ...fallbackModels.filter(m => m !== primaryModel)];

            // Use Gemini-optimized prompt and config
            const prompt = romanianTranslationPromptTemplateGemini.replace('{{chunks}}', chunksText);
            let lastError: string = '';

            for (const modelName of modelsToTry) {
                try {
                    console.log(`[processOriginalRomanianTranslation] Trying model: ${modelName} for ${input.chunks.length} chunks`);

                    const localAi = genkit({ plugins: [googleAI({ apiKey: userApiKeys.googleApiKey })], model: `googleai/${modelName}` });
                    const config = { temperature: 0.1, maxOutputTokens: 8192 };

                    // For Gemini 2.5 Pro, use even higher token limit
                    const isGemini25Pro = modelName.includes('2.5-pro');
                    if (isGemini25Pro) {
                        config.maxOutputTokens = 16384; // Higher limit for 2.5 Pro
                    }

                    // Try with schema validation first
                    try {
                        console.log(`[processOriginalRomanianTranslation] Attempting schema validation for ${modelName}...`);
                        const { output } = await localAi.generate({
                            prompt,
                            output: { schema: AIRomanianTranslationOutputSchema, format: 'json' },
                            config
                        });

                        if (output?.romanianChunks && Array.isArray(output.romanianChunks) && output.romanianChunks.length > 0) {
                            console.log(`[processOriginalRomanianTranslation] Schema validation successful, ${output.romanianChunks.length} chunks translated`);
                            // Note: Auto-save is handled by the main function, not the batch helper
                            return { success: true, data: { romanianChunks: output.romanianChunks } };
                        } else {
                            throw new Error("Invalid schema output");
                        }
                    } catch {
                        console.log(`[processOriginalRomanianTranslation] Schema validation failed for ${modelName}, trying next model`);
                        lastError = `Schema validation failed for ${modelName}`;
                        continue; // Try next model
                    }
                } catch (modelError) {
                    const errorMsg = modelError instanceof Error ? modelError.message : 'Unknown error';
                    console.error(`[processOriginalRomanianTranslation] Model ${modelName} failed:`, errorMsg);
                    lastError = `Model ${modelName}: ${errorMsg}`;
                    continue;
                }
            }

            return { success: false, error: `All Google models failed. Last error: ${lastError}` };
        } catch (error) {
            console.error('[processOriginalRomanianTranslation] Error:', error);
            return { success: false, error: error instanceof Error ? error.message : "An unexpected error occurred during Romanian translation." };
        }
    }
}

// Complete Spanish translation workflow that saves to Baserow
export async function generateAndSaveSpanishTranslation(
    storyId: string,
    input: GenerateSpanishTranslationInput
): Promise<{ success: boolean, data?: z.infer<typeof AISpanishTranslationOutputSchema>, error?: string }> {
    console.log(`[generateAndSaveSpanishTranslation] Starting complete workflow for story ${storyId}`);
    
    // Step 1: Generate the translation
    const translationResult = await generateSpanishTranslation(input);
    
    if (!translationResult.success || !translationResult.data?.spanishChunks) {
        console.error(`[generateAndSaveSpanishTranslation] Translation failed:`, translationResult.error);
        return translationResult;
    }
    
    console.log(`[generateAndSaveSpanishTranslation] Translation successful, saving ${translationResult.data.spanishChunks.length} chunks to Baserow`);
    
    // Step 2: Get the current story
    const { getStory } = await import('../baserowStoryActions');
    const storyResult = await getStory(storyId, input.userId);
    
    if (!storyResult.success || !storyResult.data) {
        console.error(`[generateAndSaveSpanishTranslation] Failed to get story:`, storyResult.error);
        return { success: false, error: `Failed to get story for saving: ${storyResult.error}` };
    }
    
    // Step 3: Update the story with Spanish translation
    const updatedStory = {
        ...storyResult.data,
        spanishNarrationChunks: translationResult.data.spanishChunks
    };
    
    // Step 4: Save the updated story
    const { saveStory } = await import('../baserowStoryActions');
    const saveResult = await saveStory(updatedStory, input.userId);
    
    if (!saveResult.success) {
        console.error(`[generateAndSaveSpanishTranslation] Failed to save story:`, saveResult.error);
        return { success: false, error: `Translation completed but failed to save: ${saveResult.error}` };
    }
    
    console.log(`[generateAndSaveSpanishTranslation] Successfully saved Spanish translation to Baserow for story ${storyId}`);
    return translationResult;
}

export async function generateSpanishTranslation(input: GenerateSpanishTranslationInput): Promise<{ success: boolean, data?: z.infer<typeof AISpanishTranslationOutputSchema>, error?: string }> {
    console.log(`[generateSpanishTranslation] Function called with userId: ${input.userId}, ${input.chunks.length} chunks`);
    
    // Add a unique request identifier to help track duplicates in logs
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    console.log(`[generateSpanishTranslation] Request ID: ${requestId}`);

    const userKeysResult = await getUserApiKeys(input.userId);
    if (!userKeysResult.success || !userKeysResult.data) {
        return { success: false, error: "Could not fetch user API keys." };
    }
    const userApiKeys = userKeysResult.data;

    // Check if this is a large translation that should be processed in smaller batches
    const totalTextLength = input.chunks.reduce((sum, chunk) => sum + chunk.text.length, 0);
    console.log(`[generateSpanishTranslation] Total text length: ${totalTextLength} characters, ${input.chunks.length} chunks`);

    // For large translations with many chunks, process in smaller batches to avoid rate limits
    const shouldUseBatching = input.chunks.length > 10 && input.aiProvider === 'google';
    if (shouldUseBatching) {
        console.log(`[generateSpanishTranslation] Large translation detected (${input.chunks.length} chunks), using batch processing to avoid rate limits`);
        try {
            const result = await processBatchTranslation(input, userApiKeys, 'spanish');
            
            // Auto-save batch processing results
            if (result.success && result.data?.spanishChunks) {
                await autoSaveTranslationToBaserow(input.userId, input.chunks, result.data.spanishChunks, 'spanish');
            }
            
            // For batch processing, ensure we return data in the correct schema format
            return {
                success: result.success,
                data: result.success && result.data?.spanishChunks ? { spanishChunks: result.data.spanishChunks } : undefined,
                error: result.error
            };
        } catch (error) {
            console.error(`[generateSpanishTranslation] Batch processing failed:`, error);
            return { 
                success: false, 
                error: `Batch processing failed: ${error instanceof Error ? error.message : 'Unknown error'}. This might be due to a timeout. Please try again.` 
            };
        }
    }

    // Format chunks for the prompt
    const chunksText = JSON.stringify(input.chunks, null, 2);

    if (input.aiProvider === 'perplexity') {
        if (!userApiKeys.perplexityApiKey) {
            return { success: false, error: "Perplexity API key not configured by user. Please set it in Account Settings." };
        }
        try {
            // Use original prompt and config for Perplexity (working fine)
            const prompt = spanishTranslationPromptTemplate.replace('{{chunks}}', chunksText);
            const { generateWithPerplexity } = await import('../../ai/genkit');
            const messages = [
                { role: 'system' as const, content: 'You are a professional English to Spanish translator specializing in children\'s stories. Translate accurately while maintaining natural, child-friendly Spanish.' },
                { role: 'user' as const, content: prompt }
            ];

            const resultText = await runFlow(generateWithPerplexity, {
                modelName: input.perplexityModel || 'sonar-reasoning-pro',
                messages: messages,
                userId: input.userId,
            });

            try {
                const extractedJson = extractJsonFromPerplexityResponse(resultText);
                const parsedOutput = JSON.parse(extractedJson) as (z.infer<typeof AISpanishTranslationOutputSchema> & { translatedChunks?: string[] });

                if (parsedOutput?.error) {
                    return { success: false, error: parsedOutput.error };
                }
                // Handle both "spanishChunks" and "translatedChunks" formats
                if (parsedOutput?.spanishChunks && Array.isArray(parsedOutput.spanishChunks)) {
                    // Auto-save to Baserow
                    await autoSaveTranslationToBaserow(input.userId, input.chunks, parsedOutput.spanishChunks, 'spanish');
                    return { success: true, data: { spanishChunks: parsedOutput.spanishChunks } };
                }
                
                // Convert Perplexity's simple string array to expected object format
                if (parsedOutput?.translatedChunks && Array.isArray(parsedOutput.translatedChunks)) {
                    const spanishChunks = parsedOutput.translatedChunks.map((text: string, index: number) => ({
                        id: input.chunks[index]?.id || `chunk_${index}`,
                        text: text,
                        index: index
                    }));
                    // Auto-save to Baserow
                    await autoSaveTranslationToBaserow(input.userId, input.chunks, spanishChunks, 'spanish');
                    return { success: true, data: { spanishChunks } };
                }

                console.error('Perplexity AI did not return the expected spanishChunks or translatedChunks array:', parsedOutput);
                return { success: false, error: 'Failed to parse Spanish translation from Perplexity AI response.' };

            } catch (e) {
                console.error("Failed to parse JSON from Perplexity for Spanish translation:", resultText, e);
                return { success: false, error: "Failed to parse Spanish translation from Perplexity. Output was not valid JSON." };
            }

        } catch (error) {
            console.error("Error in generateSpanishTranslation with Perplexity AI call:", error);
            const errorMessage = error instanceof Error ? error.message : "Failed to generate Spanish translation with Perplexity.";
            return { success: false, error: errorMessage };
        }
    } else { // Default to Google AI
        if (!userApiKeys.googleApiKey) {
            return { success: false, error: "Google API key not configured by user. Please set it in Account Settings." };
        }
        try {
            // Use only the selected model - no fallback
            const selectedModel = input.googleScriptModel || 'gemini-2.5-pro';
            const modelsToTry = [selectedModel]; // Only try the selected model

            // Log available models for debugging
            console.log(`[generateSpanishTranslation] Using selected model: ${selectedModel}`);
            console.log(`[generateSpanishTranslation] Models to try: ${modelsToTry.join(', ')}`);

            // Fetch available models for debugging (don't block on failure)
            try {
                const availableModels = await listGoogleScriptModels(input.userId);
                if (availableModels.success) {
                    const modelNames = availableModels.models?.map(m => m.id) || [];
                    console.log(`[generateSpanishTranslation] Available models from API:`, modelNames);

                    // Check if gemini-2.5-pro is actually available
                    const has25Pro = modelNames.some(name => name.includes('2.5-pro') || name.includes('2.5pro'));
                    if (!has25Pro) {
                        console.warn(`[generateSpanishTranslation] WARNING: gemini-2.5-pro not found in available models. Consider updating fallback models.`);
                    }
                }
            } catch (debugError) {
                console.warn(`[generateSpanishTranslation] Could not fetch available models for debugging:`, debugError);
            }

            // Use Gemini-optimized prompt and config
            const prompt = spanishTranslationPromptTemplateGemini.replace('{{chunks}}', chunksText);

            let lastError: string = '';

            for (const modelName of modelsToTry) {
                try {
                    console.log(`[generateSpanishTranslation] Trying model: ${modelName}`);

                    const localAi = genkit({ plugins: [googleAI({ apiKey: userApiKeys.googleApiKey })], model: `googleai/${modelName}` });
                    const config = { temperature: 0.1, maxOutputTokens: 8192 }; // Lower temperature and higher token limit for better structured output

                    // For Gemini 2.5 Pro, use even higher token limit
                    const isGemini25Pro = modelName.includes('2.5-pro');
                    if (isGemini25Pro) {
                        config.maxOutputTokens = 16384; // Higher limit for 2.5 Pro
                    }

                    // Try with schema validation first
                    try {
                        console.log(`[generateSpanishTranslation] Attempting schema validation for ${modelName}...`);
                        const { output } = await localAi.generate({
                            prompt,
                            output: { schema: AISpanishTranslationOutputSchema, format: 'json' },
                            config
                        });

                        console.log(`[generateSpanishTranslation] Schema output received:`, output);

                        if (output?.spanishChunks && Array.isArray(output.spanishChunks) && output.spanishChunks.length > 0) {
                            console.log(`[generateSpanishTranslation] Schema validation successful, ${output.spanishChunks.length} chunks translated`);
                            
                            // Auto-save to Baserow
                            await autoSaveTranslationToBaserow(input.userId, input.chunks, output.spanishChunks, 'spanish');
                            
                            return { success: true, data: { spanishChunks: output.spanishChunks } };
                        } else {
                            console.log(`[generateSpanishTranslation] Schema output invalid:`, output);
                            throw new Error("Invalid schema output");
                        }
                    } catch (schemaError) {
                        console.log(`[generateSpanishTranslation] Schema validation failed for ${modelName}:`, schemaError);

                        // Check if this is a network/connectivity error, rate limit, or quota issue
                        const errorMessage = schemaError instanceof Error ? schemaError.message : String(schemaError);
                        if (errorMessage.includes('fetch failed') || errorMessage.includes('network') || errorMessage.includes('ECONNRESET') ||
                            errorMessage.includes('quota') || errorMessage.includes('rate limit') || errorMessage.includes('429') ||
                            errorMessage.includes('503') || errorMessage.includes('timeout')) {
                            console.log(`[generateSpanishTranslation] Network/quota/rate limit error detected for ${modelName}: ${errorMessage}`);

                            // For gemini-2.5-pro rate limits, add a small delay before trying next model
                            if (modelName.includes('2.5-pro') && (errorMessage.includes('quota') || errorMessage.includes('rate'))) {
                                console.log(`[generateSpanishTranslation] Adding delay due to gemini-2.5-pro rate limits...`);
                                await new Promise(resolve => setTimeout(resolve, 1000)); // Reduced delay to 1 second
                            }

                            lastError = `Network/quota error with ${modelName}: ${errorMessage}`;
                            continue; // Skip to next model instead of attempting text parsing
                        }

                        // Fallback to text parsing for other errors
                        try {
                            console.log(`[generateSpanishTranslation] Attempting text parsing fallback for ${modelName}...`);
                            const { text } = await localAi.generate({ prompt, config });
                            console.log(`[generateSpanishTranslation] Raw text response:`, text);

                            const jsonObject = extractValidJsonFromText(text);
                            if (jsonObject) {
                                try {
                                    const parsedOutput = JSON.parse(jsonObject) as z.infer<typeof AISpanishTranslationOutputSchema>;
                                    if (parsedOutput?.spanishChunks && Array.isArray(parsedOutput.spanishChunks)) {
                                        console.log(`[generateSpanishTranslation] Text parsing successful, ${parsedOutput.spanishChunks.length} chunks translated`);
                                        // Auto-save to Baserow
                                        await autoSaveTranslationToBaserow(input.userId, input.chunks, parsedOutput.spanishChunks, 'spanish');
                                        return { success: true, data: { spanishChunks: parsedOutput.spanishChunks } };
                                    }
                                } catch (parseError) {
                                    console.error("Failed to parse extracted JSON for Spanish translation:", parseError, "JSON:", jsonObject);
                                }
                            } else {
                                console.error("No valid JSON found in text response:", text);
                            }

                            // If text parsing fails, continue to next model
                            lastError = `Text parsing failed for ${modelName}`;
                            continue;
                        } catch (textError) {
                            console.error(`[generateSpanishTranslation] Text generation also failed for ${modelName}:`, textError);
                            lastError = `Both schema and text generation failed for ${modelName}`;
                            continue;
                        }
                    }
                } catch (modelError) {
                    const errorMsg = modelError instanceof Error ? modelError.message : 'Unknown error';
                    console.error(`[generateSpanishTranslation] Model ${modelName} failed:`, errorMsg);
                    lastError = `Model ${modelName}: ${errorMsg}`;

                    // If this is a network/fetch error, try next model immediately
                    if (errorMsg.includes('fetch failed') || errorMsg.includes('network')) {
                        continue;
                    }
                    // For other errors, still try next model but log the specific error
                    continue;
                }
            }

            // If all models failed, return detailed error
            console.error("All Google AI models failed for Spanish translation");
            return {
                success: false,
                error: `All Google AI models failed. Last error: ${lastError}. Models tried: ${modelsToTry.join(', ')}. This could be due to temporary API issues or rate limits. Please try again in a few minutes.`
            };
        } catch (error) {
            console.error("Error in generateSpanishTranslation setup:", error);
            return { success: false, error: "Failed to initialize Spanish translation with Google. This might be due to a timeout. Please try again." };
        }
    }
}

// Complete Romanian translation workflow that saves to Baserow
export async function generateAndSaveRomanianTranslation(
    storyId: string,
    input: GenerateRomanianTranslationInput
): Promise<{ success: boolean, data?: z.infer<typeof AIRomanianTranslationOutputSchema>, error?: string }> {
    console.log(`[generateAndSaveRomanianTranslation] Starting complete workflow for story ${storyId}`);
    
    // Step 1: Generate the translation
    const translationResult = await generateRomanianTranslation(input);
    
    if (!translationResult.success || !translationResult.data?.romanianChunks) {
        console.error(`[generateAndSaveRomanianTranslation] Translation failed:`, translationResult.error);
        return translationResult;
    }
    
    console.log(`[generateAndSaveRomanianTranslation] Translation successful, saving ${translationResult.data.romanianChunks.length} chunks to Baserow`);
    
    // Step 2: Get the current story
    const { getStory } = await import('../baserowStoryActions');
    const storyResult = await getStory(storyId, input.userId);
    
    if (!storyResult.success || !storyResult.data) {
        console.error(`[generateAndSaveRomanianTranslation] Failed to get story:`, storyResult.error);
        return { success: false, error: `Failed to get story for saving: ${storyResult.error}` };
    }
    
    // Step 3: Update the story with Romanian translation
    const updatedStory = {
        ...storyResult.data,
        romanianNarrationChunks: translationResult.data.romanianChunks
    };
    
    // Step 4: Save the updated story
    const { saveStory } = await import('../baserowStoryActions');
    const saveResult = await saveStory(updatedStory, input.userId);
    
    if (!saveResult.success) {
        console.error(`[generateAndSaveRomanianTranslation] Failed to save story:`, saveResult.error);
        return { success: false, error: `Translation completed but failed to save: ${saveResult.error}` };
    }
    
    console.log(`[generateAndSaveRomanianTranslation] Successfully saved Romanian translation to Baserow for story ${storyId}`);
    return translationResult;
}

export async function generateRomanianTranslation(input: GenerateRomanianTranslationInput): Promise<{ success: boolean, data?: z.infer<typeof AIRomanianTranslationOutputSchema>, error?: string }> {
    console.log(`[generateRomanianTranslation] Function called with userId: ${input.userId}, ${input.chunks.length} chunks`);
    
    // Add a unique request identifier to help track duplicates in logs
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    console.log(`[generateRomanianTranslation] Request ID: ${requestId}`);

    const userKeysResult = await getUserApiKeys(input.userId);
    if (!userKeysResult.success || !userKeysResult.data) {
        return { success: false, error: "Could not fetch user API keys." };
    }
    const userApiKeys = userKeysResult.data;

    // Check if this is a large translation that should be processed in smaller batches
    const totalTextLength = input.chunks.reduce((sum, chunk) => sum + chunk.text.length, 0);
    console.log(`[generateRomanianTranslation] Total text length: ${totalTextLength} characters, ${input.chunks.length} chunks`);

    // For large translations with many chunks, process in smaller batches to avoid rate limits
    const shouldUseBatching = input.chunks.length > 10 && input.aiProvider === 'google';
    if (shouldUseBatching) {
        console.log(`[generateRomanianTranslation] Large translation detected (${input.chunks.length} chunks), using batch processing to avoid rate limits`);
        try {
            const result = await processBatchTranslation(input, userApiKeys, 'romanian');
            
            // Auto-save batch processing results
            if (result.success && result.data?.romanianChunks) {
                await autoSaveTranslationToBaserow(input.userId, input.chunks, result.data.romanianChunks, 'romanian');
            }
            
            // For batch processing, ensure we return data in the correct schema format
            return {
                success: result.success,
                data: result.success && result.data?.romanianChunks ? { romanianChunks: result.data.romanianChunks } : undefined,
                error: result.error
            };
        } catch (error) {
            console.error(`[generateRomanianTranslation] Batch processing failed:`, error);
            return { 
                success: false, 
                error: `Batch processing failed: ${error instanceof Error ? error.message : 'Unknown error'}. This might be due to a timeout. Please try again.` 
            };
        }
    }

    // Format chunks for the prompt
    const chunksText = JSON.stringify(input.chunks, null, 2);

    if (input.aiProvider === 'perplexity') {
        if (!userApiKeys.perplexityApiKey) {
            return { success: false, error: "Perplexity API key not configured by user. Please set it in Account Settings." };
        }
        try {
            // Use original prompt and config for Perplexity (working fine)
            const prompt = romanianTranslationPromptTemplate.replace('{{chunks}}', chunksText);
            const { generateWithPerplexity } = await import('../../ai/genkit');
            const messages = [
                { role: 'system' as const, content: 'You are a professional English to Romanian translator specializing in children\'s stories. Translate accurately while maintaining natural, child-friendly Romanian.' },
                { role: 'user' as const, content: prompt }
            ];

            const resultText = await runFlow(generateWithPerplexity, {
                modelName: input.perplexityModel || 'sonar-reasoning-pro',
                messages: messages,
                userId: input.userId,
            });

            try {
                const extractedJson = extractJsonFromPerplexityResponse(resultText);
                const parsedOutput = JSON.parse(extractedJson) as (z.infer<typeof AIRomanianTranslationOutputSchema> & { translatedChunks?: string[] });

                if (parsedOutput?.error) {
                    return { success: false, error: parsedOutput.error };
                }
                // Handle both "romanianChunks" and "translatedChunks" formats
                if (parsedOutput?.romanianChunks && Array.isArray(parsedOutput.romanianChunks)) {
                    // Auto-save to Baserow
                    await autoSaveTranslationToBaserow(input.userId, input.chunks, parsedOutput.romanianChunks, 'romanian');
                    return { success: true, data: { romanianChunks: parsedOutput.romanianChunks } };
                }
                
                // Convert Perplexity's simple string array to expected object format
                if (parsedOutput?.translatedChunks && Array.isArray(parsedOutput.translatedChunks)) {
                    const romanianChunks = parsedOutput.translatedChunks.map((text: string, index: number) => ({
                        id: input.chunks[index]?.id || `chunk_${index}`,
                        text: text,
                        index: index
                    }));
                    // Auto-save to Baserow
                    await autoSaveTranslationToBaserow(input.userId, input.chunks, romanianChunks, 'romanian');
                    return { success: true, data: { romanianChunks } };
                }

                console.error('Perplexity AI did not return the expected romanianChunks or translatedChunks array:', parsedOutput);
                return { success: false, error: 'Failed to parse Romanian translation from Perplexity AI response.' };

            } catch (e) {
                console.error("Failed to parse JSON from Perplexity for Romanian translation:", resultText, e);
                return { success: false, error: "Failed to parse Romanian translation from Perplexity. Output was not valid JSON." };
            }

        } catch (error) {
            console.error("Error in generateRomanianTranslation with Perplexity AI call:", error);
            const errorMessage = error instanceof Error ? error.message : "Failed to generate Romanian translation with Perplexity.";
            return { success: false, error: errorMessage };
        }
    } else { // Default to Google AI
        if (!userApiKeys.googleApiKey) {
            return { success: false, error: "Google API key not configured by user. Please set it in Account Settings." };
        }
        try {
            // Use only the selected model - no fallback
            const selectedModel = input.googleScriptModel || 'gemini-2.5-pro';
            const modelsToTry = [selectedModel]; // Only try the selected model

            // Use Gemini-optimized prompt and config
            const prompt = romanianTranslationPromptTemplateGemini.replace('{{chunks}}', chunksText);

            let lastError: string = '';

            for (const modelName of modelsToTry) {
                try {
                    console.log(`[generateRomanianTranslation] Trying model: ${modelName}`);

                    const localAi = genkit({ plugins: [googleAI({ apiKey: userApiKeys.googleApiKey })], model: `googleai/${modelName}` });
                    const config = { temperature: 0.1, maxOutputTokens: 8192 };

                    // For Gemini 2.5 Pro, use even higher token limit
                    const isGemini25Pro = modelName.includes('2.5-pro');
                    if (isGemini25Pro) {
                        config.maxOutputTokens = 16384; // Higher limit for 2.5 Pro
                    }

                    // Try with schema validation first
                    try {
                        console.log(`[generateRomanianTranslation] Attempting schema validation for ${modelName}...`);
                        const { output } = await localAi.generate({
                            prompt,
                            output: { schema: AIRomanianTranslationOutputSchema, format: 'json' },
                            config
                        });

                        if (output?.romanianChunks && Array.isArray(output.romanianChunks) && output.romanianChunks.length > 0) {
                            console.log(`[generateRomanianTranslation] Schema validation successful, ${output.romanianChunks.length} chunks translated`);
                            // Auto-save to Baserow
                            await autoSaveTranslationToBaserow(input.userId, input.chunks, output.romanianChunks, 'romanian');
                            return { success: true, data: { romanianChunks: output.romanianChunks } };
                        } else {
                            console.log(`[generateRomanianTranslation] Schema output invalid:`, output);
                            throw new Error("Invalid schema output");
                        }
                    } catch (schemaError) {
                        console.log(`[generateRomanianTranslation] Schema validation failed for ${modelName}:`, schemaError);

                        // Check if this is a network/connectivity error, rate limit, or quota issue
                        const errorMessage = schemaError instanceof Error ? schemaError.message : String(schemaError);
                        if (errorMessage.includes('fetch failed') || errorMessage.includes('network') || errorMessage.includes('ECONNRESET') ||
                            errorMessage.includes('quota') || errorMessage.includes('rate limit') || errorMessage.includes('429') ||
                            errorMessage.includes('503') || errorMessage.includes('timeout')) {
                            console.log(`[generateRomanianTranslation] Network/quota/rate limit error detected for ${modelName}: ${errorMessage}`);

                            // For gemini-2.5-pro rate limits, add a small delay before trying next model
                            if (modelName.includes('2.5-pro') && (errorMessage.includes('quota') || errorMessage.includes('rate'))) {
                                console.log(`[generateRomanianTranslation] Adding delay due to gemini-2.5-pro rate limits...`);
                                await new Promise(resolve => setTimeout(resolve, 1000)); // Reduced delay to 1 second
                            }

                            lastError = `Network/quota error with ${modelName}: ${errorMessage}`;
                            continue; // Skip to next model instead of attempting text parsing
                        }

                        // Fallback to text parsing for other errors
                        try {
                            console.log(`[generateRomanianTranslation] Attempting text parsing fallback for ${modelName}...`);
                            const { text } = await localAi.generate({ prompt, config });
                            console.log(`[generateRomanianTranslation] Raw text response:`, text);

                            const jsonObject = extractValidJsonFromText(text);
                            if (jsonObject) {
                                try {
                                    const parsedOutput = JSON.parse(jsonObject) as z.infer<typeof AIRomanianTranslationOutputSchema>;
                                    if (parsedOutput?.romanianChunks && Array.isArray(parsedOutput.romanianChunks)) {
                                        console.log(`[generateRomanianTranslation] Text parsing successful, ${parsedOutput.romanianChunks.length} chunks translated`);
                                        // Auto-save to Baserow
                                        await autoSaveTranslationToBaserow(input.userId, input.chunks, parsedOutput.romanianChunks, 'romanian');
                                        return { success: true, data: { romanianChunks: parsedOutput.romanianChunks } };
                                    }
                                } catch (parseError) {
                                    console.error("Failed to parse extracted JSON for Romanian translation:", parseError, "JSON:", jsonObject);
                                }
                            } else {
                                console.error("No valid JSON found in text response:", text);
                            }

                            // If text parsing fails, continue to next model
                            lastError = `Text parsing failed for ${modelName}`;
                            continue;
                        } catch (textError) {
                            console.error(`[generateRomanianTranslation] Text generation also failed for ${modelName}:`, textError);
                            lastError = `Both schema and text generation failed for ${modelName}`;
                            continue;
                        }
                    }
                } catch (modelError) {
                    const errorMsg = modelError instanceof Error ? modelError.message : 'Unknown error';
                    console.error(`[generateRomanianTranslation] Model ${modelName} failed:`, errorMsg);
                    lastError = `Model ${modelName}: ${errorMsg}`;

                    // If this is a network/fetch error, try next model immediately
                    if (errorMsg.includes('fetch failed') || errorMsg.includes('network')) {
                        continue;
                    }
                    // For other errors, still try next model but log the specific error
                    continue;
                }
            }

            // If all models failed, return detailed error
            console.error("All Google AI models failed for Romanian translation");
            return {
                success: false,
                error: `All Google AI models failed. Last error: ${lastError}. Models tried: ${modelsToTry.join(', ')}. This could be due to temporary API issues or rate limits. Please try again in a few minutes.`
            };
        } catch (error) {
            console.error("Error in generateRomanianTranslation setup:", error);
            return { success: false, error: "Failed to initialize Romanian translation with Google. This might be due to a timeout. Please try again." };
        }
    }
}
