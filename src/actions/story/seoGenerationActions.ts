'use server';

import { genkit } from 'genkit';
import { googleAI } from '@genkit-ai/googleai';
import { runFlow } from '@genkit-ai/flow';
import { getUserApiKeys } from '../baserowApiKeyActions';

interface SeoGenerationParams {
  script: string;
  userId: string;
  aiProvider: 'google' | 'perplexity';
  perplexityModel?: string;
  googleScriptModel?: string;
}

interface SeoGenerationResult {
  youtubeTitle: string;
  youtubeDescription: string;
}

// Defensive filter to prevent "Made for Kids" classification
const checkForKidsContent = (text: string): string[] => {
  const negativeKeywords = [
    'for kids', 'for babies', 'toddlers', 'toddler', 'preschool', 'nursery rhyme', 
    'lullaby', 'learning colors', 'ABCs', 'alphabet song', 'sing-along', 'kids songs',
    'children\'s song', 'baby shark', 'learning numbers', 'counting song', 'educational for kids',
    'kids learning', 'children learning', 'baby learning', 'infant', 'baby music'
  ];
  
  const foundKeywords: string[] = [];
  const lowerText = text.toLowerCase();
  
  negativeKeywords.forEach(keyword => {
    if (lowerText.includes(keyword)) {
      foundKeywords.push(keyword);
    }
  });
  
  return foundKeywords;
};

// Semantic analysis to extract narrative elements
const extractNarrativeElements = (script: string) => {
  const elements = {
    character: '',
    characterTrait: '',
    setting: '',
    object: '',
    conflict: '',
    resolution: '',
    primaryEmotion: ''
  };
  
  // Use basic NLP to identify key elements
  const scriptLower = script.toLowerCase();
  
  // Character detection - look for common character words
  const characterWords = ['robot', 'alien', 'creature', 'character', 'hero', 'protagonist', 'being', 'entity'];
  const foundCharacter = characterWords.find(word => scriptLower.includes(word));
  if (foundCharacter) elements.character = foundCharacter;
  
  // Trait detection - look for descriptive adjectives
  const traitWords = ['lonely', 'curious', 'brave', 'small', 'tiny', 'mysterious', 'magical', 'gentle', 'wise', 'mischievous'];
  const foundTrait = traitWords.find(word => scriptLower.includes(word));
  if (foundTrait) elements.characterTrait = foundTrait;
  
  // Object detection - look for key nouns
  const objectWords = ['portal', 'cube', 'lamp', 'device', 'machine', 'artifact', 'crystal', 'orb', 'box', 'key'];
  const foundObject = objectWords.find(word => scriptLower.includes(word));
  if (foundObject) elements.object = foundObject;
  
  // Setting detection
  const settingWords = ['forest', 'space', 'factory', 'laboratory', 'garden', 'cave', 'room', 'house', 'world'];
  const foundSetting = settingWords.find(word => scriptLower.includes(word));
  if (foundSetting) elements.setting = foundSetting;
  
  // Emotion detection
  const emotionWords = ['heartwarming', 'funny', 'charming', 'exciting', 'mysterious', 'delightful', 'touching'];
  const foundEmotion = emotionWords.find(word => scriptLower.includes(word));
  if (foundEmotion) elements.primaryEmotion = foundEmotion;
  
  return elements;
};

// Generate multiple title variants using personal narrative templates
const generateTitleVariants = (elements: {
  character: string;
  characterTrait: string;
  setting: string;
  object: string;
  conflict: string;
  resolution: string;
  primaryEmotion: string;
}): string[] => {
  const variants: string[] = [];
  const character = elements.character || 'character';
  const trait = elements.characterTrait || '';
  const object = elements.object || 'mysterious thing';
  const setting = elements.setting || 'strange place';
  const emotion = elements.primaryEmotion || 'weird';
  
  // Template A: "The Time I..." narrative hook
  if (character && object) {
    variants.push(`The Time I Met a ${trait ? trait + ' ' : ''}${character.charAt(0).toUpperCase() + character.slice(1)} | HoloAnima Animated Storytime`);
  }
  
  // Template B: "How I..." narrative hook
  if (object || trait) {
    variants.push(`How I Discovered the ${object.charAt(0).toUpperCase() + object.slice(1)} | HoloAnima Stories`);
  }
  
  // Template C: "The Day..." narrative hook
  if (character && setting) {
    variants.push(`The Day Everything Changed in the ${setting.charAt(0).toUpperCase() + setting.slice(1)} | HoloAnima`);
  }
  
  // Template D: "Why I..." narrative hook
  if (emotion && character) {
    variants.push(`Why This ${emotion.charAt(0).toUpperCase() + emotion.slice(1)} ${character.charAt(0).toUpperCase() + character.slice(1)} Story Hit Different | HoloAnima`);
  }
  
  // Template E: Direct quirky statement
  if (character && object) {
    variants.push(`A ${trait ? trait + ' ' : ''}${character.charAt(0).toUpperCase() + character.slice(1)} Became My Hero | HoloAnima Stories`);
  }
  
  return variants.filter(v => v.length <= 60); // Optimal length for mobile
};

export async function generateSeoMetadata({
  script,
  userId,
  aiProvider,
  perplexityModel,
  googleScriptModel
}: SeoGenerationParams): Promise<{ success: boolean, data?: SeoGenerationResult, error?: string }> {
  try {
    console.log('Generating SEO metadata for script length:', script.length);
    
    // Extract narrative elements from script
    const elements = extractNarrativeElements(script);
    console.log('Extracted narrative elements:', elements);
    
    // Generate title variants using templates
    const titleVariants = generateTitleVariants(elements);
    console.log('Generated title variants:', titleVariants);
    
    // Create comprehensive prompt for AI
    const seoPrompt = `
You are an expert YouTube SEO specialist for animated storytelling content in the style of TheOdd1sOut/Jaiden Animations. Based on this animation script, generate ONE optimized YouTube title and description following these EXACT requirements:

SCRIPT:
${script}

EXTRACTED ELEMENTS:
- Character: ${elements.character}
- Character Trait: ${elements.characterTrait}
- Object/Item: ${elements.object}
- Setting: ${elements.setting}
- Primary Emotion: ${elements.primaryEmotion}

TITLE GENERATION RULES:
1. Start with a personal narrative hook: "The Time I...", "How I...", "The Day...", "Why I..." or something quirky/funny
2. Mention a unique/surprising element from the story
3. End with "| HoloAnima Animated Storytime" or "| HoloAnima Stories"
4. Capitalize major words but keep tone casual, not formal
5. Add ONE emoji at the end that fits the tone (🤔, 😅, 🌳, 🐢, ✨, 🚀, ❤️)
6. Maximum 60 characters for mobile optimization
7. AVOID "kids", "children", "babies", "toddlers" - this is for general audiences

DESCRIPTION GENERATION RULES:
1. Start with engaging first-person summary or direct audience address ("Ever wonder what it's like to..." or "When I met [character], I had no idea...")
2. Highlight what makes story interesting/funny/relatable with humor and playful language
3. Briefly mention the cast ("Meet [character names]...") and setting
4. Add audience interaction question ("Who's your favorite character? Let us know below!")
5. Call to action: "Subscribe for more weird, fun, and heartfelt stories!" with emojis
6. End with hashtags: #HoloAnima #AnimatedStorytime #LifeLessons
7. Use 1-2 emojis total as separators or at sentence ends
8. Keep conversational, authentic, casual - NOT generic or overly formal

EMOJI GUIDELINES:
- Use contextually relevant emojis: 🐢 (tortoise), 🌳 (tree), 🦉 (owl), 🐰 (bunny), 😅, 😂, 🤔, ✨, 🚀, ❤️
- Place at end of sentences or as visual separators
- 1-2 emojis maximum per title/description
- Support, don't overwhelm the text

SAMPLE TITLE VARIANTS TO CONSIDER:
${titleVariants.join('\n')}

EXAMPLE OUTPUT FORMAT:
Title: "The Day I Met the Whispering Willow 🌳 | HoloAnima Animated Storytime"
Description: "What happens when a shy tortoise discovers a tree that talks back? Turns out, a lot of weird (and kind of magical) stuff. 🐢✨  
Meet Shelly and her unlikely squad—Stella, Luna, Ollie, and the Whispering Willow tree—on this animated adventure about finding your voice in a noisy world.  
Have you ever felt misunderstood? Tell us your story in the comments!  

👉 Subscribe for more animated storytimes!  
#HoloAnima #AnimatedStorytime #LifeLessons"

Please respond with ONLY this JSON format:
{
  "youtubeTitle": "your optimized title here",
  "youtubeDescription": "your optimized description here"
}
`;

    const userKeysResult = await getUserApiKeys(userId);
    let response: string;

    // Generate using selected AI provider
    if (aiProvider === 'perplexity') {
      if (!userKeysResult.success || !userKeysResult.data?.perplexityApiKey) {
        return { success: false, error: "Perplexity API key not configured by user. Please set it in Account Settings." };
      }
      try {
        const { generateWithPerplexity } = await import('../../ai/genkit');

        const messages = [
          { role: 'system' as const, content: 'You are a YouTube SEO expert specializing in animation content. Generate optimized titles and descriptions that maximize click-through rates while avoiding "Made for Kids" classification.' },
          { role: 'user' as const, content: seoPrompt }
        ];

        response = await runFlow(generateWithPerplexity, {
          modelName: perplexityModel || 'sonar',
          messages: messages,
          userId: userId,
          contentType: 'script',
          useStructuredOutput: false,
        });

      } catch (error) {
        console.error("Error in generateSeoMetadata with Perplexity AI call:", error);
        const errorMessage = error instanceof Error ? error.message : "Failed to generate SEO metadata with Perplexity.";
        return { success: false, error: errorMessage };
      }
    } else { // Default to Google AI
      if (!userKeysResult.success || !userKeysResult.data?.googleApiKey) {
        return { success: false, error: "Google API key not configured by user. Please set it in Account Settings." };
      }
      const userGoogleKey = userKeysResult.data.googleApiKey;

      try {
        const modelName = googleScriptModel || 'gemini-2.0-flash';
        const localAi = genkit({ plugins: [googleAI({ apiKey: userGoogleKey })], model: `googleai/${modelName}` });
        const { text } = await localAi.generate({ prompt: seoPrompt });
        response = text || '';
      } catch (error) {
        console.error("Error in generateSeoMetadata AI call (Google):", error);
        return { success: false, error: "Failed to generate SEO metadata with user's Google key." };
      }
    }

    // Parse JSON response
    let parsedResponse: SeoGenerationResult;
    try {
      // Extract JSON from response if it contains extra text
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : response;
      parsedResponse = JSON.parse(jsonString);
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
      console.log('Raw response:', response);
      
      // Fallback: extract title and description manually
      const titleMatch = response.match(/title["\s]*:["\s]*([^"]*)/i);
      const descMatch = response.match(/description["\s]*:["\s]*([^"]*)/i);
      
      parsedResponse = {
        youtubeTitle: titleMatch?.[1] || "The Time I Found Something Amazing ✨ | HoloAnima",
        youtubeDescription: descMatch?.[1] || "Ever wonder what happens when you stumble into something totally unexpected? This story's got all the weird, heartfelt moments you didn't know you needed. 😅\n\nWho's your favorite character? Let us know below!\n\n👉 Subscribe for more animated storytimes!\n#HoloAnima #AnimatedStorytime #LifeLessons"
      };
    }

    // Apply defensive filter
    const titleWarnings = checkForKidsContent(parsedResponse.youtubeTitle);
    const descWarnings = checkForKidsContent(parsedResponse.youtubeDescription);
    
    if (titleWarnings.length > 0 || descWarnings.length > 0) {
      console.warn('Potential "Made for Kids" keywords detected:', { titleWarnings, descWarnings });
      
      // Apply automatic corrections
      let correctedTitle = parsedResponse.youtubeTitle;
      let correctedDesc = parsedResponse.youtubeDescription;
      
      [...titleWarnings, ...descWarnings].forEach(keyword => {
        const replacements: { [key: string]: string } = {
          'for kids': 'for everyone',
          'for babies': 'for all ages',
          'kids songs': 'delightful melodies',
          'children': 'viewers',
          'toddlers': 'everyone'
        };
        
        const replacement = replacements[keyword] || 'everyone';
        correctedTitle = correctedTitle.replace(new RegExp(keyword, 'gi'), replacement);
        correctedDesc = correctedDesc.replace(new RegExp(keyword, 'gi'), replacement);
      });
      
      parsedResponse.youtubeTitle = correctedTitle;
      parsedResponse.youtubeDescription = correctedDesc;
    }

    // Ensure title length constraint
    if (parsedResponse.youtubeTitle.length > 60) {
      console.warn('Title too long, truncating:', parsedResponse.youtubeTitle.length);
      parsedResponse.youtubeTitle = parsedResponse.youtubeTitle.substring(0, 57) + '...';
    }

    console.log('Generated SEO metadata:', parsedResponse);

    return {
      success: true,
      data: parsedResponse
    };

  } catch (error) {
    console.error('Error generating SEO metadata:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate SEO metadata'
    };
  }
}
