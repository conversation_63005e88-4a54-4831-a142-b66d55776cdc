"use server";

import { genkit } from 'genkit';
import { googleAI } from '@genkit-ai/googleai';
import { z } from 'zod';
import { runFlow } from '@genkit-ai/flow';

import {
    AIImagePromptsOutputSchema,
    type GenerateImagePromptsInput,
} from '../storyActionSchemas';

import { getUserApiKeys } from '../baserowApiKeyActions';
import {
    imagePromptsPromptTemplate
} from '../utils/promptTemplates';
import {
    extractJsonFromPerplexityResponse,
    extractValidJsonFromText
} from '../utils/storyHelpers';
import {
    uploadImageToMinIOStorage,
    uploadImageBufferToMinIOStorage,
    uploadVideoBufferToMinIOStorage
} from '../minioStorageActions';

// Utility function to transform action prompts for video generation
// Converts "@CharacterName" references to simple words for better AI understanding
function transformActionPromptForVideo(prompt: string): string {
    if (!prompt) return prompt;

    // Transform @references to simple character names
    // Example: "@OllieOtter" -> "Otter", "@SplashDuck" -> "Duck"
    const transformedPrompt = prompt
        // Handle specific character patterns first
        .replace(/@(\w*[Oo]tter\w*)/g, 'Otter')
        .replace(/@(\w*[Dd]uck\w*)/g, 'Duck')
        .replace(/@(\w*[Ff]rog\w*)/g, 'Frog')
        .replace(/@(\w*[Bb]ear\w*)/g, 'Bear')
        .replace(/@(\w*[Cc]at\w*)/g, 'Cat')
        .replace(/@(\w*[Dd]og\w*)/g, 'Dog')
        .replace(/@(\w*[Bb]ird\w*)/g, 'Bird')
        .replace(/@(\w*[Ff]ish\w*)/g, 'Fish')
        .replace(/@(\w*[Ll]ion\w*)/g, 'Lion')
        .replace(/@(\w*[Tt]iger\w*)/g, 'Tiger')
        .replace(/@(\w*[Ee]lephant\w*)/g, 'Elephant')
        .replace(/@(\w*[Mm]ouse\w*)/g, 'Mouse')
        .replace(/@(\w*[Rr]abbit\w*)/g, 'Rabbit')
        .replace(/@(\w*[Hh]orse\w*)/g, 'Horse')
        .replace(/@(\w*[Pp]ig\w*)/g, 'Pig')
        .replace(/@(\w*[Ss]heep\w*)/g, 'Sheep')
        .replace(/@(\w*[Cc]ow\w*)/g, 'Cow')
        .replace(/@(\w*[Gg]oat\w*)/g, 'Goat')
        // Handle location/item references
        .replace(/@(\w*[Pp]ond\w*)/g, 'pond')
        .replace(/@(\w*[Ll]ake\w*)/g, 'lake')
        .replace(/@(\w*[Ff]orest\w*)/g, 'forest')
        .replace(/@(\w*[Tt]ree\w*)/g, 'tree')
        .replace(/@(\w*[Hh]ouse\w*)/g, 'house')
        .replace(/@(\w*[Cc]astle\w*)/g, 'castle')
        .replace(/@(\w*[Bb]ridge\w*)/g, 'bridge')
        .replace(/@(\w*[Rr]iver\w*)/g, 'river')
        .replace(/@(\w*[Mm]ountain\w*)/g, 'mountain')
        .replace(/@(\w*[Bb]each\w*)/g, 'beach')
        // Generic fallback: extract the main word from compound names
        .replace(/@[A-Z][a-z]*([A-Z][a-z]+)/g, '$1') // CamelCase -> last word
        .replace(/@(\w+)/g, '$1'); // Any remaining @ references

    return transformedPrompt;
}



export async function generateImagePrompts(input: GenerateImagePromptsInput): Promise<{
    success: boolean;
    data?: z.infer<typeof AIImagePromptsOutputSchema>;
    error?: string;
    partialSuccess?: boolean;
    completedBatches?: number;
    totalBatches?: number;
}> {
    const userKeysResult = await getUserApiKeys(input.userId);

    if (input.aiProvider === 'perplexity') {
        if (!userKeysResult.success || !userKeysResult.data?.perplexityApiKey) {
            return { success: false, error: "Perplexity API key not configured by user. Please set it in Account Settings." };
        }

        try {
            const { generateWithPerplexity } = await import('../../ai/genkit');

            // Build the prompt with chunk data if available
            let finalPrompt = imagePromptsPromptTemplate
                .replace('{{{characterPrompts}}}', input.characterPrompts || '')
                .replace('{{{locationPrompts}}}', input.locationPrompts || '')
                .replace('{{{itemPrompts}}}', input.itemPrompts || '')
                .replace('{{{script}}}', input.script || '')
                .replace('{{numImages}}', '10'); // Default to 10 images

            // Handle chunks data if provided (this would be added to the input type)
            // For now, skip chunks processing in this simplified version
            if (false) {
                let chunksSection = '';
                [{ text: '', duration: 0, promptCount: 0 }].forEach((chunk, index: number) => {
                    chunksSection += `**Narration Chunk ${index} (Duration: ${chunk.duration}s, Required prompts: ${chunk.promptCount}):**\n`;
                    chunksSection += `"${chunk.text}"\n\n`;
                    chunksSection += `**For THIS CHUNK, generate ${chunk.promptCount} image prompt(s).**\n---\n`;
                });
                finalPrompt = finalPrompt.replace('{{#if chunksData}}**SOUND CHUNK CORRELATION MODE:**', `**SOUND CHUNK CORRELATION MODE:**\n${chunksSection}`);
                finalPrompt = finalPrompt.replace('{{else}}**FALLBACK MODE (No narration chunks provided):**', '');
                finalPrompt = finalPrompt.replace('{{/if}}', '');
            } else {
                finalPrompt = finalPrompt.replace('{{#if chunksData}}**SOUND CHUNK CORRELATION MODE:**', '**FALLBACK MODE (No narration chunks provided):**');
                finalPrompt = finalPrompt.replace('{{else}}**FALLBACK MODE (No narration chunks provided):**', '');
                finalPrompt = finalPrompt.replace('{{/if}}', '');
            }

            const messages = [
                { role: 'system' as const, content: 'You are an expert at creating detailed image prompts for AI image generation. Follow the instructions precisely to generate visual prompts based on narration chunks or story content.' },
                { role: 'user' as const, content: finalPrompt }
            ];

            const resultText = await runFlow(generateWithPerplexity, {
                modelName: input.perplexityModel || 'sonar-reasoning-pro',
                messages: messages,
                userId: input.userId,
                useStructuredOutput: true,
            });

            const cleanedText = extractJsonFromPerplexityResponse(resultText);
            const jsonString = extractValidJsonFromText(cleanedText);

            if (!jsonString) {
                console.error("Could not extract valid JSON from Perplexity response:", cleanedText.substring(0, 200));
                return { success: false, error: "Failed to parse image prompts from Perplexity response." };
            }

            let parsedResult;
            try {
                parsedResult = JSON.parse(jsonString);
            } catch (parseError) {
                console.error("Failed to parse JSON:", parseError, "Raw JSON:", jsonString);
                return { success: false, error: "Invalid JSON format in image prompts response." };
            }

            if (!parsedResult.imagePrompts || !Array.isArray(parsedResult.imagePrompts)) {
                console.error("Invalid response structure:", parsedResult);
                return { success: false, error: "Response does not contain valid imagePrompts array." };
            }

            return {
                success: true,
                data: {
                    imagePrompts: parsedResult.imagePrompts,
                    actionPrompts: parsedResult.actionPrompts || []
                }
            };

        } catch (error) {
            console.error("Error in generateImagePrompts with Perplexity AI call:", error);
            const errorMessage = error instanceof Error ? error.message : "Failed to generate image prompts with Perplexity.";
            return { success: false, error: errorMessage };
        }
    } else {
        // Google AI implementation would go here
        if (!userKeysResult.success || !userKeysResult.data?.googleApiKey) {
            return { success: false, error: "Google API key not configured by user. Please set it in Account Settings." };
        }

        try {
            const modelName = input.googleScriptModel || 'gemini-2.0-flash';
            const localAi = genkit({ plugins: [googleAI({ apiKey: userKeysResult.data.googleApiKey })], model: `googleai/${modelName}` });

            // Similar implementation for Google AI...
            const prompt = imagePromptsPromptTemplate; // Build prompt similar to above
            const { output } = await localAi.generate({ prompt, output: { schema: AIImagePromptsOutputSchema, format: 'json' } });

            return { success: true, data: output! };
        } catch (error) {
            console.error("Error in generateImagePrompts AI call (Google):", error);
            return { success: false, error: "Failed to generate image prompts with Google." };
        }
    }
}

export async function generateImageFromGemini(
    originalPrompt: string,
    userId: string,
    storyId?: string
): Promise<{ success: boolean; imageUrl?: string; error?: string; requestPrompt?: string; width?: number; height?: number }> {
    const userKeysResult = await getUserApiKeys(userId);
    if (!userKeysResult.success || !userKeysResult.data?.geminiApiKey) {
        return { success: false, error: "Gemini API key not configured by user. Please set it in Account Settings." };
    }
    const apiKey = userKeysResult.data.geminiApiKey;

    const styles = "3D, Cartoon, High Quality, 16:9 aspect ratio, detailed, sharp, professional photography";
    const requestPrompt = originalPrompt ? `${originalPrompt}, ${styles}` : styles;

    // Standard 16:9 dimensions
    const width = 1024;
    const height = 576;

    try {
        console.log(`Calling Gemini API with prompt: "${requestPrompt}" using user's key.`);

        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-preview-image-generation:generateContent?key=${apiKey}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                contents: [{
                    parts: [
                        { text: requestPrompt }
                    ]
                }],
                generationConfig: { responseModalities: ["TEXT", "IMAGE"] }
            }),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("Gemini API Error Response:", errorText);
            return { success: false, error: `Gemini API request failed: ${response.status}`, requestPrompt };
        }

        const result = await response.json();
        const candidate = result.candidates?.[0];
        const parts = candidate?.content?.parts || [];

        let imageData = null;
        for (const part of parts) {
            if (part.inlineData && part.inlineData.data) {
                imageData = part.inlineData.data;
                break;
            }
        }

        if (!imageData) {
            console.error("No image data from Gemini. Full API response:", JSON.stringify(result, null, 2));
            return { success: false, error: "No image data returned from Gemini API", requestPrompt };
        }

        if (userId && storyId) {
            try {
                const safePrompt = originalPrompt.substring(0, 30).replace(/[^a-z0-9]/gi, '_').toLowerCase();
                const imageName = `gemini_${Date.now()}_${safePrompt}`;
                const imageBuffer = Buffer.from(imageData, 'base64');
                const firebaseUrl = await uploadImageBufferToMinIOStorage(imageBuffer, userId, storyId, imageName, 'image/png');
                return { success: true, imageUrl: firebaseUrl, requestPrompt, width, height };
            } catch (uploadError) {
                console.error("Error uploading image to MinIO Storage:", uploadError);
                return { success: true, imageUrl: `data:image/png;base64,${imageData}`, requestPrompt, width, height };
            }
        }
        return { success: true, imageUrl: `data:image/png;base64,${imageData}`, requestPrompt, width, height };
    } catch (error: unknown) {
        console.error("Error calling Gemini API:", error);
        const message = error instanceof Error ? error.message : "An unknown error occurred while generating the image.";
        return { success: false, error: message, requestPrompt };
    }
}

export async function generateImageFromImagen3(
    originalPrompt: string, // This is the prompt with @placeholders, e.g. "@Mika sniffs @JuicyApple"
    userId: string,
    storyId?: string,
    styleId?: string
): Promise<{ success: boolean; imageUrl?: string; error?: string; requestPrompt?: string; expandedPrompt?: string; width?: number; height?: number }> {
    const userKeysResult = await getUserApiKeys(userId);
    if (!userKeysResult.success || !userKeysResult.data?.googleApiKey) {
        return { success: false, error: "Google API key for Imagen3 not configured by user. Please set it in Account Settings." };
    }
    const apiKey = userKeysResult.data.googleApiKey;

    // Use the new prompt builder for improved results
    const { buildOptimizedImagenPrompt, extractTechnicalModifiers, getStandardNegativePrompt, validatePrompt } = await import('@/utils/imagenPromptBuilder');

    interface EntityDescription {
        name: string;
        description: string;
        type: 'character' | 'item' | 'location';
    }

    const entities: EntityDescription[] = [];

    if (userId && storyId) {
        try {
            const { getStory } = await import('@/actions/baserowStoryActions');
            const storyResult = await getStory(storyId, userId);
            if (storyResult.success && storyResult.data) {
                const { nameToReference, extractEntityNames } = await import('@/app/(app)/assemble-video/utils');
                const entityReferencesInPrompt = Array.from(new Set(originalPrompt.match(/@[A-Za-z0-9_]+/g) || []));

                const allEntityNamesFromStory = extractEntityNames(storyResult.data);

                console.log(`[Imagen3] Found ${entityReferencesInPrompt.length} entity references in prompt:`, entityReferencesInPrompt);
                console.log(`[Imagen3] Available story entities:`, allEntityNamesFromStory);

                // Helper function to normalize references for robust comparison
                const normalizeRefForComparison = (ref: string): string => {
                    if (!ref.startsWith('@')) return ref.toLowerCase().replace(/[^a-z0-9]/g, '');
                    return '@' + ref.substring(1).toLowerCase().replace(/[^a-z0-9]/g, '');
                };

                for (const ref of entityReferencesInPrompt) {
                    let actualEntityName: string | null = null;
                    let entityType: 'character' | 'item' | 'location' | null = null;
                    let descriptionText: string | null = null;

                    const normalizedRef = normalizeRefForComparison(ref);

                    // Check characters with case-insensitive matching and fuzzy matching
                    for (const charName of allEntityNamesFromStory.characters) {
                        const generatedRef = nameToReference(charName);
                        const normalizedGeneratedRef = normalizeRefForComparison(generatedRef);
                        const normalizedCharName = normalizeRefForComparison('@' + charName.replace(/[^A-Za-z0-9]/g, ''));

                        if (normalizedGeneratedRef === normalizedRef || normalizedCharName === normalizedRef) {
                            actualEntityName = charName;
                            entityType = 'character';
                            break;
                        }
                    }

                    // Check items if not found
                    if (!actualEntityName) {
                        for (const itemName of allEntityNamesFromStory.items) {
                            const generatedRef = nameToReference(itemName);
                            const normalizedGeneratedRef = normalizeRefForComparison(generatedRef);
                            const normalizedItemName = normalizeRefForComparison('@' + itemName.replace(/[^A-Za-z0-9]/g, ''));

                            if (normalizedGeneratedRef === normalizedRef || normalizedItemName === normalizedRef) {
                                actualEntityName = itemName;
                                entityType = 'item';
                                break;
                            }
                        }
                    }

                    // Check locations if not found
                    if (!actualEntityName) {
                        console.log(`[Imagen3] Looking for location match for "${ref}"`);
                        for (const locName of allEntityNamesFromStory.locations) {
                            const generatedRef = nameToReference(locName);
                            const normalizedGeneratedRef = normalizeRefForComparison(generatedRef);
                            const normalizedLocName = normalizeRefForComparison('@' + locName.replace(/[^A-Za-z0-9]/g, ''));

                            console.log(`[Imagen3] Testing "${locName}" -> generatedRef: "${generatedRef}", normalized: "${normalizedGeneratedRef}" vs "${normalizedRef}"`);

                            if (normalizedGeneratedRef === normalizedRef || normalizedLocName === normalizedRef) {
                                actualEntityName = locName;
                                entityType = 'location';
                                console.log(`[Imagen3] ✅ MATCH FOUND! "${ref}" -> "${locName}"`);
                                break;
                            }
                        }
                    }

                    if (actualEntityName && entityType) {
                        const promptsSection = entityType === 'character' ? storyResult.data.detailsPrompts?.characterPrompts || ''
                            : entityType === 'item' ? storyResult.data.detailsPrompts?.itemPrompts || ''
                                : storyResult.data.detailsPrompts?.locationPrompts || '';

                        const escapedEntityName = actualEntityName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                        const entityPattern = new RegExp(`^\\s*${escapedEntityName}\\s*\\n(.*?)(?=\\n\\n|$)`, "ms");
                        const entityMatch = promptsSection.match(entityPattern);

                        if (entityMatch && entityMatch[1]) {
                            descriptionText = entityMatch[1].trim();
                            entities.push({
                                name: actualEntityName,
                                description: descriptionText,
                                type: entityType
                            });
                        } else {
                            console.warn(`[Imagen3] No description found for ${entityType} "${actualEntityName}" (ref: ${ref}). Name will remain in scene instruction.`);
                            entities.push({
                                name: actualEntityName,
                                description: '(No detailed description provided for direct API use)',
                                type: entityType
                            });
                        }
                    } else {
                        console.warn(`[Imagen3] ❌ Entity for reference "${ref}" not found in story data. Ref will remain in scene instruction.`);
                        console.warn(`[Imagen3] Checked ${allEntityNamesFromStory.characters.length} characters, ${allEntityNamesFromStory.items.length} items, ${allEntityNamesFromStory.locations.length} locations`);
                    }
                }
            } else {
                console.warn("[Imagen3] Failed to get story data for placeholder expansion.");
            }
        } catch (error) { console.warn("[generateImageFromImagen3] Error processing placeholders:", error); }
    }

    // Get style string
    let styleStringApplicable: string | undefined;
    if (styleId) {
        try {
            const { getStylePromptForProvider } = await import('@/utils/imageStyleUtils');
            styleStringApplicable = getStylePromptForProvider(styleId as string, 'imagen3');
        } catch (error) { console.warn("[generateImageFromImagen3] Failed to apply style from styleId:", error); }
    } else if (userId && storyId) {
        try {
            const { getStory } = await import('@/actions/baserowStoryActions');
            const storyResult = await getStory(storyId, userId);
            if (storyResult.success && storyResult.data?.imageStyleId) {
                const { getStylePromptForProvider } = await import('@/utils/imageStyleUtils');
                styleStringApplicable = getStylePromptForProvider(storyResult.data.imageStyleId as string, 'imagen3');
            }
        } catch (error) { console.warn("[generateImageFromImagen3] Failed to apply style from story:", error); }
    }

    // Extract technical modifiers and build optimized prompt
    const technicalModifiers = extractTechnicalModifiers(originalPrompt, {
        quality: 'high quality, detailed',
        cameraAngle: 'wide shot'
    });

    const promptResult = buildOptimizedImagenPrompt({
        entities,
        actionPrompt: originalPrompt,
        styleString: styleStringApplicable,
        technicalModifiers,
        negativePrompt: getStandardNegativePrompt('child-friendly')
    });

    // Validate the prompt
    const validation = validatePrompt(promptResult.prompt);
    if (!validation.isValid) {
        console.warn('[Imagen3] Prompt validation warnings:', validation.suggestions);
    }

    const requestPromptWithStyle = promptResult.prompt;
    const expandedPromptForExport = requestPromptWithStyle;

    // Standard 16:9 dimensions for Imagen3
    const width = 1024;
    const height = 576;

    try {
        console.log(`Calling Imagen 3 API with structured prompt: \n"${requestPromptWithStyle}"\nUsing user's key.`);
        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=${apiKey}`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                instances: [{ prompt: requestPromptWithStyle }],
                parameters: {
                    sampleCount: 1,
                    aspectRatio: "16:9",
                    personGeneration: "ALLOW_ALL",
                    safetySettings: [
                        { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
                        { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' },
                        { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' },
                        { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' },
                    ],
                }
            }),
        });

        const result = await response.json();

        if (!response.ok) {
            console.error("Imagen 3 API Error Response:", JSON.stringify(result, null, 2));
            return { success: false, error: `Imagen 3 API request failed: ${response.status} - ${result?.error?.message || 'Unknown error'}`, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport };
        }

        const predictions = result.predictions;
        if (!predictions || predictions.length === 0) {
            console.error("Imagen 3 API returned no predictions. Full response:", JSON.stringify(result, null, 2));
            return { success: false, error: "No image data returned from Imagen 3 API", requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport };
        }
        const imageData = predictions[0]?.bytesBase64Encoded;
        if (!imageData) {
            console.error("Imagen 3 API returned prediction but no image bytes. Full response:", JSON.stringify(result, null, 2));
            return { success: false, error: "No image bytes in Imagen 3 response", requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport };
        }

        if (userId && storyId) {
            try {
                const safePrompt = originalPrompt.substring(0, 30).replace(/[^a-z0-9]/gi, '_').toLowerCase();
                const imageName = `imagen3_${Date.now()}_${safePrompt}`;
                const imageBuffer = Buffer.from(imageData, 'base64');
                const firebaseUrl = await uploadImageBufferToMinIOStorage(imageBuffer, userId, storyId, imageName, 'image/png');
                return { success: true, imageUrl: firebaseUrl, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport, width, height };
            } catch (uploadError) {
                console.error("Error uploading image to MinIO Storage:", uploadError);
                return { success: true, imageUrl: `data:image/png;base64,${imageData}`, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport, width, height };
            }
        }
        return { success: true, imageUrl: `data:image/png;base64,${imageData}`, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport, width, height };
    } catch (error: unknown) {
        console.error("Error calling Imagen 3 API:", error);
        const message = error instanceof Error ? error.message : "An unknown error occurred while generating the image.";
        return { success: false, error: message, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport };
    }
}

export async function generateImageFromImagen4(
    originalPrompt: string, // This is the prompt with @placeholders, e.g. "@Mika sniffs @JuicyApple"
    userId: string,
    storyId?: string,
    styleId?: string
): Promise<{ success: boolean; imageUrl?: string; error?: string; requestPrompt?: string; expandedPrompt?: string; width?: number; height?: number }> {
    const userKeysResult = await getUserApiKeys(userId);
    if (!userKeysResult.success || !userKeysResult.data?.googleApiKey) {
        return { success: false, error: "Google API key for Imagen4 not configured by user. Please set it in Account Settings." };
    }
    const apiKey = userKeysResult.data.googleApiKey;

    // Use the new prompt builder for improved results
    const { buildOptimizedImagenPrompt, extractTechnicalModifiers, getStandardNegativePrompt, validatePrompt } = await import('@/utils/imagenPromptBuilder');

    interface EntityDescription {
        name: string;
        description: string;
        type: 'character' | 'item' | 'location';
    }

    const entities: EntityDescription[] = [];

    if (userId && storyId) {
        try {
            const { getStory } = await import('@/actions/baserowStoryActions');
            const storyResult = await getStory(storyId, userId);
            if (storyResult.success && storyResult.data) {
                const { nameToReference, extractEntityNames } = await import('@/app/(app)/assemble-video/utils');
                const entityReferencesInPrompt = Array.from(new Set(originalPrompt.match(/@[A-Za-z0-9_]+/g) || [])); // Unique references, include dots and handle various characters

                const allEntityNamesFromStory = extractEntityNames(storyResult.data);

                console.log(`[Imagen4] Found ${entityReferencesInPrompt.length} entity references in prompt:`, entityReferencesInPrompt);
                console.log(`[Imagen4] Available story entities:`, allEntityNamesFromStory);

                // Helper function to normalize references for robust comparison (matching utils.ts)
                const normalizeRefForComparison = (ref: string): string => {
                    if (!ref.startsWith('@')) return ref.toLowerCase().replace(/[^a-z0-9]/g, '');
                    return '@' + ref.substring(1).toLowerCase().replace(/[^a-z0-9]/g, '');
                };

                for (const ref of entityReferencesInPrompt) {
                    let actualEntityName: string | null = null;
                    let entityType: 'character' | 'item' | 'location' | null = null;
                    let descriptionText: string | null = null;

                    const normalizedRef = normalizeRefForComparison(ref);

                    // Check characters with case-insensitive matching and fuzzy matching
                    for (const charName of allEntityNamesFromStory.characters) {
                        const generatedRef = nameToReference(charName);
                        const normalizedGeneratedRef = normalizeRefForComparison(generatedRef);
                        const normalizedCharName = normalizeRefForComparison('@' + charName.replace(/[^A-Za-z0-9]/g, ''));

                        if (normalizedGeneratedRef === normalizedRef || normalizedCharName === normalizedRef) {
                            actualEntityName = charName;
                            entityType = 'character';
                            break;
                        }
                    }

                    // Check items if not found
                    if (!actualEntityName) {
                        for (const itemName of allEntityNamesFromStory.items) {
                            const generatedRef = nameToReference(itemName);
                            const normalizedGeneratedRef = normalizeRefForComparison(generatedRef);
                            const normalizedItemName = normalizeRefForComparison('@' + itemName.replace(/[^A-Za-z0-9]/g, ''));

                            if (normalizedGeneratedRef === normalizedRef || normalizedItemName === normalizedRef) {
                                actualEntityName = itemName;
                                entityType = 'item';
                                break;
                            }
                        }
                    }

                    // Check locations if not found
                    if (!actualEntityName) {
                        console.log(`[Imagen4] Looking for location match for "${ref}"`);
                        for (const locName of allEntityNamesFromStory.locations) {
                            const generatedRef = nameToReference(locName);
                            const normalizedGeneratedRef = normalizeRefForComparison(generatedRef);
                            const normalizedLocName = normalizeRefForComparison('@' + locName.replace(/[^A-Za-z0-9]/g, ''));

                            console.log(`[Imagen4] Testing "${locName}" -> generatedRef: "${generatedRef}", normalized: "${normalizedGeneratedRef}" vs "${normalizedRef}"`);

                            if (normalizedGeneratedRef === normalizedRef || normalizedLocName === normalizedRef) {
                                actualEntityName = locName;
                                entityType = 'location';
                                console.log(`[Imagen4] ✅ MATCH FOUND! "${ref}" -> "${locName}"`);
                                break;
                            }
                        }
                    }

                    if (actualEntityName && entityType) {
                        const promptsSection = entityType === 'character' ? storyResult.data.detailsPrompts?.characterPrompts || ''
                            : entityType === 'item' ? storyResult.data.detailsPrompts?.itemPrompts || ''
                                : storyResult.data.detailsPrompts?.locationPrompts || '';

                        const escapedEntityName = actualEntityName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                        const entityPattern = new RegExp(`^\\s*${escapedEntityName}\\s*\\n(.*?)(?=\\n\\n|$)`, "ms");
                        const entityMatch = promptsSection.match(entityPattern);

                        if (entityMatch && entityMatch[1]) {
                            descriptionText = entityMatch[1].trim();
                            entities.push({
                                name: ref.substring(1), // Use the reference name without @ for exact matching in buildOptimizedImagenPrompt
                                description: descriptionText,
                                type: entityType
                            });
                        } else {
                            console.warn(`[Imagen4] No description found for ${entityType} "${actualEntityName}" (ref: ${ref}). Name will remain in scene instruction.`);
                            entities.push({
                                name: ref.substring(1), // Use the reference name without @ for exact matching in buildOptimizedImagenPrompt
                                description: '(No detailed description provided for direct API use)',
                                type: entityType
                            });
                        }
                    } else {
                        console.warn(`[Imagen4] ❌ Entity for reference "${ref}" not found in story data. Ref will remain in scene instruction.`);
                        console.warn(`[Imagen4] Checked ${allEntityNamesFromStory.characters.length} characters, ${allEntityNamesFromStory.items.length} items, ${allEntityNamesFromStory.locations.length} locations`);
                    }
                }
            } else {
                console.warn("[Imagen4] Failed to get story data for placeholder expansion.");
            }
        } catch (error) { console.warn("[generateImageFromImagen4] Error processing placeholders:", error); }
    }

    // Get style string
    let styleStringApplicable: string | undefined;
    if (styleId) {
        try {
            const { getStylePromptForProvider } = await import('@/utils/imageStyleUtils');
            styleStringApplicable = getStylePromptForProvider(styleId as string, 'imagen4');
        } catch (error) { console.warn("[generateImageFromImagen4] Failed to apply style from styleId:", error); }
    } else if (userId && storyId) {
        try {
            const { getStory } = await import('@/actions/baserowStoryActions');
            const storyResult = await getStory(storyId, userId);
            if (storyResult.success && storyResult.data?.imageStyleId) {
                const { getStylePromptForProvider } = await import('@/utils/imageStyleUtils');
                styleStringApplicable = getStylePromptForProvider(storyResult.data.imageStyleId as string, 'imagen4');
            }
        } catch (error) { console.warn("[generateImageFromImagen4] Failed to apply style from story:", error); }
    }

    // Extract technical modifiers and build optimized prompt for Imagen 4
    const technicalModifiers = extractTechnicalModifiers(originalPrompt, {
        quality: '4K, HDR, high quality, detailed',
        cameraAngle: 'wide shot'
    });

    const promptResult = buildOptimizedImagenPrompt({
        entities,
        actionPrompt: originalPrompt,
        styleString: styleStringApplicable,
        technicalModifiers,
        negativePrompt: getStandardNegativePrompt('child-friendly')
    });

    // Validate the prompt
    const validation = validatePrompt(promptResult.prompt);
    if (!validation.isValid) {
        console.warn('[Imagen4] Prompt validation warnings:', validation.suggestions);
    }

    const requestPromptWithStyle = promptResult.prompt;
    const expandedPromptForExport = requestPromptWithStyle;

    // Standard 16:9 dimensions for Imagen4 (updated to higher resolution supported by Imagen 4)
    const width = 1408;
    const height = 768;

    try {
        console.log(`Calling Imagen 4 API with structured prompt: \n"${requestPromptWithStyle}"\nUsing user's key.`);

        // Try the Vertex AI predict endpoint similar to Imagen 3
        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/imagen-4.0-generate-preview-06-06:predict?key=${apiKey}`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                instances: [{ prompt: requestPromptWithStyle }],
                parameters: {
                    sampleCount: 1,
                    aspectRatio: "16:9",
                    personGeneration: "ALLOW_ALL",
                    safetySettings: [
                        { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
                        { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' },
                        { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' },
                        { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' },
                    ],
                }
            }),
        });

        let result;
        try {
            result = await response.json();
        } catch (parseError) {
            console.error("Failed to parse Imagen 4 API response as JSON:", parseError);
            const text = await response.text();
            console.error("Raw response:", text);
            return { success: false, error: `Invalid JSON response from Imagen 4 API. Status: ${response.status}. The Imagen 4 model may not be available yet through the REST API.`, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport };
        }

        if (!response.ok) {
            console.error("Imagen 4 API Error Response:", JSON.stringify(result, null, 2));
            return { success: false, error: `Imagen 4 API request failed: ${response.status} - ${result?.error?.message || 'Unknown error'}`, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport };
        }

        const predictions = result.predictions;
        if (!predictions || predictions.length === 0) {
            console.error("Imagen 4 API returned no predictions. Full response:", JSON.stringify(result, null, 2));
            return { success: false, error: "No image data returned from Imagen 4 API", requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport };
        }
        const imageData = predictions[0]?.bytesBase64Encoded;
        if (!imageData) {
            console.error("Imagen 4 API returned prediction but no image bytes. Full response:", JSON.stringify(result, null, 2));
            return { success: false, error: "No image bytes in Imagen 4 response", requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport };
        }

        if (userId && storyId) {
            try {
                const safePrompt = originalPrompt.substring(0, 30).replace(/[^a-z0-9]/gi, '_').toLowerCase();
                const imageName = `imagen4_${Date.now()}_${safePrompt}`;
                const imageBuffer = Buffer.from(imageData, 'base64');
                const firebaseUrl = await uploadImageBufferToMinIOStorage(imageBuffer, userId, storyId, imageName, 'image/png');
                return { success: true, imageUrl: firebaseUrl, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport, width, height };
            } catch (uploadError) {
                console.error("Error uploading image to MinIO Storage:", uploadError);
                return { success: true, imageUrl: `data:image/png;base64,${imageData}`, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport, width, height };
            }
        }
        return { success: true, imageUrl: `data:image/png;base64,${imageData}`, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport, width, height };
    } catch (error: unknown) {
        console.error("Error calling Imagen 4 API:", error);
        const message = error instanceof Error ? error.message : "An unknown error occurred while generating the image.";
        return { success: false, error: message, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport };
    }
}

export async function generateImageFromImagen4Ultra(
    originalPrompt: string, // This is the prompt with @placeholders, e.g. "@Mika sniffs @JuicyApple"
    userId: string,
    storyId?: string,
    styleId?: string
): Promise<{ success: boolean; imageUrl?: string; error?: string; requestPrompt?: string; expandedPrompt?: string; width?: number; height?: number }> {
    const userKeysResult = await getUserApiKeys(userId);
    if (!userKeysResult.success || !userKeysResult.data?.googleApiKey) {
        return { success: false, error: "Google API key for Imagen 4 Ultra not configured by user. Please set it in Account Settings." };
    }
    const apiKey = userKeysResult.data.googleApiKey;

    // Use the new prompt builder for improved results
    const { buildOptimizedImagenPrompt, extractTechnicalModifiers, getStandardNegativePrompt, validatePrompt } = await import('@/utils/imagenPromptBuilder');

    interface EntityDescription {
        name: string;
        description: string;
        type: 'character' | 'item' | 'location';
    }

    const entities: EntityDescription[] = [];

    if (userId && storyId) {
        try {
            const { getStory } = await import('@/actions/baserowStoryActions');
            const storyResult = await getStory(storyId, userId);
            if (storyResult.success && storyResult.data) {
                const { nameToReference, extractEntityNames } = await import('@/app/(app)/assemble-video/utils');
                const entityReferencesInPrompt = Array.from(new Set(originalPrompt.match(/@[A-Za-z0-9_]+/g) || [])); // Unique references, include dots and handle various characters

                const allEntityNamesFromStory = extractEntityNames(storyResult.data);

                console.log(`[Imagen4Ultra] Found ${entityReferencesInPrompt.length} entity references in prompt:`, entityReferencesInPrompt);
                console.log(`[Imagen4Ultra] Available story entities:`, allEntityNamesFromStory);

                // Helper function to normalize references for robust comparison (matching utils.ts)
                const normalizeRefForComparison = (ref: string): string => {
                    if (!ref.startsWith('@')) return ref.toLowerCase().replace(/[^a-z0-9]/g, '');
                    return '@' + ref.substring(1).toLowerCase().replace(/[^a-z0-9]/g, '');
                };

                for (const ref of entityReferencesInPrompt) {
                    let actualEntityName: string | null = null;
                    let entityType: 'character' | 'item' | 'location' | null = null;
                    let descriptionText: string | null = null;

                    const normalizedRef = normalizeRefForComparison(ref);

                    // Check characters with case-insensitive matching and fuzzy matching
                    for (const charName of allEntityNamesFromStory.characters) {
                        const generatedRef = nameToReference(charName);
                        const normalizedGeneratedRef = normalizeRefForComparison(generatedRef);
                        const normalizedCharName = normalizeRefForComparison('@' + charName.replace(/[^A-Za-z0-9]/g, ''));

                        if (normalizedGeneratedRef === normalizedRef || normalizedCharName === normalizedRef) {
                            actualEntityName = charName;
                            entityType = 'character';
                            break;
                        }
                    }

                    // Check items if not found
                    if (!actualEntityName) {
                        for (const itemName of allEntityNamesFromStory.items) {
                            const generatedRef = nameToReference(itemName);
                            const normalizedGeneratedRef = normalizeRefForComparison(generatedRef);
                            const normalizedItemName = normalizeRefForComparison('@' + itemName.replace(/[^A-Za-z0-9]/g, ''));

                            if (normalizedGeneratedRef === normalizedRef || normalizedItemName === normalizedRef) {
                                actualEntityName = itemName;
                                entityType = 'item';
                                break;
                            }
                        }
                    }

                    // Check locations if not found
                    if (!actualEntityName) {
                        console.log(`[Imagen4Ultra] Looking for location match for "${ref}"`);
                        for (const locName of allEntityNamesFromStory.locations) {
                            const generatedRef = nameToReference(locName);
                            const normalizedGeneratedRef = normalizeRefForComparison(generatedRef);
                            const normalizedLocName = normalizeRefForComparison('@' + locName.replace(/[^A-Za-z0-9]/g, ''));

                            console.log(`[Imagen4Ultra] Testing "${locName}" -> generatedRef: "${generatedRef}", normalized: "${normalizedGeneratedRef}" vs "${normalizedRef}"`);

                            if (normalizedGeneratedRef === normalizedRef || normalizedLocName === normalizedRef) {
                                actualEntityName = locName;
                                entityType = 'location';
                                console.log(`[Imagen4Ultra] ✅ MATCH FOUND! "${ref}" -> "${locName}"`);
                                break;
                            }
                        }
                    }

                    if (actualEntityName && entityType) {
                        const promptsSection = entityType === 'character' ? storyResult.data.detailsPrompts?.characterPrompts || ''
                            : entityType === 'item' ? storyResult.data.detailsPrompts?.itemPrompts || ''
                                : storyResult.data.detailsPrompts?.locationPrompts || '';

                        const escapedEntityName = actualEntityName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                        const entityPattern = new RegExp(`^\\s*${escapedEntityName}\\s*\\n(.*?)(?=\\n\\n|$)`, "ms");
                        const entityMatch = promptsSection.match(entityPattern);

                        if (entityMatch && entityMatch[1]) {
                            descriptionText = entityMatch[1].trim();
                            entities.push({
                                name: ref.substring(1), // Use the reference name without @ for exact matching in buildOptimizedImagenPrompt
                                description: descriptionText,
                                type: entityType
                            });
                        } else {
                            console.warn(`[Imagen4Ultra] No description found for ${entityType} "${actualEntityName}" (ref: ${ref}). Name will remain in scene instruction.`);
                            entities.push({
                                name: ref.substring(1), // Use the reference name without @ for exact matching in buildOptimizedImagenPrompt
                                description: '(No detailed description provided for direct API use)',
                                type: entityType
                            });
                        }
                    } else {
                        console.warn(`[Imagen4Ultra] ❌ Entity for reference "${ref}" not found in story data. Ref will remain in scene instruction.`);
                        console.warn(`[Imagen4Ultra] Checked ${allEntityNamesFromStory.characters.length} characters, ${allEntityNamesFromStory.items.length} items, ${allEntityNamesFromStory.locations.length} locations`);
                    }
                }
            } else {
                console.warn("[Imagen4Ultra] Failed to get story data for placeholder expansion.");
            }
        } catch (error) { console.warn("[generateImageFromImagen4Ultra] Error processing placeholders:", error); }
    }

    // Get style string
    let styleStringApplicable: string | undefined;
    if (styleId) {
        try {
            const { getStylePromptForProvider } = await import('@/utils/imageStyleUtils');
            styleStringApplicable = getStylePromptForProvider(styleId as string, 'imagen4ultra');
        } catch (error) { console.warn("[generateImageFromImagen4Ultra] Failed to apply style from styleId:", error); }
    } else if (userId && storyId) {
        try {
            const { getStory } = await import('@/actions/baserowStoryActions');
            const storyResult = await getStory(storyId, userId);
            if (storyResult.success && storyResult.data?.imageStyleId) {
                const { getStylePromptForProvider } = await import('@/utils/imageStyleUtils');
                styleStringApplicable = getStylePromptForProvider(storyResult.data.imageStyleId as string, 'imagen4ultra');
            }
        } catch (error) { console.warn("[generateImageFromImagen4Ultra] Failed to apply style from story:", error); }
    }

    // Extract technical modifiers and build optimized prompt for Imagen 4 Ultra
    const technicalModifiers = extractTechnicalModifiers(originalPrompt, {
        quality: '8K, ultra high definition, professional photography, masterpiece quality',
        cameraAngle: 'wide shot'
    });

    const promptResult = buildOptimizedImagenPrompt({
        entities,
        actionPrompt: originalPrompt,
        styleString: styleStringApplicable,
        technicalModifiers,
        negativePrompt: getStandardNegativePrompt('child-friendly')
    });

    // Validate the prompt
    const validation = validatePrompt(promptResult.prompt);
    if (!validation.isValid) {
        console.warn('[Imagen4Ultra] Prompt validation warnings:', validation.suggestions);
    }

    const requestPromptWithStyle = promptResult.prompt;
    const expandedPromptForExport = requestPromptWithStyle;

    // Higher resolution dimensions for Imagen 4 Ultra
    const width = 1408;
    const height = 768;

    try {
        console.log(`Calling Imagen 4 Ultra API with structured prompt: \n"${requestPromptWithStyle}"\nUsing user's key.`);

        // Use the Imagen 4 Ultra endpoint
        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/imagen-4.0-ultra-generate-preview-06-06:predict?key=${apiKey}`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                instances: [{ prompt: requestPromptWithStyle }],
                parameters: {
                    sampleCount: 1, // Imagen 4 Ultra only generates 1 image at a time
                    aspectRatio: "16:9",
                    personGeneration: "ALLOW_ALL",
                    safetySettings: [
                        { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
                        { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' },
                        { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' },
                        { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' },
                    ],
                }
            }),
        });

        let result;
        try {
            result = await response.json();
        } catch (parseError) {
            console.error("Failed to parse Imagen 4 Ultra API response as JSON:", parseError);
            const text = await response.text();
            console.error("Raw response:", text);
            return { success: false, error: `Invalid JSON response from Imagen 4 Ultra API. Status: ${response.status}. The Imagen 4 Ultra model may not be available yet through the REST API.`, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport };
        }

        if (!response.ok) {
            console.error("Imagen 4 Ultra API Error Response:", JSON.stringify(result, null, 2));
            return { success: false, error: `Imagen 4 Ultra API request failed: ${response.status} - ${result?.error?.message || 'Unknown error'}`, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport };
        }

        const predictions = result.predictions;
        if (!predictions || predictions.length === 0) {
            console.error("Imagen 4 Ultra API returned no predictions. Full response:", JSON.stringify(result, null, 2));
            return { success: false, error: "No image data returned from Imagen 4 Ultra API", requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport };
        }
        const imageData = predictions[0]?.bytesBase64Encoded;
        if (!imageData) {
            console.error("Imagen 4 Ultra API returned prediction but no image bytes. Full response:", JSON.stringify(result, null, 2));
            return { success: false, error: "No image bytes in Imagen 4 Ultra response", requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport };
        }

        if (userId && storyId) {
            try {
                const safePrompt = originalPrompt.substring(0, 30).replace(/[^a-z0-9]/gi, '_').toLowerCase();
                const imageName = `imagen4ultra_${Date.now()}_${safePrompt}`;
                const imageBuffer = Buffer.from(imageData, 'base64');
                const firebaseUrl = await uploadImageBufferToMinIOStorage(imageBuffer, userId, storyId, imageName, 'image/png');
                return { success: true, imageUrl: firebaseUrl, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport, width, height };
            } catch (uploadError) {
                console.error("Error uploading image to MinIO Storage:", uploadError);
                return { success: true, imageUrl: `data:image/png;base64,${imageData}`, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport, width, height };
            }
        }
        return { success: true, imageUrl: `data:image/png;base64,${imageData}`, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport, width, height };
    } catch (error: unknown) {
        console.error("Error calling Imagen 4 Ultra API:", error);
        const message = error instanceof Error ? error.message : "An unknown error occurred while generating the image.";
        return { success: false, error: message, requestPrompt: requestPromptWithStyle, expandedPrompt: expandedPromptForExport };
    }
}

export async function generateImageFromPrompt(
    originalPrompt: string,
    userId: string,
    storyId?: string,
    provider: 'picsart' | 'gemini' | 'imagen3' | 'imagen4' | 'imagen4ultra' = 'picsart',
    styleId?: string
): Promise<{ success: boolean; imageUrl?: string; error?: string; requestPrompt?: string; expandedPrompt?: string; width?: number; height?: number }> {
    if (provider === 'gemini') {
        return generateImageFromGemini(originalPrompt, userId, storyId);
    }

    if (provider === 'imagen3') {
        // For Imagen3, originalPrompt is the scene instruction with @placeholders
        // It will be structured internally by generateImageFromImagen3
        return generateImageFromImagen3(originalPrompt, userId, storyId, styleId);
    }

    if (provider === 'imagen4') {
        // For Imagen4, originalPrompt is the scene instruction with @placeholders
        // It will be structured internally by generateImageFromImagen4
        return generateImageFromImagen4(originalPrompt, userId, storyId, styleId);
    }

    if (provider === 'imagen4ultra') {
        // For Imagen4Ultra, originalPrompt is the scene instruction with @placeholders
        // It will be structured internally by generateImageFromImagen4Ultra
        return generateImageFromImagen4Ultra(originalPrompt, userId, storyId, styleId);
    }

    // Picsart provider logic
    const userKeysResult = await getUserApiKeys(userId);
    if (!userKeysResult.success || !userKeysResult.data?.picsartApiKey) {
        return { success: false, error: "Picsart API key not configured by user. Please set it in Account Settings." };
    }
    const picsartApiKey = userKeysResult.data.picsartApiKey;

    let processedPrompt = originalPrompt; // This is the prompt with @placeholders
    if (userId && storyId) {
        try {
            const { getStory } = await import('@/actions/baserowStoryActions');
            const storyResult = await getStory(storyId, userId);
            if (storyResult.success && storyResult.data) {
                const { parseEntityReferences } = await import('@/app/(app)/assemble-video/utils');
                // For Picsart (Flux), we expand the @references directly into the prompt.
                processedPrompt = parseEntityReferences(originalPrompt, storyResult.data);
            }
        } catch (error) { console.warn("Failed to replace placeholders for Picsart, using original prompt:", error); }
    }

    let finalPrompt = processedPrompt || "high quality image";
    if (styleId) {
        try {
            const { applyStyleToPrompt } = await import('@/utils/imageStyleUtils');
            finalPrompt = applyStyleToPrompt(finalPrompt, styleId as string, provider);
        } catch (error) { console.warn("Failed to apply style for Picsart:", error); }
    } else if (userId && storyId) {
        try {
            const { getStory } = await import('@/actions/baserowStoryActions');
            const storyResult = await getStory(storyId, userId);
            if (storyResult.success && storyResult.data?.imageStyleId) {
                const { applyStyleToPrompt } = await import('@/utils/imageStyleUtils');
                finalPrompt = applyStyleToPrompt(finalPrompt, storyResult.data.imageStyleId as string, provider);
            }
        } catch (error) { console.warn("Failed to apply style from story for Picsart:", error); }
    }

    const requestPrompt = finalPrompt;
    const expandedPromptForExport = finalPrompt; // Store the full prompt with entity references + style that's sent to API
    const negativePrompt = "ugly, tiling, poorly drawn hands, poorly drawn feet, poorly drawn face, out of frame, extra limbs, disfigured, deformed, body out of frame, blurry, bad anatomy, blurred, watermark, grainy, signature, cut off, draft, low quality, worst quality, SFW, text, words, letters, nsfw, nude";
    const width = 1024; const height = 576; const count = 1;

    try {
        console.log(`Calling Picsart API with prompt: "${requestPrompt}" using user's key.`);
        const response = await fetch("https://genai-api.picsart.io/v1/text2image", {
            method: "POST",
            headers: { "Content-Type": "application/json", "x-picsart-api-key": picsartApiKey },
            body: JSON.stringify({ prompt: requestPrompt, negativePrompt, width, height, count }),
        });

        const responseText = await response.text();
        if (!response.ok) {
            console.error("PicsArt API Error Response Text:", responseText);
            let errorData: { message?: string; title?: string; };
            try { errorData = JSON.parse(responseText); } catch { errorData = { message: `PicsArt API request failed with status ${response.status}. Response: ${responseText}` }; }
            return { success: false, error: errorData.message || errorData.title || `PicsArt API request failed: ${response.status}`, requestPrompt, expandedPrompt: expandedPromptForExport };
        }

        const result = JSON.parse(responseText);
        if (response.status === 202 && result.status === 'ACCEPTED' && result.inference_id) {
            const pollResult = await pollForPicsArtImage(result.inference_id, picsartApiKey, requestPrompt, expandedPromptForExport);
            if (pollResult.success && pollResult.imageUrl && userId && storyId) {
                try {
                    const safePrompt = originalPrompt.substring(0, 30).replace(/[^a-z0-9]/gi, '_').toLowerCase();
                    const imageName = `${Date.now()}_${safePrompt}`;
                    const firebaseUrl = await uploadImageToMinIOStorage(pollResult.imageUrl, userId, storyId, imageName);
                    return { success: true, imageUrl: firebaseUrl, requestPrompt: pollResult.requestPrompt, expandedPrompt: pollResult.expandedPrompt, width, height };
                } catch (uploadError) {
                    console.error("Error uploading image to MinIO Storage:", uploadError);
                    return { success: false, error: `Image generation succeeded but failed to save to storage: ${uploadError}`, requestPrompt: pollResult.requestPrompt };
                }
            }
            return pollResult;
        } else if (response.ok && result.data && Array.isArray(result.data) && result.data.length > 0 && result.data[0].url) {
            if (userId && storyId) {
                try {
                    const safePrompt = originalPrompt.substring(0, 30).replace(/[^a-z0-9]/gi, '_').toLowerCase();
                    const imageName = `${Date.now()}_${safePrompt}`;
                    const firebaseUrl = await uploadImageToMinIOStorage(result.data[0].url, userId, storyId, imageName);
                    return { success: true, imageUrl: firebaseUrl, requestPrompt, expandedPrompt: expandedPromptForExport, width, height };
                } catch (uploadError) {
                    console.error("Error uploading image to MinIO Storage:", uploadError);
                    return { success: false, error: `Image generation succeeded but failed to save to storage: ${uploadError}`, requestPrompt, expandedPrompt: expandedPromptForExport };
                }
            }
            return { success: false, error: "Storage upload required but userId or storyId missing", requestPrompt, expandedPrompt: expandedPromptForExport };
        } else {
            const errorDetail = `Status: ${response.status}, Body: ${JSON.stringify(result)}`;
            return { success: false, error: `Unexpected response format from PicsArt API after POST. Details: ${errorDetail}`, requestPrompt, expandedPrompt: expandedPromptForExport };
        }
    } catch (error: unknown) {
        console.error("Error calling PicsArt API:", error);
        const message = error instanceof Error ? error.message : "An unknown error occurred while generating the image.";
        return { success: false, error: message, requestPrompt, expandedPrompt: expandedPromptForExport };
    }
}

async function pollForPicsArtImage(
    inferenceId: string,
    apiKey: string,
    requestPrompt: string,
    expandedPrompt: string,
    maxAttempts = 20,
    delayMs = 6000
): Promise<{ success: boolean; imageUrl?: string; error?: string; requestPrompt?: string; expandedPrompt?: string }> {
    const pollingUrl = `https://genai-api.picsart.io/v1/text2image/inferences/${inferenceId}`;
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            const response = await fetch(pollingUrl, { method: "GET", headers: { "x-picsart-api-key": apiKey } });
            const responseText = await response.text();
            let result;
            try {
                result = JSON.parse(responseText);
            } catch {
                if (response.status === 202 && attempt < maxAttempts) { await new Promise(resolve => setTimeout(resolve, delayMs)); continue; }
                return { success: false, error: `PicsArt Polling: Failed to parse JSON. Status: ${response.status}, Body: ${responseText}`, requestPrompt, expandedPrompt };
            }
            if (response.status === 200) {
                let imageUrl: string | undefined;
                if (result.data && result.data.url) { imageUrl = result.data.url; }
                else if (result.data && Array.isArray(result.data) && result.data.length > 0 && result.data[0].url) { imageUrl = result.data[0].url; }
                else if (result.url) { imageUrl = result.url; }
                if (imageUrl) { return { success: true, imageUrl, requestPrompt, expandedPrompt }; }
                else { return { success: false, error: "PicsArt Polling: Image success (200 OK) but no URL found.", requestPrompt, expandedPrompt }; }
            } else if (response.status === 202) {
                if (attempt < maxAttempts) { await new Promise(resolve => setTimeout(resolve, delayMs)); }
            } else {
                return { success: false, error: `PicsArt Polling: Request failed with status ${response.status}. Details: ${JSON.stringify(result)}`, requestPrompt, expandedPrompt };
            }
        } catch (error: unknown) {
            if (attempt >= maxAttempts) {
                const message = error instanceof Error ? error.message : "PicsArt Polling: Error after multiple attempts";
                return { success: false, error: message, requestPrompt, expandedPrompt };
            }
            await new Promise(resolve => setTimeout(resolve, delayMs));
        }
    }
    return { success: false, error: "Image generation timed out after polling.", requestPrompt, expandedPrompt };
}

/**
 * Generate video from image using Google Veo 2
 */
export async function generateVideoFromImage(
    imageUrl: string,
    actionPrompt: string,
    userId: string,
    storyId?: string
): Promise<{ success: boolean; videoUrl?: string; error?: string; requestPrompt?: string; width?: number; height?: number }> {
    const userKeysResult = await getUserApiKeys(userId);
    if (!userKeysResult.success || !userKeysResult.data?.googleApiKey) {
        return { success: false, error: "Google API key not configured by user. Please set it in Account Settings." };
    }
    const apiKey = userKeysResult.data.googleApiKey;

    // Transform the action prompt to be video-friendly
    const videoPrompt = transformActionPromptForVideo(actionPrompt);
    console.log(`Transformed prompt for video: "${actionPrompt}" -> "${videoPrompt}"`);

    try {
        console.log(`Calling Google Veo 2 API with prompt: "${videoPrompt}" for image: ${imageUrl}`);

        // First, we need to download the image and convert it to base64
        let imageBase64: string;
        try {
            const imageResponse = await fetch(imageUrl);
            if (!imageResponse.ok) {
                throw new Error(`Failed to fetch image: ${imageResponse.status}`);
            }
            const imageBuffer = await imageResponse.arrayBuffer();
            imageBase64 = Buffer.from(imageBuffer).toString("base64");
        } catch (error) {
            console.error("Error fetching image for video generation:", error);
            return { success: false, error: "Failed to fetch image for video generation" };
        }

        console.log("Using base64 image data for Google Veo 2 API");

        // Use the correct Vertex AI endpoint for Veo 2 video generation
        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/veo-2.0-generate-001:predictLongRunning?key=${apiKey}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                instances: [{
                    prompt: videoPrompt,
                    image: {
                        bytesBase64Encoded: imageBase64,
                        mimeType: 'image/jpeg'
                    }
                }],
                parameters: {
                    aspectRatio: "16:9",
                    personGeneration: "allow_adult",
                    sampleCount: 1,
                    durationSeconds: 5,
                    enhancePrompt: true
                }
            }),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("Google Veo 2 API Error Response:", errorText);
            return { success: false, error: `Veo 2 API request failed: ${response.status}`, requestPrompt: videoPrompt };
        }

        const result = await response.json();
        console.log("Veo 2 API Response:", JSON.stringify(result, null, 2));

        // The predictLongRunning endpoint returns an operation object
        const operation = result;
        if (!operation.name) {
            console.error("No operation name in Veo 2 response:", result);
            return { success: false, error: "Invalid response from Veo 2 API", requestPrompt: videoPrompt };
        }

        console.log(`Starting video generation operation: ${operation.name}`);

        // Poll the operation status until it's done
        let pollOperation = operation;
        const maxPollAttempts = 60; // Maximum 10 minutes (10s * 60)
        let pollAttempts = 0;

        while (!pollOperation.done && pollAttempts < maxPollAttempts) {
            pollAttempts++;
            console.log(`Polling attempt ${pollAttempts}/${maxPollAttempts} for operation ${operation.name}`);

            // Wait 10 seconds before checking again
            await new Promise(resolve => setTimeout(resolve, 10000));

            // Check operation status
            const pollResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/${operation.name}?key=${apiKey}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                }
            });

            if (!pollResponse.ok) {
                const pollError = await pollResponse.text();
                console.error("Error polling operation status:", pollError);
                return { success: false, error: `Failed to poll operation status: ${pollResponse.status}`, requestPrompt: videoPrompt };
            }

            pollOperation = await pollResponse.json();
            console.log(`Operation status poll ${pollAttempts}: done=${pollOperation.done}`);
        }

        if (!pollOperation.done) {
            return { success: false, error: "Video generation timed out. Please try again later.", requestPrompt: videoPrompt };
        }

        // Check for errors in the completed operation
        if (pollOperation.error) {
            console.error("Video generation operation failed:", pollOperation.error);
            return { success: false, error: `Video generation failed: ${pollOperation.error.message || 'Unknown error'}`, requestPrompt: videoPrompt };
        }

        // Extract video data from the completed operation
        const generatedVideos = pollOperation.response?.generated_videos ||
            pollOperation.response?.generatedVideos ||
            pollOperation.response?.generateVideoResponse?.generatedSamples;

        console.log("Full operation response structure:", JSON.stringify(pollOperation.response, null, 2));
        console.log("Generated videos found:", generatedVideos);

        if (!generatedVideos || generatedVideos.length === 0) {
            console.error("No generated videos in completed operation:", pollOperation);
            return { success: false, error: "No videos were generated", requestPrompt: videoPrompt };
        }

        const firstVideo = generatedVideos[0];
        console.log("First video structure:", JSON.stringify(firstVideo, null, 2));
        let videoData: string | null = null;

        // Try to get video data from different possible locations
        if (firstVideo.video?.uri) {
            console.log("Found video URI:", firstVideo.video.uri);
            // Download video from URI
            try {
                // Try different authentication methods
                let videoResponse;

                // Method 1: Try with Authorization header
                console.log("Trying download with Authorization header...");
                videoResponse = await fetch(firstVideo.video.uri, {
                    headers: {
                        'Authorization': `Bearer ${apiKey}`
                    }
                });
                console.log("Video download response status (Bearer token):", videoResponse.status, videoResponse.statusText);

                // Method 2: Try with API key parameter
                if (!videoResponse.ok) {
                    console.log("Retrying download with API key parameter...");
                    videoResponse = await fetch(`${firstVideo.video.uri}?key=${apiKey}`);
                    console.log("Video download response status (with key):", videoResponse.status, videoResponse.statusText);
                }

                // Method 3: Try without authentication (URI might be pre-authenticated)
                if (!videoResponse.ok) {
                    console.log("Retrying download without API key...");
                    videoResponse = await fetch(firstVideo.video.uri);
                    console.log("Video download response status (without key):", videoResponse.status, videoResponse.statusText);
                }

                if (videoResponse.ok) {
                    const videoBuffer = await videoResponse.arrayBuffer();
                    videoData = Buffer.from(videoBuffer).toString('base64');
                    console.log("Successfully downloaded video from URI, size:", videoData.length);
                } else {
                    console.error("Video download failed:", videoResponse.status, videoResponse.statusText);
                    const errorText = await videoResponse.text();
                    console.error("Error response body:", errorText);

                    // Fallback: Use the video URI directly instead of downloading
                    console.log("Using video URI directly as fallback...");

                    // Return success with the URI - the frontend can try to play it directly
                    return {
                        success: true,
                        videoUrl: firstVideo.video.uri,
                        requestPrompt: videoPrompt,
                        width: 1280,
                        height: 720
                    };
                }
            } catch (error) {
                console.error("Error downloading video from URI:", error);
            }
        } else if (firstVideo.video?.videoBytes) {
            console.log("Found video.videoBytes");
            // Use video bytes directly
            videoData = firstVideo.video.videoBytes;
        } else if (firstVideo.videoBytes) {
            console.log("Found videoBytes");
            // Alternative location for video bytes
            videoData = firstVideo.videoBytes;
        } else if (firstVideo.bytesBase64Encoded) {
            console.log("Found bytesBase64Encoded");
            // Check for base64 encoded bytes (common in Google APIs)
            videoData = firstVideo.bytesBase64Encoded;
        } else if (firstVideo.videoContent) {
            console.log("Found videoContent");
            // Another possible location
            videoData = firstVideo.videoContent;
        } else {
            console.log("Checking all possible video data locations...");
            console.log("Available keys in firstVideo:", Object.keys(firstVideo));
            // Try to find any base64 data in the object
            for (const [key, value] of Object.entries(firstVideo)) {
                if (typeof value === 'string' && value.length > 1000) {
                    console.log(`Found potential video data at key '${key}', length: ${value.length}`);
                    videoData = value;
                    break;
                }
            }
        }

        if (!videoData) {
            console.error("Could not extract video data from operation response:", pollOperation);
            return { success: false, error: "Could not extract video data from generation result", requestPrompt: videoPrompt };
        }

        console.log("Successfully extracted video data, length:", videoData.length);

        // Upload video to MinIO if user and story are provided
        if (userId && storyId) {
            try {
                const safePrompt = actionPrompt.substring(0, 30).replace(/[^a-z0-9]/gi, "_").toLowerCase();
                const videoName = `veo2_${Date.now()}_${safePrompt}.mp4`;
                const videoBuffer = Buffer.from(videoData, "base64");
                const minioUrl = await uploadVideoBufferToMinIOStorage(videoBuffer, userId, storyId, videoName, "video/mp4");

                return {
                    success: true,
                    videoUrl: minioUrl,
                    requestPrompt: videoPrompt,
                    width: 1280, // Veo 2 typical output dimensions
                    height: 720
                };
            } catch (uploadError) {
                console.error("Error uploading video to MinIO Storage:", uploadError);
                // Return base64 video as fallback
                return {
                    success: true,
                    videoUrl: `data:video/mp4;base64,${videoData}`,
                    requestPrompt: videoPrompt,
                    width: 1280,
                    height: 720
                };
            }
        }

        return {
            success: true,
            videoUrl: `data:video/mp4;base64,${videoData}`,
            requestPrompt: videoPrompt,
            width: 1280,
            height: 720
        };

    } catch (error: unknown) {
        console.error("Error calling Google Veo 2 API:", error);
        const message = error instanceof Error ? error.message : "An unknown error occurred while generating the video.";
        return { success: false, error: message, requestPrompt: videoPrompt };
    }
}
