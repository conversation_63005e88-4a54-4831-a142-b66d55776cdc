'use server';

import { generateImageFromImagen4Ultra } from './imageActions';

interface ThumbnailImageGenerationParams {
  thumbnailPrompt: string;
  userId: string;
  storyId?: string;
}

interface ThumbnailImageGenerationResult {
  thumbnailImageUrl: string;
  width?: number;
  height?: number;
}

export async function generateThumbnailImage({
  thumbnailPrompt,
  userId,
  storyId
}: ThumbnailImageGenerationParams): Promise<{ success: boolean, data?: ThumbnailImageGenerationResult, error?: string }> {
  try {
    console.log('Generating YouTube thumbnail image with Imagen 4 Ultra...');
    
    // Use the existing Imagen 4 Ultra function with the optimized thumbnail prompt
    const result = await generateImageFromImagen4Ultra(
      thumbnailPrompt, // This should already be optimized for thumbnails
      userId,
      storyId,
      undefined // No specific style ID since the prompt should include style instructions
    );
    
    if (result.success && result.imageUrl) {
      return {
        success: true,
        data: {
          thumbnailImageUrl: result.imageUrl,
          width: result.width,
          height: result.height
        }
      };
    } else {
      return {
        success: false,
        error: result.error || 'Failed to generate thumbnail image with Imagen 4 Ultra'
      };
    }
    
  } catch (error) {
    console.error('Error generating thumbnail image:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate thumbnail image'
    };
  }
}
