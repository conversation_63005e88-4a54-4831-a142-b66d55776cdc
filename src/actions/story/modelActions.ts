"use server";

import { getUserApiKeys } from '../baserowApiKeyActions';

export async function listGoogleScriptModels(userId: string): Promise<{ success: boolean; models?: Array<{ id: string; name: string }>; error?: string }> {
    try {
        const userKeysResult = await getUserApiKeys(userId);
        if (!userKeysResult.success || !userKeysResult.data?.googleApiKey) {
            return { success: false, error: "Google API key not configured. Please set it in Account Settings." };
        }

        // Standard Google AI models for script generation
        const models = [
            { id: 'gemini-2.0-flash', name: 'Gemini 2.0 Flash (Recommended)' },
            { id: 'gemini-2.5-pro', name: 'Gemini 2.5 Pro' },
            { id: 'gemini-2.5-pro-exp-03-25', name: 'Gemini 2.5 Pro Experimental (03-25)' },
            { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' },
            { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' },
            { id: 'gemini-1.0-pro', name: 'Gemini 1.0 Pro' }
        ];

        return { success: true, models };
    } catch (error) {
        console.error("Error listing Google Script models:", error);
        return { success: false, error: "Failed to list Google models" };
    }
}

export async function listPerplexityModels(userId: string): Promise<{ success: boolean; models?: Array<{ id: string; name: string }>; error?: string }> {
    try {
        const userKeysResult = await getUserApiKeys(userId);
        if (!userKeysResult.success || !userKeysResult.data?.perplexityApiKey) {
            return { success: false, error: "Perplexity API key not configured. Please set it in Account Settings." };
        }

        // Available Perplexity models
        const models = [
            { id: 'sonar-reasoning-pro', name: 'Sonar Reasoning Pro (Recommended)' },
            { id: 'sonar-pro', name: 'Sonar Pro' },
            { id: 'sonar', name: 'Sonar' },
            { id: 'llama-3.1-sonar-huge-128k-online', name: 'Llama 3.1 Sonar Huge (128K)' },
            { id: 'llama-3.1-sonar-large-128k-online', name: 'Llama 3.1 Sonar Large (128K)' }
        ];

        return { success: true, models };
    } catch (error) {
        console.error("Error listing Perplexity models:", error);
        return { success: false, error: "Failed to list Perplexity models" };
    }
}
