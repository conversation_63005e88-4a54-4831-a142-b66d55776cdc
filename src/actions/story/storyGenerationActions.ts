"use server";

import { genkit } from 'genkit';
import { googleAI } from '@genkit-ai/googleai';
import { z } from 'zod';
import { runFlow } from '@genkit-ai/flow';

import {
    AITitleOutputSchema,
    type GenerateTitleInput,
    AIScriptOutputSchema,
    type GenerateScriptInput,
    AIScriptChunksOutputSchema,
    type GenerateScriptChunksInput,
} from '../storyActionSchemas';

import { getUserApiKeys } from '../baserowApiKeyActions';
import { 
    titlePromptTemplate, 
    scriptPromptTemplate, 
    scriptChunksPromptTemplate 
} from '../utils/promptTemplates';
import { 
    extractJsonFromPerplexityResponse, 
    extractValidJsonFromText,
    fixMissingPeriods,
    validateScriptChunks
} from '../utils/storyHelpers';

export async function generateTitle(input: GenerateTitleInput): Promise<{ success: boolean, data?: z.infer<typeof AITitleOutputSchema>, error?: string }> {
    const userKeysResult = await getUserApiKeys(input.userId);

    if (input.aiProvider === 'perplexity') {
        if (!userKeysResult.success || !userKeysResult.data?.perplexityApiKey) {
            return { success: false, error: "Perplexity API key not configured by user. Please set it in Account Settings." };
        }
        try {
            const { generateWithPerplexity } = await import('../../ai/genkit'); // Corrected path

            const promptText = titlePromptTemplate.replace('{{userPrompt}}', input.userPrompt);

            const messages = [
                { role: 'system' as const, content: 'You are an expert at creating catchy and concise titles for stories. Generate a short title (ideally 3-7 words, maximum 10 words).' },
                { role: 'user' as const, content: promptText }
            ];

            const titleText = await runFlow(generateWithPerplexity, {
                modelName: input.perplexityModel || 'sonar-reasoning-pro', // Default model
                messages: messages,
                userId: input.userId,
                contentType: 'title',
                useStructuredOutput: true,
            });

            return { success: true, data: { title: titleText } }; // Wrapped to match AITitleOutputSchema

        } catch (error) {
            console.error("Error in generateTitle with Perplexity AI call:", error);
            const errorMessage = error instanceof Error ? error.message : "Failed to generate title with Perplexity.";
            return { success: false, error: errorMessage };
        }
    } else { // Default to Google AI
        if (!userKeysResult.success || !userKeysResult.data?.googleApiKey) {
            return { success: false, error: "Google API key not configured by user. Please set it in Account Settings." };
        }
        const userGoogleKey = userKeysResult.data.googleApiKey;

        try {
            const modelName = input.googleScriptModel || 'gemini-2.0-flash'; // Default if not provided
            const localAi = genkit({ plugins: [googleAI({ apiKey: userGoogleKey })], model: `googleai/${modelName}` });
            const prompt = titlePromptTemplate.replace('{{userPrompt}}', input.userPrompt);
            const { output } = await localAi.generate({ prompt, output: { schema: AITitleOutputSchema, format: 'json' } });

            if (!output?.title) {
                const promptWords = input.userPrompt.split(' ').slice(0, 5).join(' ');
                return { success: true, data: { title: `${promptWords}... (Draft)` } };
            }
            return { success: true, data: output };
        } catch (error) {
            console.error("Error in generateTitle AI call (Google):", error);
            return { success: false, error: "Failed to generate title with user's Google key." };
        }
    }
}

export async function generateScript(input: GenerateScriptInput): Promise<{ success: boolean, data?: z.infer<typeof AIScriptOutputSchema>, error?: string }> {
    const userKeysResult = await getUserApiKeys(input.userId);

    if (input.aiProvider === 'perplexity') {
        if (!userKeysResult.success || !userKeysResult.data?.perplexityApiKey) {
            return { success: false, error: "Perplexity API key not configured by user. Please set it in Account Settings." };
        }
        try {
            const { generateWithPerplexity } = await import('../../ai/genkit'); // Corrected path

            const promptText = scriptPromptTemplate.replace('{{{prompt}}}', input.prompt);

            const messages = [
                { role: 'system' as const, content: 'You are a story writer. Write engaging stories for animated videos. Write only the story content itself - no production instructions, stage directions, or narrator cues. Write in pure narrative prose that flows naturally.' },
                { role: 'user' as const, content: promptText }
            ];

            const scriptText = await runFlow(generateWithPerplexity, {
                modelName: input.perplexityModel || 'sonar-reasoning-pro', // Default model
                messages: messages,
                userId: input.userId,
                contentType: 'script',
                useStructuredOutput: true,
            });

            return { success: true, data: { script: scriptText } }; // Wrapped to match AIScriptOutputSchema

        } catch (error) {
            console.error("Error in generateScript with Perplexity AI call:", error);
            const errorMessage = error instanceof Error ? error.message : "Failed to generate script with Perplexity.";
            return { success: false, error: errorMessage };
        }
    } else { // Default to Google AI
        if (!userKeysResult.success || !userKeysResult.data?.googleApiKey) {
            return { success: false, error: "Google API key not configured by user. Please set it in Account Settings." };
        }
        const userGoogleKey = userKeysResult.data.googleApiKey;

        try {
            const modelName = input.googleScriptModel || 'gemini-2.0-flash'; // Default if not provided
            const localAi = genkit({ plugins: [googleAI({ apiKey: userGoogleKey })], model: `googleai/${modelName}` });
            const prompt = scriptPromptTemplate.replace('{{{prompt}}}', input.prompt);
            const { output } = await localAi.generate({ prompt, output: { schema: AIScriptOutputSchema, format: 'json' } });
            return { success: true, data: output! };
        } catch (error) {
            console.error("Error in generateScript AI call (Google):", error);
            return { success: false, error: "Failed to generate script with user's Google key." };
        }
    }
}

export async function generateScriptChunks(input: GenerateScriptChunksInput): Promise<{ success: boolean, data?: Omit<z.infer<typeof AIScriptChunksOutputSchema>, 'error'>, error?: string }> {
    const userKeysResult = await getUserApiKeys(input.userId);

    if (input.aiProvider === 'perplexity') {
        if (!userKeysResult.success || !userKeysResult.data?.perplexityApiKey) {
            return { success: false, error: "Perplexity API key not configured by user. Please set it in Account Settings." };
        }
        try {
            const { generateWithPerplexity } = await import('../../ai/genkit');

            const promptText = scriptChunksPromptTemplate.replace('{{{script}}}', input.script);

            const messages = [
                { role: 'system' as const, content: 'You are a movie director and script editor. Split story scripts into meaningful visual scenes for animation. Each chunk should contain complete sentences and represent a coherent visual scene.' },
                { role: 'user' as const, content: promptText }
            ];

            const resultText = await runFlow(generateWithPerplexity, {
                modelName: input.perplexityModel || 'sonar-reasoning-pro',
                messages: messages,
                userId: input.userId,
                useStructuredOutput: true,
            });

            // Extract JSON from Perplexity response
            const cleanedText = extractJsonFromPerplexityResponse(resultText);
            const jsonString = extractValidJsonFromText(cleanedText);

            if (!jsonString) {
                console.error("Could not extract valid JSON from Perplexity response:", cleanedText.substring(0, 200));
                return { success: false, error: "Failed to parse script chunks from Perplexity response." };
            }

            let parsedResult;
            try {
                parsedResult = JSON.parse(jsonString);
            } catch (parseError) {
                console.error("Failed to parse JSON:", parseError, "Raw JSON:", jsonString);
                return { success: false, error: "Invalid JSON format in script chunks response." };
            }

            if (!parsedResult.scriptChunks || !Array.isArray(parsedResult.scriptChunks)) {
                console.error("Invalid response structure:", parsedResult);
                return { success: false, error: "Response does not contain valid scriptChunks array." };
            }

            let finalChunks = parsedResult.scriptChunks;

            // Validate chunks
            const validation = validateScriptChunks(input.script, finalChunks);
            if (!validation.isValid) {
                console.warn("Script chunks validation failed:", validation.error);
                
                // Try to fix missing periods
                const fixedChunks = fixMissingPeriods(finalChunks, input.script);
                if (fixedChunks) {
                    const fixedValidation = validateScriptChunks(input.script, fixedChunks);
                    if (fixedValidation.isValid) {
                        finalChunks = fixedChunks;
                        console.log("Successfully fixed missing periods in chunks");
                    }
                }
            }

            return { success: true, data: { scriptChunks: finalChunks } };

        } catch (error) {
            console.error("Error in generateScriptChunks with Perplexity AI call:", error);
            const errorMessage = error instanceof Error ? error.message : "Failed to generate script chunks with Perplexity.";
            return { success: false, error: errorMessage };
        }
    } else { // Default to Google AI
        if (!userKeysResult.success || !userKeysResult.data?.googleApiKey) {
            return { success: false, error: "Google API key not configured by user. Please set it in Account Settings." };
        }
        const userGoogleKey = userKeysResult.data.googleApiKey;

        try {
            const modelName = input.googleScriptModel || 'gemini-2.0-flash';
            const localAi = genkit({ plugins: [googleAI({ apiKey: userGoogleKey })], model: `googleai/${modelName}` });
            const prompt = scriptChunksPromptTemplate.replace('{{{script}}}', input.script);
            const { output } = await localAi.generate({ prompt, output: { schema: AIScriptChunksOutputSchema, format: 'json' } });

            if (!output?.scriptChunks) {
                return { success: false, error: "No script chunks returned from AI model." };
            }

            let finalChunks = output.scriptChunks;

            // Validate chunks
            const validation = validateScriptChunks(input.script, finalChunks);
            if (!validation.isValid) {
                console.warn("Script chunks validation failed:", validation.error);
                
                // Try to fix missing periods
                const fixedChunks = fixMissingPeriods(finalChunks, input.script);
                if (fixedChunks) {
                    const fixedValidation = validateScriptChunks(input.script, fixedChunks);
                    if (fixedValidation.isValid) {
                        finalChunks = fixedChunks;
                        console.log("Successfully fixed missing periods in chunks");
                    }
                }
            }

            return { success: true, data: { scriptChunks: finalChunks } };
        } catch (error) {
            console.error("Error in generateScriptChunks AI call (Google):", error);
            return { success: false, error: "Failed to generate script chunks with user's Google key." };
        }
    }
}
