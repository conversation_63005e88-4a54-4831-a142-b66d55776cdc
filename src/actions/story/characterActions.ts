"use server";

import { genkit } from 'genkit';
import { googleAI } from '@genkit-ai/googleai';
import { z } from 'zod';
import { runFlow } from '@genkit-ai/flow';

import {
    AICharacterPromptsOutputSchema,
    type GenerateCharacterPromptsInput,
} from '../storyActionSchemas';

import { getUserApiKeys } from '../baserowApiKeyActions';
import { characterPromptsPromptTemplate } from '../utils/promptTemplates';
import { extractJsonFromPerplexityResponse } from '../utils/storyHelpers';

export async function generateCharacterPrompts(input: GenerateCharacterPromptsInput): Promise<{ success: boolean, data?: z.infer<typeof AICharacterPromptsOutputSchema>, error?: string }> {
    const userKeysResult = await getUserApiKeys(input.userId);
    if (!userKeysResult.success || !userKeysResult.data) {
        return { success: false, error: "Could not fetch user API keys." };
    }
    const userApiKeys = userKeysResult.data;

    let stylePromptText: string | undefined;
    if (input.imageStyleId) {
        try {
            const { getStylePromptForProvider } = await import('@/utils/imageStyleUtils');
            stylePromptText = getStylePromptForProvider(input.imageStyleId as string, input.imageProvider || 'picsart');
        } catch (error) {
            console.warn('Failed to get style prompt for character generation:', error);
        }
    }

    let finalPrompt = characterPromptsPromptTemplate.replace('{{{script}}}', input.script);
    if (stylePromptText) {
        finalPrompt = finalPrompt.replace('{{#if stylePrompt}}\n**ARTISTIC STYLE REQUIREMENTS:**\nIncorporate these style characteristics into all descriptions: {{{stylePrompt}}}\nEnsure that character, item, and location descriptions align with this artistic style while maintaining their unique features.\n{{/if}}', `**ARTISTIC STYLE REQUIREMENTS:**\nIncorporate these style characteristics into all descriptions: ${stylePromptText}\nEnsure that character, item, and location descriptions align with this artistic style while maintaining their unique features.`);
    } else {
        finalPrompt = finalPrompt.replace('{{#if stylePrompt}}\n**ARTISTIC STYLE REQUIREMENTS:**\nIncorporate these style characteristics into all descriptions: {{{stylePrompt}}}\nEnsure that character, item, and location descriptions align with this artistic style while maintaining their unique features.\n{{/if}}', '');
    }

    if (input.aiProvider === 'perplexity') {
        if (!userApiKeys.perplexityApiKey) {
            return { success: false, error: "Perplexity API key not configured by user. Please set it in Account Settings." };
        }
        try {
            const { generateWithPerplexity } = await import('../../ai/genkit');
            const messages = [
                { role: 'system' as const, content: 'You are an expert prompt engineer. Follow the instructions precisely to generate character, item, and location prompts based on the script.' },
                { role: 'user' as const, content: finalPrompt }
            ];
            const resultText = await runFlow(generateWithPerplexity, {
                modelName: input.perplexityModel || 'sonar-reasoning-pro', // Default Perplexity model
                messages: messages,
                userId: input.userId,
            });
            // First, try to extract JSON from reasoning tags if present
            const cleanedResultText = extractJsonFromPerplexityResponse(resultText);

            // Try to parse as JSON first
            try {
                const parsedJson = JSON.parse(cleanedResultText);
                if (parsedJson.characterPrompts || parsedJson.itemPrompts || parsedJson.locationPrompts) {
                    return { success: true, data: parsedJson };
                }
            } catch {
                // Not JSON, continue with text parsing
            }

            // Fallback to text-based parsing for headings format
            const characterPrompts = cleanedResultText.match(/Character Prompts:\s*([\s\S]*?)(Item Prompts:|Location Prompts:|$)/)?.[1]?.trim() || '';
            const itemPrompts = cleanedResultText.match(/Item Prompts:\s*([\s\S]*?)(Location Prompts:|$)/)?.[1]?.trim() || '';
            const locationPrompts = cleanedResultText.match(/Location Prompts:\s*([\s\S]*?$)/)?.[1]?.trim() || '';

            if (!characterPrompts && !itemPrompts && !locationPrompts) {
                console.warn("Could not parse Perplexity output for character prompts. Raw output:", cleanedResultText);
                return { success: false, error: "Failed to parse character prompts from Perplexity. Output might be partial or malformed. Raw: " + cleanedResultText.substring(0, 100) + "..." };
            }

            return { success: true, data: { characterPrompts, itemPrompts, locationPrompts } };

        } catch (error) {
            console.error("Error in generateCharacterPrompts with Perplexity AI call:", error);
            const errorMessage = error instanceof Error ? error.message : "Failed to generate character prompts with Perplexity.";
            return { success: false, error: errorMessage };
        }
    } else { // Default to Google AI
        if (!userApiKeys.googleApiKey) {
            return { success: false, error: "Google API key not configured by user. Please set it in Account Settings." };
        }

        const primaryModel = input.googleScriptModel || 'gemini-2.5-pro';
        const fallbackModels = ['gemini-2.5-pro', 'gemini-2.0-flash', 'gemini-1.5-flash', 'gemini-1.5-pro'];
        
        for (const modelName of fallbackModels) {
            try {
                console.log(`[generateCharacterPrompts] Trying model: ${modelName}`);
                const localAi = genkit({ plugins: [googleAI({ apiKey: userApiKeys.googleApiKey })], model: `googleai/${modelName}` });
                const { output } = await localAi.generate({ prompt: finalPrompt, output: { schema: AICharacterPromptsOutputSchema, format: 'json' } });
                console.log(`[generateCharacterPrompts] Success with model: ${modelName}`);
                return { success: true, data: output! };
            } catch (error: unknown) {
                console.error(`[generateCharacterPrompts] Error with model ${modelName}:`, error);
                
                // If this was our preferred model and it's a 5xx error, add a small delay
                if (modelName === primaryModel && typeof error === 'object' && error !== null && 'status' in error && (error.status as number) >= 500) {
                    console.log(`[generateCharacterPrompts] Adding delay due to ${modelName} server error...`);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
                // Continue to next model unless this is the last one
                if (modelName === fallbackModels[fallbackModels.length - 1]) {
                    return { success: false, error: "Failed to generate character/item/location prompts with Google. All fallback models failed." };
                }
            }
        }
        
        return { success: false, error: "Failed to generate character/item/location prompts with Google." };
    }
}
