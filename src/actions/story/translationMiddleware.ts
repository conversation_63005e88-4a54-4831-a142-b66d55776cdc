"use server";

import { 
    generateSpanishTranslation as originalGenerateSpanishTranslation,
    generateRomanianTranslation as originalGenerateRomanianTranslation
} from './translationActions';
import { 
    type GenerateSpanishTranslationInput,
    type GenerateRomanianTranslationInput,
    AISpanishTranslationOutputSchema,
    AIRomanianTranslationOutputSchema
} from '../storyActionSchemas';
import { z } from 'zod';

// Auto-saving wrapper for Spanish translation
export async function generateSpanishTranslationWithAutoSave(
    input: GenerateSpanishTranslationInput,
    storyId?: string
): Promise<{ success: boolean, data?: z.infer<typeof AISpanishTranslationOutputSchema>, error?: string }> {
    console.log(`[generateSpanishTranslationWithAutoSave] Starting translation with auto-save for story ${storyId || 'unknown'}`);
    
    // Call the original translation function
    const result = await originalGenerateSpanishTranslation(input);
    
    // If translation succeeded and we have a story ID, save to Baserow
    if (result.success && result.data?.spanishChunks && storyId) {
        try {
            console.log(`[generateSpanishTranslationWithAutoSave] Translation successful, saving ${result.data.spanishChunks.length} chunks to Baserow`);
            
            // Import here to avoid circular dependencies
            const { getStory, saveStory } = await import('../baserowStoryActions');
            
            // Get the current story
            const storyResult = await getStory(storyId, input.userId);
            
            if (storyResult.success && storyResult.data) {
                // Update the story with Spanish translation
                const updatedStory = {
                    ...storyResult.data,
                    spanishNarrationChunks: result.data.spanishChunks
                };
                
                // Save the updated story
                const saveResult = await saveStory(updatedStory, input.userId);
                
                if (saveResult.success) {
                    console.log(`[generateSpanishTranslationWithAutoSave] Successfully saved Spanish translation to Baserow for story ${storyId}`);
                } else {
                    console.error(`[generateSpanishTranslationWithAutoSave] Failed to save to Baserow:`, saveResult.error);
                    // Don't fail the translation if save fails, just log the error
                }
            } else {
                console.error(`[generateSpanishTranslationWithAutoSave] Failed to get story for saving:`, storyResult.error);
            }
        } catch (saveError) {
            console.error(`[generateSpanishTranslationWithAutoSave] Error during auto-save:`, saveError);
            // Don't fail the translation if save fails, just log the error
        }
    } else if (result.success && !storyId) {
        console.warn(`[generateSpanishTranslationWithAutoSave] Translation successful but no storyId provided for auto-save`);
    }
    
    return result;
}

// Auto-saving wrapper for Romanian translation
export async function generateRomanianTranslationWithAutoSave(
    input: GenerateRomanianTranslationInput,
    storyId?: string
): Promise<{ success: boolean, data?: z.infer<typeof AIRomanianTranslationOutputSchema>, error?: string }> {
    console.log(`[generateRomanianTranslationWithAutoSave] Starting translation with auto-save for story ${storyId || 'unknown'}`);
    
    // Call the original translation function
    const result = await originalGenerateRomanianTranslation(input);
    
    // If translation succeeded and we have a story ID, save to Baserow
    if (result.success && result.data?.romanianChunks && storyId) {
        try {
            console.log(`[generateRomanianTranslationWithAutoSave] Translation successful, saving ${result.data.romanianChunks.length} chunks to Baserow`);
            
            // Import here to avoid circular dependencies
            const { getStory, saveStory } = await import('../baserowStoryActions');
            
            // Get the current story
            const storyResult = await getStory(storyId, input.userId);
            
            if (storyResult.success && storyResult.data) {
                // Update the story with Romanian translation
                const updatedStory = {
                    ...storyResult.data,
                    romanianNarrationChunks: result.data.romanianChunks
                };
                
                // Save the updated story
                const saveResult = await saveStory(updatedStory, input.userId);
                
                if (saveResult.success) {
                    console.log(`[generateRomanianTranslationWithAutoSave] Successfully saved Romanian translation to Baserow for story ${storyId}`);
                } else {
                    console.error(`[generateRomanianTranslationWithAutoSave] Failed to save to Baserow:`, saveResult.error);
                    // Don't fail the translation if save fails, just log the error
                }
            } else {
                console.error(`[generateRomanianTranslationWithAutoSave] Failed to get story for saving:`, storyResult.error);
            }
        } catch (saveError) {
            console.error(`[generateRomanianTranslationWithAutoSave] Error during auto-save:`, saveError);
            // Don't fail the translation if save fails, just log the error
        }
    } else if (result.success && !storyId) {
        console.warn(`[generateRomanianTranslationWithAutoSave] Translation successful but no storyId provided for auto-save`);
    }
    
    return result;
}