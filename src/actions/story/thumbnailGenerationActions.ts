'use server';

import { genkit } from 'genkit';
import { googleAI } from '@genkit-ai/googleai';
import { runFlow } from '@genkit-ai/flow';
import { getUserApiKeys } from '../baserowApiKeyActions';

interface ThumbnailGenerationParams {
  script: string;
  characterPrompts?: string;
  itemPrompts?: string;
  locationPrompts?: string;
  userId: string;
  storyId?: string;
  aiProvider: 'google' | 'perplexity';
  perplexityModel?: string;
  googleScriptModel?: string;
}

interface ThumbnailGenerationResult {
  thumbnailPrompt: string;
}

// Extract main character details from character prompts
const extractMainCharacter = (characterPrompts: string) => {
  if (!characterPrompts) return null;
  
  // Split by character entries and get the first one (main character)
  const characterSections = characterPrompts.split(/(?:Character:|character:)/i).filter(section => section.trim());
  
  if (characterSections.length === 0) return null;
  
  // Get the first character (usually the main character)
  const mainCharacterSection = characterSections[0].trim();
  
  // Extract character name
  const nameMatch = mainCharacterSection.match(/^([A-Za-z\s]+?)[-–:]/);
  const name = nameMatch ? nameMatch[1].trim() : 'main character';
  
  // Clean up the description by removing any leading name/colon
  const description = mainCharacterSection.replace(/^[^-–:]*[-–:]/, '').trim();
  
  return {
    name,
    description,
    fullText: mainCharacterSection
  };
};

export async function generateThumbnailPrompt({
  script,
  characterPrompts,
  itemPrompts,
  locationPrompts,
  userId,
  storyId,
  aiProvider,
  perplexityModel,
  googleScriptModel
}: ThumbnailGenerationParams): Promise<{ success: boolean, data?: ThumbnailGenerationResult, error?: string }> {
  try {
    console.log('Generating YouTube thumbnail prompt for script length:', script.length);
    
    // Extract main character details for consistency
    const mainCharacter = extractMainCharacter(characterPrompts || '');
    console.log('Extracted main character:', mainCharacter);
    
    // Prepare context from character, item, and location prompts
    const contextDetails = [];
    if (characterPrompts) contextDetails.push(`CHARACTERS:\n${characterPrompts}`);
    if (itemPrompts) contextDetails.push(`ITEMS:\n${itemPrompts}`);
    if (locationPrompts) contextDetails.push(`LOCATIONS:\n${locationPrompts}`);
    
    // Create comprehensive prompt for AI
    const thumbnailPrompt = `
You are an expert YouTube thumbnail designer specializing in animated content. Create ONE optimized thumbnail prompt for Imagen 4 Ultra that follows YouTube thumbnail best practices and maximizes click-through rates.

STORY SCRIPT:
${script}

${contextDetails.length > 0 ? 'STORY ELEMENTS:\n' + contextDetails.join('\n\n') : ''}

${mainCharacter ? `
MAIN CHARACTER CONSISTENCY REQUIREMENT:
You MUST use the EXACT character description from the story elements above. The main character is "${mainCharacter.name}" and must be described exactly as: "${mainCharacter.description}"

DO NOT create generic descriptions like "tiny field mouse" - USE THE EXACT CHARACTER DETAILS including specific physical features, clothing, colors, and personality traits from the character prompts above.
` : ''}

YOUTUBE THUMBNAIL BEST PRACTICES:
1. **High Contrast & Bright Colors**: Use vibrant, saturated colors that pop against YouTube's white/dark backgrounds
2. **Large, Expressive Characters**: Main character should take up 30-50% of frame with exaggerated expressions
3. **Rule of Thirds**: Position key elements along the rule of thirds grid lines
4. **Emotional Expression**: Show clear emotions - surprise, excitement, curiosity, shock, joy
5. **Action & Movement**: Suggest motion or dynamic poses to create energy
6. **Visual Hierarchy**: One main focal point with supporting elements
7. **Mobile Optimization**: Design must be readable at small sizes (thumbnail view)
8. **Color Psychology**: 
   - Red/Orange: Excitement, urgency
   - Blue: Trust, calm curiosity
   - Yellow: Happiness, attention-grabbing
   - Purple: Mystery, creativity
   - Green: Nature, growth, success

IMAGEN 4 ULTRA PROMPT REQUIREMENTS:
- Use vivid, specific descriptive language
- Include technical photography terms for quality
- Specify composition and framing
- Add lighting and mood descriptors
- Keep under 200 words for optimal results
- Use "3D animated style" or "3D cartoon style" to match HoloAnima's aesthetic

THUMBNAIL COMPOSITION GUIDELINES:
- **Foreground**: Main character with clear emotion (surprised, excited, curious, determined)
- **Mid-ground**: Key story element or object creating intrigue
- **Background**: Simple, contrasting environment that doesn't compete for attention
- **Lighting**: Dramatic, directional lighting to create depth and focus
- **Angle**: Slightly low angle to make characters appear more dynamic and heroic

Generate a single, optimized prompt that will create a compelling YouTube thumbnail that viewers can't resist clicking. The thumbnail should instantly communicate the story's main appeal and create curiosity.

CRITICAL REQUIREMENTS:
1. Focus on ONE main character and ONE key story element. Avoid cluttered compositions.
2. MANDATORY: Use the EXACT character description provided above - including specific physical traits, clothing, and appearance details.
3. Do NOT use generic character descriptions - maintain complete consistency with the established character design.

Response format - provide ONLY the Imagen 4 Ultra prompt, nothing else:
`;

    const userKeysResult = await getUserApiKeys(userId);
    let response: string;

    // Generate using selected AI provider
    if (aiProvider === 'perplexity') {
      if (!userKeysResult.success || !userKeysResult.data?.perplexityApiKey) {
        return { success: false, error: "Perplexity API key not configured by user. Please set it in Account Settings." };
      }
      try {
        const { generateWithPerplexity } = await import('../../ai/genkit');

        const messages = [
          { role: 'system' as const, content: 'You are a YouTube thumbnail design expert specializing in animated content. Create compelling thumbnail prompts that maximize click-through rates using proven design psychology and visual hierarchy principles.' },
          { role: 'user' as const, content: thumbnailPrompt }
        ];

        response = await runFlow(generateWithPerplexity, {
          modelName: perplexityModel || 'sonar',
          messages: messages,
          userId: userId,
          contentType: 'script',
          useStructuredOutput: false,
        });

      } catch (error) {
        console.error("Error in generateThumbnailPrompt with Perplexity AI call:", error);
        const errorMessage = error instanceof Error ? error.message : "Failed to generate thumbnail prompt with Perplexity.";
        return { success: false, error: errorMessage };
      }
    } else { // Default to Google AI
      if (!userKeysResult.success || !userKeysResult.data?.googleApiKey) {
        return { success: false, error: "Google API key not configured by user. Please set it in Account Settings." };
      }
      const userGoogleKey = userKeysResult.data.googleApiKey;

      try {
        const modelName = googleScriptModel || 'gemini-2.0-flash';
        const localAi = genkit({ plugins: [googleAI({ apiKey: userGoogleKey })], model: `googleai/${modelName}` });
        const { text } = await localAi.generate({ prompt: thumbnailPrompt });
        response = text || '';
      } catch (error) {
        console.error("Error in generateThumbnailPrompt AI call (Google):", error);
        return { success: false, error: "Failed to generate thumbnail prompt with user's Google key." };
      }
    }

    // Clean up the response to get just the prompt
    let cleanedPrompt = response.trim();
    
    // Remove any potential markdown formatting or extra text
    if (cleanedPrompt.includes('```')) {
      const codeMatch = cleanedPrompt.match(/```(?:.*\n)?([\s\S]*?)```/);
      if (codeMatch) {
        cleanedPrompt = codeMatch[1].trim();
      }
    }
    
    // Remove common AI response prefixes
    const prefixesToRemove = [
      'Here is the Imagen 4 Ultra prompt:',
      'Imagen 4 Ultra prompt:',
      'Prompt:',
      'Here\'s the prompt:',
      'The prompt is:',
      'Generated prompt:'
    ];
    
    for (const prefix of prefixesToRemove) {
      if (cleanedPrompt.toLowerCase().startsWith(prefix.toLowerCase())) {
        cleanedPrompt = cleanedPrompt.substring(prefix.length).trim();
        break;
      }
    }

    // Expand entity references in the generated prompt if we have story data
    if (userId && storyId) {
      try {
        const { getStory } = await import('@/actions/baserowStoryActions');
        const storyResult = await getStory(storyId, userId);
        if (storyResult.success && storyResult.data) {
          const { parseEntityReferences } = await import('@/app/(app)/assemble-video/utils');
          cleanedPrompt = parseEntityReferences(cleanedPrompt, storyResult.data);
          console.log('[generateThumbnailPrompt] Expanded prompt with entity references:', cleanedPrompt);
        }
      } catch (error) {
        console.warn('[generateThumbnailPrompt] Failed to expand entity references:', error);
      }
    }

    // Ensure the prompt includes quality enhancers for Imagen 4 Ultra
    const qualityEnhancers = [
      'high quality', 'ultra high definition', '4K', '8K', 
      'professional', 'masterpiece', 'detailed'
    ];
    
    const hasQualityEnhancer = qualityEnhancers.some(enhancer => 
      cleanedPrompt.toLowerCase().includes(enhancer.toLowerCase())
    );
    
    if (!hasQualityEnhancer) {
      cleanedPrompt += ', high quality, ultra high definition, professional digital art';
    }

    // Ensure it mentions 3D animated style if not already present
    if (!cleanedPrompt.toLowerCase().includes('3d') && !cleanedPrompt.toLowerCase().includes('animated')) {
      cleanedPrompt = '3D animated style, ' + cleanedPrompt;
    }

    console.log('Generated thumbnail prompt:', cleanedPrompt);

    return {
      success: true,
      data: { thumbnailPrompt: cleanedPrompt }
    };

  } catch (error) {
    console.error('Error generating thumbnail prompt:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate thumbnail prompt'
    };
  }
}
