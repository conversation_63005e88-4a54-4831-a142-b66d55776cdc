// Main barrel export file for all story actions
// This file provides a clean public API while keeping functionality modularized
// Note: Individual modules have "use server" - this barrel file just re-exports

// Re-export from story generation module
export { 
    generateTitle, 
    generateScript, 
    generateScriptChunks 
} from './story/storyGenerationActions';

// Re-export from character actions module
export { generateCharacterPrompts } from './story/characterActions';

// Re-export from audio actions module
export { 
    generateNarrationAudio, 
    generateVoicePreview 
} from './story/audioActions';
export type { 
    GenerateNarrationAudioActionInput, 
    VoicePreviewInput 
} from './story/audioActions';

// Re-export from image actions module
export { 
    generateImagePrompts,
    generateImageFromGemini,
    generateImageFromImagen3,
    generateImageFromImagen4,
    generateImageFromImagen4Ultra,
    generateImageFromPrompt,
    generateVideoFromImage
} from './story/imageActions';

// Re-export from translation actions module
export { 
    generateSpanishTranslation, 
    generateRomanianTranslation,
    generateAndSaveSpanishTranslation,
    generateAndSaveRomanianTranslation
} from './story/translationActions';

// Re-export from model actions module
export { 
    listGoogleScriptModels, 
    listPerplexityModels 
} from './story/modelActions';

// Re-export from SEO actions module
export { generateSeoMetadata } from './story/seoGenerationActions';

// Re-export from thumbnail actions module
export { generateThumbnailPrompt } from './story/thumbnailGenerationActions';
export { generateThumbnailImage } from './story/thumbnailImageActions';

// Re-export all schema types for backwards compatibility
export type {
    GenerateTitleInput,
    GenerateScriptInput,
    GenerateCharacterPromptsInput,
    GenerateImagePromptsInput,
    GenerateScriptChunksInput,
    GenerateSpanishTranslationInput,
    GenerateRomanianTranslationInput
} from './storyActionSchemas';
