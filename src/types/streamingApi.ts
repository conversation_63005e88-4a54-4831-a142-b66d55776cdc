import type { GenerateImagePromptsOutput } from '@/ai/flows/generate-image-prompts-types';

export type StreamingApiResponse = 
  | {
      type: 'start' | 'ping';
      message: string;
    }
  | {
      type: 'success';
      data: GenerateImagePromptsOutput | { romanianChunks: Array<{ id: string; text: string; index: number }> } | { spanishChunks: Array<{ id: string; text: string; index: number }> };
      message: string;
    }
  | {
      type: 'error';
      message: string;
    };