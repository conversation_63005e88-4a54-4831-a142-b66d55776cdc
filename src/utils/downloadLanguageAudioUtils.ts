import J<PERSON><PERSON><PERSON> from 'jszip';
import { Story } from '@/types/story';
import { getValidMinIOUrl } from '@/utils/signedUrlGenerator';

/**
 * Add WAV headers to raw PCM data from Google TTS
 * Google TTS returns 24kHz, 16-bit, mono PCM data
 */
function addWavHeadersToBuffer(pcmData: ArrayBuffer): ArrayBuffer {
  const pcmBytes = pcmData.byteLength;
  const sampleRate = 24000; // Google TTS uses 24kHz
  const numChannels = 1; // Mono
  const bitsPerSample = 16; // 16-bit
  const bytesPerSample = bitsPerSample / 8;
  const blockAlign = numChannels * bytesPerSample;
  const byteRate = sampleRate * blockAlign;
  
  // WAV header is 44 bytes
  const headerSize = 44;
  const fileSize = headerSize + pcmBytes;
  
  const buffer = new ArrayBuffer(fileSize);
  const view = new DataView(buffer);
  
  // RIFF header
  view.setUint32(0, 0x52494646, false); // "RIFF"
  view.setUint32(4, fileSize - 8, true); // File size - 8
  view.setUint32(8, 0x57415645, false); // "WAVE"
  
  // fmt chunk
  view.setUint32(12, 0x666d7420, false); // "fmt "
  view.setUint32(16, 16, true); // fmt chunk size
  view.setUint16(20, 1, true); // Audio format (1 = PCM)
  view.setUint16(22, numChannels, true); // Number of channels
  view.setUint32(24, sampleRate, true); // Sample rate
  view.setUint32(28, byteRate, true); // Byte rate
  view.setUint16(32, blockAlign, true); // Block align
  view.setUint16(34, bitsPerSample, true); // Bits per sample
  
  // data chunk
  view.setUint32(36, 0x64617461, false); // "data"
  view.setUint32(40, pcmBytes, true); // Data size
  
  const dataArray = new Uint8Array(buffer, headerSize);
  const pcmArray = new Uint8Array(pcmData);
  dataArray.set(pcmArray);
  
  return buffer;
}

/**
 * Fetch an audio file and return it as a Blob with proper MIME type
 */
async function fetchAudioFile(url: string): Promise<Blob | null> {
  // Check if this is a Minio URL that might need special handling
  const isMinioUrl = url.includes('minio-api.holoanima.com');
  
  try {
    if (!url || typeof url !== 'string') {
      console.warn(`[AUDIO DOWNLOAD] Invalid URL provided:`, url);
      return null;
    }
    
    // Validate URL format
    try {
      new URL(url);
    } catch (urlError) {
      console.warn(`[AUDIO DOWNLOAD] Malformed URL:`, url, urlError);
      return null;
    }
    
    // For MinIO URLs, ensure we have a valid (non-expired) URL
    let validUrl = url;
    if (isMinioUrl) {
      console.log(`[AUDIO DOWNLOAD] Validating MinIO URL for audio:`, url);
      try {
        const refreshedUrl = await getValidMinIOUrl(url);
        if (refreshedUrl) {
          validUrl = refreshedUrl;
          console.log(`[AUDIO DOWNLOAD] Using refreshed URL for audio`);
        } else {
          console.warn(`[AUDIO DOWNLOAD] Failed to refresh MinIO URL:`, url);
          return null;
        }
      } catch (error) {
        console.error(`[AUDIO DOWNLOAD] Error refreshing MinIO URL:`, error);
        return null;
      }
    }
    
    console.log(`[AUDIO DOWNLOAD] Fetching audio from:`, validUrl);
    
    // Add headers for better compatibility - use valid URL (potentially refreshed)
    const response = await fetch(validUrl, {
      method: 'GET',
      headers: {
        'Accept': 'audio/*',
      }
    });
    
    if (!response.ok) {
      console.warn(`[AUDIO DOWNLOAD] Failed to fetch file from ${validUrl} - Status: ${response.status} ${response.statusText}`);
      return null;
    }
    
    console.log(`[AUDIO DOWNLOAD] Successfully fetched file, content-type: ${response.headers.get('content-type')}, size: ${response.headers.get('content-length') || 'unknown'}`);
  
    const arrayBuffer = await response.arrayBuffer();
    const view = new Uint8Array(arrayBuffer);
    
    // Check for WAV signature: "RIFF"
    const isWav = view[0] === 0x52 && view[1] === 0x49 && view[2] === 0x46 && view[3] === 0x46;
    
    // Check URL extension as additional hint
    const urlHasWavExtension = validUrl.toLowerCase().includes('.wav');
    const urlHasMp3Extension = validUrl.toLowerCase().includes('.mp3');
    
    // Very strict MP3 detection - only for genuine MP3 files
    const isId3Tag = view[0] === 0x49 && view[1] === 0x44 && view[2] === 0x33; // "ID3" tag
    const isMpegFrame = view[0] === 0xFF && (view[1] & 0xE0) === 0xE0;
    
    // For MinIO files, trust the file extension in URL more than binary detection
    // since we know MinIO stores the correct format
    let isMp3: boolean;
    if (isMinioUrl) {
      // For MinIO: prioritize URL extension, then binary detection
      if (urlHasMp3Extension && !isWav) {
        isMp3 = true;
      } else if (urlHasWavExtension || isWav) {
        isMp3 = false;
      } else {
        // Fallback to binary detection only if URL doesn't help
        isMp3 = isId3Tag && !isWav;
      }
    } else {
      // For non-MinIO: use binary detection
      isMp3 = (isId3Tag || isMpegFrame) && !isWav;
    }
    
    console.log(`[AUDIO DOWNLOAD] Audio file analysis:`, {
      size: arrayBuffer.byteLength,
      firstBytes: Array.from(view.slice(0, 8)).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '),
      url: validUrl,
      urlHasWavExtension,
      urlHasMp3Extension,
      isWav,
      isMp3,
      isMinioUrl,
      detectionMethod: isMinioUrl ? 'URL extension + binary' : 'binary only',
      likelyTTS: isWav || (!isMp3 && urlHasWavExtension) ? 'Google TTS' : isMp3 ? 'ElevenLabs' : 'Unknown'
    });
    
    // Accurate detection based on actual file content
    if (isWav) {
      console.log(`[AUDIO DOWNLOAD] Detected WAV file (likely Google TTS)`);
      return new Blob([arrayBuffer], { type: 'audio/wav' });
    }
    
    if (isMp3) {
      console.log(`[AUDIO DOWNLOAD] Detected MP3 file (likely ElevenLabs TTS)`);
      return new Blob([arrayBuffer], { type: 'audio/mpeg' });
    }
    
    // If neither WAV nor MP3, treat as raw PCM data and add WAV headers
    console.log(`[AUDIO DOWNLOAD] Unknown audio format - treating as raw PCM and adding WAV headers`);
    const wavBuffer = addWavHeadersToBuffer(arrayBuffer);
    return new Blob([wavBuffer], { type: 'audio/wav' });
  } catch (error) {
    console.warn(`[AUDIO DOWNLOAD] Error fetching file from ${url}:`, error);
    return null;
  }
}

/**
 * Download all audio files for a specific language as a ZIP file
 */
export async function downloadLanguageAudioAsZip(storyData: Story, language: 'en' | 'es' | 'ro') {
  if (!storyData.title) {
    throw new Error('Story must have a title to download');
  }

  console.log(`[LANG AUDIO ZIP] Starting download for ${language} audio for story:`, {
    title: storyData.title,
  });

  const zip = new JSZip();
  
  // Determine which chunks to use based on language
  let chunks;
  let languageName;
  
  switch (language) {
    case 'en':
      chunks = storyData.narrationChunks;
      languageName = 'English';
      break;
    case 'es':
      chunks = storyData.spanishNarrationChunks;
      languageName = 'Spanish';
      break;
    case 'ro':
      chunks = storyData.romanianNarrationChunks;
      languageName = 'Romanian';
      break;
    default:
      throw new Error(`Unsupported language: ${language}`);
  }
  
  if (!chunks || chunks.length === 0) {
    throw new Error(`No ${languageName} audio chunks found for this story`);
  }

  // Add audio chunks to ZIP
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    if (chunk.audioUrl) {
      try {
        console.log(`[LANG AUDIO ZIP] Processing ${languageName} audio chunk ${i + 1}...`);
        const audioBlob = await fetchAudioFile(chunk.audioUrl);
        if (audioBlob && audioBlob.size > 0) {
          const extension = audioBlob.type === 'audio/mpeg' ? 'mp3' : 'wav';
          const filename = `${language}_chunk_${chunk.index !== undefined ? chunk.index + 1 : i + 1}.${extension}`;
          zip.file(filename, audioBlob);
          console.log(`[LANG AUDIO ZIP] ✅ Successfully added ${languageName} audio chunk ${i + 1} as ${filename} (${audioBlob.size} bytes, type: ${audioBlob.type})`);
        } else {
          console.warn(`[LANG AUDIO ZIP] ❌ Failed to fetch or empty ${languageName} audio chunk ${i + 1} - URL may be expired or invalid`);
        }
      } catch (error) {
        console.error(`[LANG AUDIO ZIP] Error processing ${languageName} audio chunk ${i + 1}:`, error);
      }
    }
  }

  try {
    console.log(`[LANG AUDIO ZIP] Starting ZIP file generation for ${languageName} audio...`);
    const content = await zip.generateAsync({ 
      type: 'blob',
      compression: 'DEFLATE',
      compressionOptions: {
        level: 6
      }
    });
    
    console.log(`[LANG AUDIO ZIP] ZIP file generated successfully, size: ${content.size} bytes`);
    
    if (content.size === 0) {
      throw new Error('Generated ZIP file is empty');
    }
    
    const url = window.URL.createObjectURL(content);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${storyData.title.replace(/[^a-zA-Z0-9]/g, '_')}_${languageName}_audio.zip`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    console.log(`[LANG AUDIO ZIP] Download initiated successfully for ${languageName} audio`);
  } catch (error) {
    console.error(`[LANG AUDIO ZIP] Error generating or downloading ZIP file:`, error);
    throw new Error(`Failed to create ZIP file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}