/**
 * Advanced prompt builder for Google Imagen 3/4 following 2025 best practices
 * Based on Google's official documentation and prompt engineering guidelines
 */

export interface EntityDescription {
  name: string;
  description: string;
  type: 'character' | 'item' | 'location';
}

export interface PromptBuilderOptions {
  entities: EntityDescription[];
  actionPrompt: string;
  styleString?: string;
  technicalModifiers?: {
    cameraAngle?: string; // e.g., "wide shot", "close-up", "aerial view"
    lighting?: string; // e.g., "golden hour", "soft lighting", "dramatic lighting"
    lensType?: string; // e.g., "35mm", "wide-angle", "macro"
    quality?: string; // e.g., "4K", "HDR", "high quality"
    mood?: string; // e.g., "dreamy", "vibrant", "peaceful"
  };
  negativePrompt?: string;
  aspectRatio?: string;
}

/**
 * Builds an optimized prompt for Imagen 3/4 following Google's best practices:
 * - Flattens entity descriptions into the main sentence
 * - Integrates style naturally at the end
 * - Includes technical modifiers inline
 * - Creates one rich, descriptive sentence instead of blocks
 */
export function buildOptimizedImagenPrompt(options: PromptBuilderOptions): {
  prompt: string;
  negativePrompt?: string;
  metadata: {
    entityCount: number;
    hasStyle: boolean;
    hasTechnicalModifiers: boolean;
    tokenEstimate: number;
  };
} {
  const { entities, actionPrompt, styleString, technicalModifiers, negativePrompt } = options;

  // Step 1: Extract and inline entity descriptions
  const entityDescriptions = new Map<string, string>();
  entities.forEach(entity => {
    entityDescriptions.set(entity.name, entity.description);
  });

  // Step 2: Replace entity references with inline descriptions
  let enrichedPrompt = actionPrompt;
  
  // Replace @EntityName with detailed descriptions inline
  entities.forEach(entity => {
    const entityRef = `@${entity.name}`;
    const inlineDescription = entity.description;
    
    // Replace the reference with the description in a natural way
    enrichedPrompt = enrichedPrompt.replace(
      new RegExp(entityRef, 'gi'), 
      inlineDescription
    );
  });

  // Step 2.5: Handle any remaining @placeholders that weren't found in entities
  // Replace them with generic descriptions to avoid leaving placeholders in the final prompt
  const remainingPlaceholders = enrichedPrompt.match(/@[A-Za-z0-9_]+/g) || [];
  remainingPlaceholders.forEach(placeholder => {
    const entityName = placeholder.substring(1); // Remove @
    console.log(`[PromptBuilder] Unfound placeholder ${placeholder} being replaced with generic description`);
    
    // Create a generic description based on the entity name
    const genericDescription = entityName
      .replace(/([A-Z])/g, ' $1') // Add spaces before capital letters
      .toLowerCase()
      .trim();
    
    enrichedPrompt = enrichedPrompt.replace(
      new RegExp(placeholder, 'gi'),
      genericDescription
    );
  });

  // Step 3: Build the complete prompt parts
  const promptParts: string[] = [];

  // Main scene description (now with inline entity descriptions)
  promptParts.push(enrichedPrompt);

  // Step 4: Add technical modifiers inline
  const technicalParts: string[] = [];
  
  if (technicalModifiers?.cameraAngle) {
    technicalParts.push(technicalModifiers.cameraAngle);
  }
  
  if (technicalModifiers?.lighting) {
    technicalParts.push(technicalModifiers.lighting);
  }
  
  if (technicalModifiers?.lensType) {
    technicalParts.push(`${technicalModifiers.lensType} lens`);
  }
  
  if (technicalModifiers?.quality) {
    technicalParts.push(technicalModifiers.quality);
  }

  if (technicalParts.length > 0) {
    promptParts.push(technicalParts.join(', '));
  }

  // Step 5: Add style naturally at the end
  if (styleString) {
    // Remove any "Use the following artistic style:" prefixes if they exist
    const cleanStyle = styleString.replace(/^(Use the following artistic style:|in the style of)/i, '').trim();
    
    if (cleanStyle.startsWith('in the style of') || cleanStyle.includes('style')) {
      promptParts.push(cleanStyle);
    } else {
      promptParts.push(`in the style of ${cleanStyle}`);
    }
  }

  // Step 6: Add mood/atmosphere if specified
  if (technicalModifiers?.mood) {
    promptParts.push(`${technicalModifiers.mood} atmosphere`);
  }

  // Step 7: Join everything into one rich sentence
  const finalPrompt = promptParts.join(', ').replace(/,\s*,/g, ',').trim();

  // Step 8: Estimate token count (rough approximation)
  const tokenEstimate = Math.ceil(finalPrompt.split(/\s+/).length * 1.3);

  return {
    prompt: finalPrompt,
    negativePrompt,
    metadata: {
      entityCount: entities.length,
      hasStyle: !!styleString,
      hasTechnicalModifiers: !!technicalModifiers && Object.keys(technicalModifiers).length > 0,
      tokenEstimate
    }
  };
}

/**
 * Extracts technical modifiers from scene metadata or context
 */
export function extractTechnicalModifiers(
  sceneContext?: string,
  defaultModifiers?: PromptBuilderOptions['technicalModifiers']
): PromptBuilderOptions['technicalModifiers'] {
  const modifiers = { ...defaultModifiers };

  if (!sceneContext) return modifiers;

  const context = sceneContext.toLowerCase();

  // Extract camera angles
  if (context.includes('close') || context.includes('zoom')) {
    modifiers.cameraAngle = 'close-up';
  } else if (context.includes('wide') || context.includes('landscape')) {
    modifiers.cameraAngle = 'wide shot';
  } else if (context.includes('aerial') || context.includes('above')) {
    modifiers.cameraAngle = 'aerial view';
  }

  // Extract lighting
  if (context.includes('sunset') || context.includes('golden')) {
    modifiers.lighting = 'golden hour lighting';
  } else if (context.includes('morning') || context.includes('dawn')) {
    modifiers.lighting = 'soft morning light';
  } else if (context.includes('dramatic') || context.includes('shadow')) {
    modifiers.lighting = 'dramatic lighting';
  } else if (context.includes('natural') || context.includes('outdoor')) {
    modifiers.lighting = 'natural lighting';
  }

  // Extract mood
  if (context.includes('peaceful') || context.includes('calm')) {
    modifiers.mood = 'peaceful';
  } else if (context.includes('exciting') || context.includes('dynamic')) {
    modifiers.mood = 'dynamic';
  } else if (context.includes('dreamy') || context.includes('magical')) {
    modifiers.mood = 'dreamy';
  }

  // Set default quality
  if (!modifiers.quality) {
    modifiers.quality = 'high quality, detailed';
  }

  return modifiers;
}

/**
 * Creates standardized negative prompts for different content types
 */
export function getStandardNegativePrompt(contentType: 'child-friendly' | 'general' | 'artistic' = 'general'): string {
  const baseNegative = 'blurry, low quality, distorted, watermark, text overlay, duplicate, cropped';
  
  switch (contentType) {
    case 'child-friendly':
      return `${baseNegative}, inappropriate content, scary, violent`;
    case 'artistic':
      return `${baseNegative}, photorealistic when not intended, overly saturated`;
    case 'general':
    default:
      return baseNegative;
  }
}

/**
 * Validates prompt length and provides suggestions
 */
export function validatePrompt(prompt: string): {
  isValid: boolean;
  tokenEstimate: number;
  suggestions: string[];
} {
  const tokenEstimate = Math.ceil(prompt.split(/\s+/).length * 1.3);
  const maxTokens = 480; // Imagen's limit
  
  const suggestions: string[] = [];
  
  if (tokenEstimate > maxTokens) {
    suggestions.push(`Prompt is too long (${tokenEstimate} tokens). Maximum is ${maxTokens} tokens.`);
    suggestions.push('Consider shortening entity descriptions or removing some technical modifiers.');
  }
  
  if (prompt.length < 50) {
    suggestions.push('Prompt might be too short. Consider adding more descriptive details.');
  }
  
  if (!prompt.includes('style') && !prompt.includes('quality')) {
    suggestions.push('Consider adding style or quality modifiers for better results.');
  }
  
  return {
    isValid: tokenEstimate <= maxTokens && prompt.length >= 10,
    tokenEstimate,
    suggestions
  };
}
