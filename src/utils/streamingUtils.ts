/**
 * Utility function to handle streaming responses from the API
 * @param response ReadableStream response from the API
 * @param onMessage Callback function to handle each message
 * @param onComplete Callback function to handle completion
 * @param onError Callback function to handle errors
 */
export async function handleStreamingResponse<T>(
  response: Response,
  onMessage: (data: T) => void,
  onComplete?: () => void,
  onError?: (error: unknown) => void
): Promise<void> {
  if (!response.body) {
    onError?.(new Error('Response body is empty'));
    return;
  }

  const reader = response.body.getReader();
  const decoder = new TextDecoder();

  try {
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        onComplete?.();
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            onMessage(data as T);
          } catch (parseError) {
            console.warn('Failed to parse streaming message:', line, parseError);
          }
        }
      }
    }
  } catch (error) {
    onError?.(error);
  } finally {
    reader.releaseLock();
  }
}