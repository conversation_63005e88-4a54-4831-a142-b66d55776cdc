/**
 * Utility for generating fresh signed URLs from MinIO storage paths
 */

import { getFileSignedUrl } from '@/actions/minioStorageActions';

/**
 * Check if a URL is a MinIO storage path (not a signed URL)
 */
export function isMinIOStoragePath(url: string): boolean {
  // Check if it's a path without query parameters (no signed URL params)
  return url.includes('minio-api.holoanima.com') && 
         url.includes('/storytailor-media/') && 
         !url.includes('X-Amz-Algorithm');
}

/**
 * Check if a URL is an expired or about-to-expire signed URL
 */
export function isExpiredOrExpiring(url: string): boolean {
  if (!url.includes('X-Amz-Date=')) return false;
  
  try {
    const urlObj = new URL(url);
    const dateParam = urlObj.searchParams.get('X-Amz-Date');
    const expiresParam = urlObj.searchParams.get('X-Amz-Expires');
    
    if (!dateParam || !expiresParam) return false;
    
    // Parse the date: format is YYYYMMDDTHHMMSSZ
    const year = parseInt(dateParam.substring(0, 4));
    const month = parseInt(dateParam.substring(4, 6)) - 1; // Month is 0-indexed
    const day = parseInt(dateParam.substring(6, 8));
    const hour = parseInt(dateParam.substring(9, 11));
    const minute = parseInt(dateParam.substring(11, 13));
    const second = parseInt(dateParam.substring(13, 15));
    
    const signedDate = new Date(year, month, day, hour, minute, second);
    const expiryDate = new Date(signedDate.getTime() + parseInt(expiresParam) * 1000);
    
    // Consider expired if expires within 1 hour (buffer for safety)
    const bufferTime = 60 * 60 * 1000; // 1 hour in milliseconds
    return Date.now() >= (expiryDate.getTime() - bufferTime);
  } catch (error) {
    console.warn('Error parsing URL date:', error);
    return false;
  }
}

/**
 * Extract MinIO file path from a signed URL
 */
export function extractFilePathFromSignedUrl(signedUrl: string): string | null {
  try {
    const url = new URL(signedUrl);
    const bucketName = 'storytailor-media';
    const pathParts = url.pathname.split('/').filter(part => part.length > 0);
    
    const bucketIndex = pathParts.findIndex(part => part === bucketName);
    if (bucketIndex !== -1 && bucketIndex < pathParts.length - 1) {
      return pathParts.slice(bucketIndex + 1).join('/');
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting file path from signed URL:', error);
    return null;
  }
}

/**
 * Generate a fresh signed URL for a MinIO storage path or refresh an expired signed URL
 */
export async function generateFreshSignedUrl(urlOrPath: string): Promise<string | null> {
  try {
    let filePath: string | null = null;
    
    // If it's already a storage path (not a signed URL), use it directly
    if (isMinIOStoragePath(urlOrPath) && !urlOrPath.includes('X-Amz-Algorithm')) {
      // Extract just the file path part after /storytailor-media/
      const url = new URL(urlOrPath);
      const bucketName = 'storytailor-media';
      const pathParts = url.pathname.split('/').filter(part => part.length > 0);
      const bucketIndex = pathParts.findIndex(part => part === bucketName);
      
      if (bucketIndex !== -1 && bucketIndex < pathParts.length - 1) {
        filePath = pathParts.slice(bucketIndex + 1).join('/');
      }
    } else {
      // Extract file path from signed URL
      filePath = extractFilePathFromSignedUrl(urlOrPath);
    }
    
    if (!filePath) {
      console.warn('Could not extract file path from URL:', urlOrPath);
      return null;
    }
    
    console.log('Generating fresh signed URL for file path:', filePath);
    
    // Generate new signed URL with 7-day expiration
    const signedUrl = await getFileSignedUrl(filePath, 7 * 24 * 60 * 60);
    return signedUrl;
    
  } catch (error) {
    console.error('Error generating fresh signed URL:', error);
    return null;
  }
}

/**
 * Get a valid URL for MinIO storage - either use existing if fresh, or generate new if expired
 */
export async function getValidMinIOUrl(originalUrl: string): Promise<string | null> {
  if (!originalUrl) return null;
  
  // If it's not a MinIO URL at all, return as-is
  if (!originalUrl.includes('minio-api.holoanima.com')) {
    return originalUrl;
  }
  
  // If it's a signed URL and not expired, use it
  if (originalUrl.includes('X-Amz-Algorithm') && !isExpiredOrExpiring(originalUrl)) {
    return originalUrl;
  }
  
  // Generate fresh signed URL
  return await generateFreshSignedUrl(originalUrl);
}
