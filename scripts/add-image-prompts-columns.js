#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to add image_prompts and action_prompts columns to Baserow stories table
 * This helps separate image prompts from the JSON settings to improve performance
 */

const BASEROW_API_URL = process.env.BASEROW_API_URL || 'http://192.168.31.251:8980/api';
const BASEROW_TOKEN = process.env.BASEROW_TOKEN;
const BASEROW_STORIES_TABLE_ID = process.env.BASEROW_STORIES_TABLE_ID || '696';

if (!BASEROW_TOKEN) {
  console.error('BASEROW_TOKEN environment variable is required');
  process.exit(1);
}

const headers = {
  'Authorization': `Token ${BASEROW_TOKEN}`,
  'Content-Type': 'application/json'
};

async function apiCall(endpoint, method = 'GET', body = null) {
  const url = `${BASEROW_API_URL}${endpoint}`;
  
  const response = await fetch(url, {
    method,
    headers,
    body: body ? JSON.stringify(body) : undefined
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Baserow API call failed: ${response.status} ${response.statusText}\n${errorText}`);
  }

  return response.json();
}

async function addImagePromptsColumns() {
  try {
    console.log('Adding image_prompts column...');
    
    // Add image_prompts column (Long Text type)
    const imagePromptsColumn = {
      type: 'long_text',
      name: 'image_prompts'
    };
    
    const imagePromptsResult = await apiCall(`/database/fields/table/${BASEROW_STORIES_TABLE_ID}/`, 'POST', imagePromptsColumn);
    console.log('✅ Added image_prompts column:', imagePromptsResult.id);

    console.log('Adding action_prompts column...');
    
    // Add action_prompts column (Long Text type)
    const actionPromptsColumn = {
      type: 'long_text',
      name: 'action_prompts'
    };
    
    const actionPromptsResult = await apiCall(`/database/fields/table/${BASEROW_STORIES_TABLE_ID}/`, 'POST', actionPromptsColumn);
    console.log('✅ Added action_prompts column:', actionPromptsResult.id);

    console.log('🎉 Successfully added both columns to the stories table!');
    
  } catch (error) {
    if (error.message.includes('already exists') || error.message.includes('duplicate')) {
      console.log('ℹ️  Columns may already exist, which is fine.');
    } else {
      console.error('❌ Error adding columns:', error.message);
      process.exit(1);
    }
  }
}

// Run the migration
addImagePromptsColumns();