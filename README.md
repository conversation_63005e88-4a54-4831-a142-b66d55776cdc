l# StoryTailor - AI Animated Story Generator

StoryTailor is a Next.js application that allows users to generate animated video scripts, narration audio, image prompts, and complete videos using AI. It features user authentication via Firebase Auth and uses a hybrid architecture with Baserow database and MinIO storage for cost-effective, self-hosted backend services.

## Getting Started

### Prerequisites

- Node.js (v18 or later recommended)
- npm or yarn
- A Firebase project (for authentication only)
- Access to Baserow database and MinIO storage (self-hosted or cloud)

### 1. Clone the Repository

```bash
git clone <repository-url>
cd story-tailor
```

### 2. Install Dependencies

```bash
npm install
# or
yarn install
```

### 3. Set Up Firebase Authentication

1.  **Create a Firebase Project**: Go to the [Firebase Console](https://console.firebase.google.com/) and create a new project.
2.  **Register Your App**:
    *   In your Firebase project, go to Project Settings.
    *   Under "Your apps", click the Web icon (`</>`) to add a new web app.
    *   Give your app a nickname and click "Register app".
    *   Firebase will provide you with a `firebaseConfig` object. You'll need these values for your environment variables.
3.  **Enable Authentication**:
    *   In the Firebase console, go to "Authentication" (under Build).
    *   Click "Get started".
    *   On the "Sign-in method" tab, enable "Email/Password" provider.

### 4. Set Up Baserow Database

StoryTailor uses Baserow (PostgreSQL-based) for data storage instead of Firestore.

**Option A: Use Existing Hosted Instance**
- Database URL: `https://baserow.holoanima.com`
- Contact the project maintainer for API token access

**Option B: Self-Host Baserow**
```bash
# Using Docker
docker run -d \
  --name baserow \
  -e BASEROW_PUBLIC_URL=http://localhost:8980 \
  -p 8980:80 \
  --restart unless-stopped \
  baserow/baserow:latest
```

**Required Tables:**
- **Stories Table** (ID: 696) - Main story data with dedicated columns for image prompts, smart JSON fallback, and enhanced security validation
- **User API Keys Table** (ID: 697) - Encrypted user API key storage

### 5. Set Up MinIO Storage

StoryTailor uses MinIO (S3-compatible) for file storage instead of Firebase Storage.

**Option A: Use Existing Hosted Instance**
- Storage URL: `https://minio.holoanima.com`
- Console: `https://minio.holoanima.com`

**Option B: Self-Host MinIO**
```bash
# Using Docker
docker run -d \
  --name minio \
  -p 9100:9000 -p 9101:9001 \
  -e "MINIO_ROOT_USER=admin" \
  -e "MINIO_ROOT_PASSWORD=your-secure-password" \
  --restart unless-stopped \
  quay.io/minio/minio server /data --console-address ":9001"
```

**Required Bucket:**
- **storytailor-media** - Main storage bucket for user content

### 6. Configure Environment Variables

Create a `.env.local` file in the root of your project with the following configuration:

**File: `.env.local`**
```env
# Firebase Authentication (required)
NEXT_PUBLIC_FIREBASE_API_KEY="YOUR_FIREBASE_API_KEY"
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN="YOUR_AUTH_DOMAIN"
NEXT_PUBLIC_FIREBASE_PROJECT_ID="YOUR_PROJECT_ID"
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET="YOUR_STORAGE_BUCKET"
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID="YOUR_MESSAGING_SENDER_ID"
NEXT_PUBLIC_FIREBASE_APP_ID="YOUR_APP_ID"

# Baserow Database (required)
BASEROW_API_URL="https://baserow.holoanima.com/api"
BASEROW_TOKEN="YOUR_BASEROW_API_TOKEN"
BASEROW_STORIES_TABLE_ID="696"
BASEROW_USER_API_KEYS_TABLE_ID="697"

# MinIO Storage (required)
MINIO_ENDPOINT="https://minio-api.holoanima.com"
MINIO_ACCESS_KEY="YOUR_MINIO_ACCESS_KEY"
MINIO_SECRET_KEY="YOUR_MINIO_SECRET_KEY"
MINIO_BUCKET_NAME="storytailor-media"
MINIO_REGION="us-east-1"

# Google AI (optional - for Genkit flows)
GOOGLE_API_KEY="YOUR_GOOGLE_AI_STUDIO_API_KEY"

# Service Account (for server-side operations)
GOOGLE_APPLICATION_CREDENTIALS="./.secure/storytailor-f089f-2b63d889003f.json"
```

**Environment Variable Notes:**
- Replace `"YOUR_..."` with your actual credentials
- For hosted instances, contact the project maintainer for API tokens
- For self-hosted setups, use your local URLs and credentials

### 7. Run the Development Server

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:9002](http://localhost:9002) with your browser to see the result.

## Migration Status

**✅ MIGRATION COMPLETED** - StoryTailor has successfully migrated from Firebase to a hybrid architecture:

- **Database**: Firestore → Baserow (PostgreSQL) ✅
- **Storage**: Firebase Storage → MinIO (S3-compatible) ✅  
- **Authentication**: Firebase Auth (preserved) ✅
- **Cost Savings**: 30-55% reduction in backend costs ✅
- **Data Sovereignty**: Complete ownership of data ✅

The application now runs on a **hybrid architecture** with Vercel-ready frontend and self-hosted backend services accessible via Cloudflare tunnels.

## Deployment

### Production Deployment (Vercel + Self-Hosted Backend)

1. **Deploy Frontend to Vercel**:
   ```bash
   # Connect your GitHub repository to Vercel
   # Set environment variables in Vercel dashboard
   # Deploy automatically on push to main branch
   ```

2. **Backend Services** (already running):
   - **Database**: https://baserow.holoanima.com
   - **Storage**: https://minio-api.holoanima.com
   - **Console**: https://minio.holoanima.com

3. **Environment Variables for Vercel**:
   ```env
   # Firebase Auth
   NEXT_PUBLIC_FIREBASE_API_KEY=your_key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=storytailor-f089f.firebaseapp.com
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=storytailor-f089f
   
   # Backend Services (via Cloudflare tunnels)
   BASEROW_API_URL=https://baserow.holoanima.com/api
   BASEROW_TOKEN=your_token
   MINIO_ENDPOINT=https://minio-api.holoanima.com
   MINIO_ACCESS_KEY=your_key
   MINIO_SECRET_KEY=your_secret
   ```

### Local Development with Remote Backend

```bash
# Use the hosted backend services
npm run dev
# Application connects to remote Baserow and MinIO via tunnels
```

## Quick Reference

### Key URLs
- **Development**: http://localhost:9002
- **Database Admin**: https://baserow.holoanima.com
- **Storage Console**: https://minio.holoanima.com
- **Documentation**: See `docs/` folder

### Important Files
- **Environment**: `.env.local` (create from template)
- **Database Config**: `src/lib/baserow.ts`
- **Storage Config**: `src/lib/minio.ts`
- **MCP Config**: `~/.kiro/settings/mcp.json`

### Support
- **Issues**: GitHub repository issues
- **Documentation**: `docs/` folder
  - **Development Environment**: `docs/development-environment.md` - **VS Code, Kiro agent, and MCP setup** for AI-enhanced development
  - **Character Recognition**: `docs/character-recognition-system.md` - **Enhanced character recognition** supporting 25+ animal types with intelligent placeholder transformation and multi-term generic handling ("character", "animal", "creature")
  - **Story Processing**: `docs/story-processing-utilities.md` - **Comprehensive utilities** for character transformation, JSON extraction, audio processing, and script validation
  - **Video Rendering**: `docs/video-rendering.md` - **Advanced video rendering** with story processing integration and character consistency
  - **Translation System**: `docs/translation-system-overview.md` - **Auto-save enabled** translation system with automatic Baserow persistence
  - **Translation API**: `docs/translation-api.md` - Complete API reference with auto-save functionality
  - **Component Migration**: `docs/component-migration-guide.md` - **Migration complete** - no code changes required
  - **Translation Performance**: `docs/translation-performance.md` - Performance optimization and auto-save monitoring
  - **Security Enhancements**: `docs/security-enhancements.md` - **Enhanced security** with user authorization and data validation
  - **Data Handling**: `docs/data-handling-improvements.md` - Smart fallback system and automatic optimization
  - **Data Formatting**: `docs/data-formatting-standards.md` - **Standardized date formatting** (YYYY-MM-DD) for optimal database performance
  - **Image Generation**: `docs/image-generation-improvements.md` - **Enhanced character recognition** and automatic database optimization
  - **Blueprint**: `docs/blueprint.md` - Application architecture overview
- **Project Status**: `PROJECT-STATUS.md`
- **Migration Details**: `MIGRATION-COMPLETED.md`
- **Story Processing Enhancement**: `STORY-PROCESSING-ENHANCEMENT.md` - **Comprehensive enhancement** with advanced character recognition and text processing utilities

### 8. Configure Model Context Protocol (MCP) - Enhanced AI Capabilities

StoryTailor now has MCP (Model Context Protocol) **enabled** to enhance AI capabilities with external tools and data sources. This provides advanced research, content validation, and enhanced story generation features.

#### Prerequisites for MCP
- Install `uv` and `uvx` for Python package management:
  ```bash
  # macOS with Homebrew
  brew install uv
  
  # Or follow installation guide: https://docs.astral.sh/uv/getting-started/installation/
  ```

#### Current MCP Status: ENABLED ✅
MCP is now active with enhanced IDE integration and the following configuration hierarchy:

**IDE Configuration** (VS Code):
- **Kiro Agent**: Enabled (`kiroAgent.configureMCP: "Enabled"`)
- **AI Features**: Inline completion and codebase indexing enabled
- **TypeScript**: Auto-closing tags disabled for better development experience

**Configuration Files** (in order of precedence):
1. **User-level**: `~/.kiro/settings/mcp.json` (global, primary configuration)
2. **Workspace-level**: `.kiro/settings/mcp.json` (project-specific, if exists)
3. **Legacy configs**: `.kilocode/mcp.json` and `mcp_settings.json` (compatibility)

**Active Servers**:
- **fetch**: HTTP request capabilities (✅ enabled with auto-approve)
- **context7**: Enhanced context understanding (✅ enabled)
- **brave-search**: Web search capabilities (available in legacy configs)

#### MCP Benefits for StoryTailor
- **Enhanced Research**: Fetch external content for story inspiration and fact-checking
- **Improved Coherence**: Better narrative consistency across story segments  
- **Content Validation**: Real-time verification of story elements
- **External APIs**: Direct integration with research databases and content sources
- **IDE Integration**: Seamless AI assistance during development with Kiro agent

#### Adding Custom MCP Servers
Edit the appropriate configuration file to add new servers:

```json
{
  "mcpServers": {
    "server-name": {
      "command": "uvx",
      "args": ["package-name@latest"],
      "env": {
        "API_KEY": "your-api-key"
      },
      "disabled": false,
      "autoApprove": ["tool1", "tool2"]
    }
  }
}
```

**Note**: Servers automatically reconnect on configuration changes. Use Kiro's command palette (search "MCP") to manage server connections and test functionality.

### Genkit Development (Optional)

If you are working with Genkit flows:
```bash
npm run genkit:watch
```
This will start the Genkit development server, typically on port 3400.

## Core Features

-   **AI Script Generation**: Generate video scripts from prompts using Google AI or Perplexity
-   **Multi-language Narration**: Create MP3 narration in English, Spanish, and Romanian using ElevenLabs or Google TTS with **automatic database persistence** - all translations now auto-save to Baserow
-   **Advanced Character Recognition**: Comprehensive character placeholder transformation system supporting **25+ animal types** with intelligent context-aware processing:
    - **Woodland Creatures**: fox, deer, hedgehog, raccoon, otter, beaver, squirrel, chipmunk, porcupine, skunk, mole, weasel, possum, badger
    - **Farm Animals**: pig, sheep, cow, goat, horse
    - **Common Pets**: cat, dog, hamster, guinea pig, ferret
    - **Birds & Aquatic**: bird, owl, duck, fish
    - **Large Animals**: bear, lion, tiger, elephant, wolf
    - **Small Animals**: mouse, rabbit/bunny, frog, turtle/tortoise
-   **Smart Prompt Transformation**: Converts character placeholders (`@CharacterName`) to natural language descriptions (`the fox`) for optimal AI image generation with dual analysis of character names and descriptions
-   **Generic Term Processing**: Intelligently handles generic terms ("character", "animal", "creature") with context-aware replacements based on story content and character traits
-   **Character & Scene Prompts**: Auto-generate detailed character, item, and location descriptions with **flexible format support** - handles both structured prefixed format ("Character: Name - @Placeholder") and simple format ("Name - @Placeholder") with comprehensive debugging, enhanced entity mapping, and **structured TypeScript interfaces** with strict type safety (`Record<string, unknown>` instead of `any`) for type-safe entity processing
-   **Structured Entity Storage**: New JSON-based entity mapping system with dedicated database columns (`character_mappings`, `item_mappings`, `location_mappings`) for improved performance, type safety, and backward compatibility with legacy formats
-   **Image Generation**: Create scene images using Picsart, Gemini, or Imagen3 APIs with intelligent data handling, automatic database optimization, and enhanced security validation
-   **Advanced Text Processing**: Comprehensive utilities for JSON extraction from AI responses, audio duration estimation, script validation, and content parsing with robust error handling
-   **Video Assembly & Export**: Timeline-based video editor for arranging images, audio, and text into MP4 videos
-   **User Authentication**: Secure user accounts via Firebase Auth
-   **Hybrid Data Storage**: Baserow database + MinIO storage for cost-effective backend
-   **Smart Data Management**: Intelligent fallback system with dedicated columns for optimal performance and backward compatibility
-   **Multi-provider AI**: Support for multiple AI services with user-provided API keys

### 🚀 Latest Enhancements

#### Auto-Save Translation System
**Zero-Configuration Persistence**: All translation functions now automatically save results to Baserow without requiring code changes or manual save operations.

**Key Benefits**:
- **Seamless Integration**: Existing components work unchanged
- **Smart Story Matching**: Automatically finds and updates the correct story
- **Production Ready**: Enhanced error handling and comprehensive logging
- **Backward Compatible**: No breaking changes to existing APIs

#### Enhanced Entity Mapping
**Flexible Format Support**: Entity mapping now supports both structured prefixed format and simple format with comprehensive debugging and enhanced type safety.

**Key Benefits**:
- **Dual Pattern Matching**: Handles "Character: Name - @Placeholder" and "Name - @Placeholder" formats
- **Enhanced Type Safety**: Strict TypeScript typing with `Record<string, unknown>` instead of `any` for better code quality
- **Enhanced Debugging**: Detailed logging for successful mappings and parsing issues
- **Runtime Safety**: Prevents type-related errors during entity processing
- **Backward Compatibility**: Existing entity descriptions continue to work unchanged
- **Improved Reliability**: Better error handling and graceful degradation

**Supported Languages**:
- ✅ **Spanish** - `generateSpanishTranslation()` with auto-save
- ✅ **Romanian** - `generateRomanianTranslation()` with auto-save

## Style Guidelines

-   **Primary color**: Soft teal (`#A0E7E5`)
-   **Secondary color**: Light beige (`#E6D8B9`)
-   **Accent**: Coral (`#FF6F61`)
-   Clean, readable fonts and friendly icons.
-   Spacious layout with gentle transitions.

## Tech Stack

-   **Framework**: Next.js 15 with App Router
-   **Language**: TypeScript with strict mode enabled
-   **Styling**: Tailwind CSS with shadcn/ui components
-   **Authentication**: Firebase Auth (preserved from migration)
-   **Database**: Baserow (PostgreSQL-based, migrated from Firestore)
-   **Storage**: MinIO S3-compatible storage (migrated from Firebase Storage)
-   **AI Integration**: Genkit framework for AI flows
-   **Video Processing**: Remotion for video rendering and playback with advanced story processing integration
-   **Audio Processing**: fluent-ffmpeg for audio operations with intelligent duration estimation
-   **Story Processing**: Comprehensive utilities for character recognition, prompt transformation, and content validation
-   **State Management**: React Query (@tanstack/react-query)
-   **Model Context Protocol (MCP)**: Enhanced AI capabilities with external tools
-   **Development Environment**: VS Code with Kiro agent integration and enhanced AI features

## Available Commands

```bash
# Development server (runs on port 9002)
npm run dev

# AI development with Genkit
npm run genkit:dev
npm run genkit:watch

# Build and deployment
npm run build
npm run start

# Code quality
npm run lint
npm run typecheck

# Database operations
npm run setup:baserow
npm run test:baserow-auth
npm run check:baserow
npm run test:baserow

# Video rendering
npm run remotion:render
npm run remotion:render:fast
npm run remotion:benchmark
```

## Architecture Overview

StoryTailor uses a **hybrid architecture** combining cloud and self-hosted services:

- **Frontend**: Vercel-ready Next.js application
- **Authentication**: Firebase Auth (cloud)
- **Database**: Baserow (self-hosted PostgreSQL)
- **Storage**: MinIO (self-hosted S3-compatible)
- **Remote Access**: Cloudflare tunnels for global backend access

### Cost Benefits
- **30-55% cost reduction** compared to full Firebase stack
- **Data sovereignty** with self-hosted backend
- **Scalable** hybrid cloud architecture

## Troubleshooting

### Firebase Authentication Issues
- Ensure your `.env.local` file has correct Firebase credentials
- Verify Email/Password authentication is enabled in Firebase Console
- Check that Firebase project is active and properly configured

### Baserow Database Issues
- **Connection Failed**: Verify `BASEROW_API_URL` and `BASEROW_TOKEN` in environment
- **Table Not Found**: Ensure table IDs (696, 697) exist in your Baserow instance
- **Permission Denied**: Check API token has read/write access to required tables

### MinIO Storage Issues
- **Connection Failed**: Verify `MINIO_ENDPOINT`, `MINIO_ACCESS_KEY`, and `MINIO_SECRET_KEY`
- **Bucket Not Found**: Ensure `storytailor-media` bucket exists
- **Upload Failed**: Check MinIO service is running and accessible

### MCP Server Issues
- **Server Won't Start**: Verify `uvx` is installed (`uvx --version`)
- **Tools Not Available**: Check server is enabled in MCP configuration
- **API Errors**: Review server logs in Kiro's MCP panel
- **IDE Integration**: Ensure Kiro agent is enabled in VS Code settings (`kiroAgent.configureMCP: "Enabled"`)
- **AI Features**: Verify inline completion and codebase indexing are active

### Genkit AI Issues
- Ensure `GOOGLE_API_KEY` is set for Google AI integration
- Check Generative Language API is enabled in Google Cloud Console
- Review Genkit development server logs (`npm run genkit:watch`)

### Development Issues
- **Port 9002 in use**: Kill existing processes or change port in package.json
- **Build errors ignored**: TypeScript/ESLint errors are ignored during builds for faster iteration
- **Service account**: Ensure `GOOGLE_APPLICATION_CREDENTIALS` points to valid service account file
```