const { getStory } = require('./src/actions/baserowStoryActions');

async function debugStory() {
  // You'll need to replace these with actual values from your environment
  const storyId = 'story_1756752064012_7l3arvatq';
  // Replace with actual user ID that owns this story
  const userId = 'user-id-here'; 
  
  try {
    const result = await getStory(storyId, userId);
    if (result.success && result.data) {
      console.log('Story data:');
      console.log('Character mappings:', result.data.character_mappings);
      console.log('Item mappings:', result.data.item_mappings);
      console.log('Location mappings:', result.data.location_mappings);
      console.log('Details prompts:', result.data.detailsPrompts);
      
      // Let's also check the action prompts
      console.log('Action prompts:', result.data.actionPrompts);
      
      // Let's specifically look at scenes 4 and 5 mentioned in the issue
      if (result.data.actionPrompts && result.data.actionPrompts.length > 4) {
        console.log('Scene 4 action prompt:', result.data.actionPrompts[3]);
        console.log('Scene 5 action prompt:', result.data.actionPrompts[4]);
      }
    } else {
      console.error('Failed to get story:', result.error);
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

debugStory();