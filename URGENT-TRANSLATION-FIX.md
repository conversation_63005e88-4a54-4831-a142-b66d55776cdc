# URGENT: Translation Memory Leak Fix

## Issue
Both `generateSpanishTranslation` and `generateRomanianTranslation` functions in `src/actions/story/translationActions.ts` are missing `finally` blocks to clean up the `activeTranslations` Set, causing memory leaks.

## Required Fix

Add the following `finally` block to **BOTH** translation functions:

### Spanish Translation Function
Find the existing `try {` block around line 250 and add:

```typescript
} finally {
    // Clean up the active translation tracking
    activeTranslations.delete(translationKey);
}
```

### Romanian Translation Function  
Find the existing `try {` block around line 500 and add:

```typescript
} finally {
    // Clean up the active translation tracking
    activeTranslations.delete(translationKey);
}
```

## Complete Pattern
Each function should follow this pattern:

```typescript
export async function generateRomanianTranslation(input: GenerateRomanianTranslationInput) {
    // ... duplicate detection logic ...
    
    activeTranslations.add(translationKey);
    
    try {
        // ... existing translation logic ...
        return result;
    } finally {
        // CRITICAL: Clean up the active translation tracking
        activeTranslations.delete(translationKey);
    }
}
```

## Priority: URGENT
This fix is required before production deployment to prevent memory leaks and application instability.

## Testing
After applying the fix:
1. Start multiple identical translation requests
2. Verify they are properly rejected with duplicate message
3. Verify the `activeTranslations` Set is cleaned up after completion
4. Monitor memory usage over time to confirm no leaks