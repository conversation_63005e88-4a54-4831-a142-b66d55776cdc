--- a/src/app/(app)/create-story/page.tsx
+++ b/src/app/(app)/create-story/page.tsx
@@ -798,7 +798,7 @@
             <AccordionItem value="step-4" disabled={!storyData.narrationAudioUrl}>
               <AccordionTrigger className="text-xl font-semibold hover:no-underline data-[state=open]:text-primary">
                 <div className="flex items-center">
-                  <LucideImage className="w-6 h-6 mr-3" /> Step 4: Generate &amp; Edit Image Prompts
+                  <LucideImage className="w-6 h-6 mr-3" /> Step 4: Generate Image Prompts &amp; Assemble Video
                 </div>
               </AccordionTrigger>
               <AccordionContent className="pt-4 space-y-4">
@@ -852,193 +852,45 @@
                           </div>
                         ))}
                       </div>
+                    </div>
+                    )}
+                    
+                    {/* Assemble & Export Video Button */}
+                    {storyData.imagePrompts && storyData.imagePrompts.length > 0 && (
+                      <div className="mt-8">
+                        <Separator className="mb-6" />
+                        <Label className="block text-xl font-semibold mb-4">Assemble &amp; Export Video</Label>
+                        
+                        {storyData.id ? (
+                          <>
+                            <p className="text-muted-foreground mb-4">Your prompts are ready! You can now proceed to assemble your video.</p>
+                            <Button 
+                              asChild 
+                              className="bg-primary hover:bg-primary/90 text-primary-foreground"
+                              size="lg"
+                            >
+                              <Link href={`/assemble-video?storyId=${storyData.id}`}>
+                                <Film className="mr-2 h-5 w-5" /> Assemble &amp; Export Video
+                              </Link>
+                            </Button>
+                            <div className="flex items-center p-3 text-sm text-primary bg-primary/10 border border-primary/20 rounded-md mt-4">
+                              <Info className="h-5 w-5 mr-2 shrink-0" />
+                              <span>Video assembly and MP4 export will be handled on the next page. Some features might be under development.</span>
+                            </div>
+                          </>
+                        ) : (
+                          <div className="flex items-center p-3 text-sm text-yellow-700 bg-yellow-100 border border-yellow-200 rounded-md">
+                            <AlertCircle className="h-5 w-5 mr-2 shrink-0" />
+                            <span>Please save your story first to enable video assembly.</span>
+                          </div>
+                        )}
                       </div>
-                    )}
+                    )}
                   </div>
                 ): (
                      <p className="text-muted-foreground">Please generate or upload narration audio in Step 3 first.</p>
                 )}
               </AccordionContent>
             </AccordionItem>
-
-            {/* Step 5: Image Generation */}
-            <AccordionItem value="step-5" disabled={!(storyData.imagePrompts && storyData.imagePrompts.length > 0)}>
-              <AccordionTrigger className="text-xl font-semibold hover:no-underline data-[state=open]:text-primary">
-                <div className="flex items-center">
-                  <ImageIcon className="w-6 h-6 mr-3" /> Step 5: Generate Images
-                </div>
-              </AccordionTrigger>
-              <AccordionContent className="pt-4 space-y-4">
-                {(storyData.imagePrompts && storyData.imagePrompts.length > 0) ? (
-                  <div>
-                    <div className="flex justify-between items-center mb-2">
-                        <Label className="block text-md font-medium">Image Prompts ({storyData.imagePrompts.length} total)</Label>
-                        <Button 
-                            onClick={handleGenerateAllImages} 
-                            disabled={isLoading.allImages || canAssembleVideo || storyData.imagePrompts.every((_, idx) => isLoading[`image-${idx}`])} 
-                            variant="outline"
-                        >
-                            {isLoading.allImages ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Sparkles className="mr-2 h-4 w-4" />}
-                            Generate All Remaining Images
-                        </Button>
-                    </div>
-                    <div className="max-h-96 overflow-y-auto space-y-3 pr-2 rounded-md border p-3 bg-muted/20">
-                      {storyData.imagePrompts.map((prompt, index) => {
-                        const existingImage = storyData.generatedImages?.find(img => img && img.prompt === prompt);
-                        return (
-                          <div key={index} className="p-3 bg-card rounded-md shadow-sm border">
-                            <p className="text-sm text-muted-foreground"><strong>Prompt {index + 1}:</strong> {prompt}</p>
-                            {existingImage ? (
-                              <div className="mt-2 border rounded-md overflow-hidden w-40 h-40 relative">
-                                <Image src={existingImage.imageUrl} alt={`Generated image for prompt ${index + 1}`} layout="fill" objectFit="cover" data-ai-hint={existingImage.dataAiHint || "story scene"} />
-                                <CheckCircle className="absolute top-1 right-1 h-5 w-5 text-green-500 bg-white rounded-full p-0.5" />
-                              </div>
-                            ) : (
-                              <Button onClick={() => handleGenerateSingleImage(prompt, index)} size="sm" variant="secondary" className="mt-2" disabled={isLoading[`image-${index}`]}>
-                                {isLoading[`image-${index}`] ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <ImageIcon className="mr-2 h-4 w-4" />}
-                                Generate Image {index + 1}
-                              </Button>
-                            )}
-                          </div>
-                        );
-                      })}
-                    </div>
-                    {canAssembleVideo && (
-                       <div className="flex items-center p-3 mt-4 text-sm text-green-700 bg-green-100 border border-green-200 rounded-md">
-                         <CheckCircle className="h-5 w-5 mr-2 shrink-0" />
-                         <span>All images have been generated! Proceed to the next step.</span>
-                       </div>
-                    )}
-                  </div>
-                ) : (
-                     <p className="text-muted-foreground">Please generate image prompts in Step 4 first.</p>
-                )}
-              </AccordionContent>
-            </AccordionItem>
-
-            {/* Step 6: Video Assembly */}
-            <AccordionItem value="step-6" disabled={!canAssembleVideo}>
-              <AccordionTrigger className="text-xl font-semibold hover:no-underline data-[state=open]:text-primary">
-                <div className="flex items-center">
-                  <Clapperboard className="w-6 h-6 mr-3" /> Step 6: Assemble &amp; Export Video
-                </div>
-              </AccordionTrigger>
-              <AccordionContent className="pt-4 space-y-4">
-                 {!canAssembleVideo && <p className="text-muted-foreground">Please generate all images in Step 5 first.</p>}
-                {canAssembleVideo && storyData.id && (
-                  <>
-                    <p className="text-muted-foreground">All images are generated. You can now proceed to assemble your video.</p>
-                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mt-4">
-                      {storyData.generatedImages?.map((img, index) => (
-                        img && 
-                        <div key={index} className="border rounded-md overflow-hidden aspect-square relative group shadow-md">
-                          <Image src={img.imageUrl} alt={`Scene ${index + 1}`} layout="fill" objectFit="cover" data-ai-hint={img.dataAiHint || "animation frame"}/>
-                          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
-                            <p className="text-white text-xs p-1 text-center">{img.prompt.substring(0,50)}...</p>
-                          </div>
-                        </div>
-                      ))}
-                    </div>
-                    <Button asChild className="mt-4 bg-primary hover:bg-primary/90 text-primary-foreground" disabled={!canAssembleVideo || !storyData.id}>
-                      <Link href={`/assemble-video?storyId=${storyData.id}`}>
-                        <Film className="mr-2 h-4 w-4" /> Assemble &amp; Export Video
-                      </Link>
-                    </Button>
-                    <div className="flex items-center p-3 text-sm text-primary bg-primary/10 border border-primary/20 rounded-md mt-2">
-                      <Info className="h-5 w-5 mr-2 shrink-0" />
-                      <span>Video assembly and MP4 export will be handled on the next page. Some features might be under development.</span>
-                    </div>
-                  </>
-                )}
-                 {canAssembleVideo && !storyData.id && (
-                    <div className="flex items-center p-3 text-sm text-yellow-700 bg-yellow-100 border border-yellow-200 rounded-md">
-                        <AlertCircle className="h-5 w-5 mr-2 shrink-0" />
-                        <span>Please save your story first to enable video assembly.</span>
-                    </div>
-                 )}
-              </AccordionContent>
-            </AccordionItem>
           </Accordion>
