# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# turbo
.turbo

# typescript
tsconfig.tsbuildinfo

# sensitive files
.secure/

# generated media files
*.wav
*.mp3
*.mp4
*.jpeg
*.jpg
*.png
*.gif
*.webp

# temporary media directories
/public/remotion-assets/
/public/downloads/
/temp/


# Firebase service account credentials
storytailor-f089f-firebase-adminsdk-fbsvc-e5a2c1ca7e.json