const { baserowService } = require('./src/lib/baserow.ts');

async function findStory() {
  try {
    // Get all stories and find the one with our ID
    const stories = await baserowService.getStories();
    const targetStory = stories.find(story => story.firebase_story_id === 'story_1756752064012_7l3arvatq');
    
    if (targetStory) {
      console.log('Found story:');
      console.log('Story ID:', targetStory.id);
      console.log('Firebase Story ID:', targetStory.firebase_story_id);
      console.log('User ID:', targetStory.user_id);
      console.log('Character mappings:', targetStory.character_mappings);
      console.log('Item mappings:', targetStory.item_mappings);
      console.log('Location mappings:', targetStory.location_mappings);
      console.log('Details prompts:', targetStory.details_prompts);
      console.log('Action prompts:', targetStory.action_prompts);
      
      // Let's look at the specific scenes mentioned in the issue
      if (targetStory.action_prompts) {
        try {
          const actionPrompts = JSON.parse(targetStory.action_prompts);
          console.log('Parsed action prompts:', actionPrompts);
          
          if (actionPrompts.length > 4) {
            console.log('Scene 4 action prompt:', actionPrompts[3]);
            console.log('Scene 5 action prompt:', actionPrompts[4]);
          }
        } catch (parseError) {
          console.log('Action prompts (unparsed):', targetStory.action_prompts);
        }
      }
    } else {
      console.log('Story not found');
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

findStory();