{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "GOOGLE_APPLICATION_CREDENTIALS=./.secure/storytailor-f089f-2b63d889003f.json next dev -p 9002", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "setup:baserow": "tsx scripts/setup-baserow-schema.ts", "test:baserow-auth": "tsx scripts/test-baserow-auth.ts", "check:baserow": "tsx scripts/check-baserow-structure.ts", "test:baserow": "tsx scripts/test-baserow-connection.ts", "remotion:benchmark": "npx remotion benchmark", "remotion:render": "npx remotion render", "remotion:render:fast": "npx remotion render --concurrency=4 --log=verbose"}, "dependencies": {"@aws-sdk/client-s3": "^3.828.0", "@aws-sdk/s3-request-presigner": "^3.828.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@genkit-ai/flow": "^0.5.17", "@genkit-ai/googleai": "^1.8.0", "@genkit-ai/next": "^1.8.0", "@hookform/resolvers": "^4.1.3", "@opentelemetry/exporter-jaeger": "^2.0.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@remotion/bundler": "^4.0.311", "@remotion/cli": "^4.0.311", "@remotion/player": "^4.0.311", "@remotion/renderer": "^4.0.311", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.17.19", "@types/fluent-ffmpeg": "^2.1.27", "@types/jszip": "^3.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "firebase": "^11.7.0", "firebase-admin": "^13.4.0", "fluent-ffmpeg": "^2.1.3", "genkit": "^1.8.0", "jszip": "^3.10.1", "lucide-react": "^0.475.0", "minio": "^8.0.5", "next": "^15.3.2", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-video-editor": "file:../react-video-editor", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20.17.46", "@types/react": "^18", "@types/react-dom": "^18", "dotenv": "^16.5.0", "eslint": "9.27.0", "eslint-config-next": "15.3.2", "genkit-cli": "^1.8.0", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.20.3", "typescript": "^5"}, "description": "StoryTailor is a Next.js AI-powered animated story generator that creates complete video stories from text prompts. Features include script generation, multi-language narration, image generation, and video assembly. Uses hybrid architecture with Firebase Auth, Baserow database, MinIO storage, and MCP integration for enhanced AI capabilities.", "main": "index.js", "directories": {"doc": "docs"}, "keywords": [], "author": "", "license": "ISC"}