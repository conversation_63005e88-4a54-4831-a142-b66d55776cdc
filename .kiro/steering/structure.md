# Project Structure & Organization

## Directory Structure

```
src/
├── actions/           # Server actions for data operations
│   ├── baserowStoryActions.ts     # Baserow database operations
│   ├── baserowApiKeyActions.ts    # User API key management
│   ├── minioStorageActions.ts     # MinIO storage operations
│   └── storyActions.ts            # Main story generation logic
├── ai/                # AI integration and flows
│   ├── flows/         # Genkit AI flow definitions
│   └── genkit.ts      # Genkit configuration
├── app/               # Next.js App Router pages
│   ├── (app)/         # Authenticated app pages
│   │   ├── assemble-video/    # Video timeline editor
│   │   ├── create-story/      # Story creation wizard
│   │   └── dashboard/         # User dashboard
│   ├── (auth)/        # Authentication pages
│   └── api/           # API routes
├── components/        # React components
│   ├── ui/            # shadcn/ui components
│   ├── create-story/  # Story creation components
│   └── auth/          # Authentication components
├── lib/               # Utility libraries
│   ├── firebase.ts    # Firebase client config
│   ├── baserow.ts     # Baserow API client
│   └── minio.ts       # MinIO storage client
├── types/             # TypeScript type definitions
├── utils/             # Utility functions
└── constants/         # Application constants
```

## Code Organization Patterns

### Server Actions Pattern
- All data operations use Next.js server actions
- Actions are organized by data source (Baserow, MinIO, Firebase)
- Zod schemas for input/output validation
- Error handling with success/error response pattern

### AI Flow Pattern
- Genkit flows in `src/ai/flows/` with separate type definitions
- Each flow has input/output types and flow definition
- Server actions wrap AI flows for client consumption

### Component Structure
- Feature-based component organization
- UI components in `src/components/ui/` (shadcn/ui)
- Feature components in dedicated folders
- Shared components at root level

### Type Safety
- Comprehensive TypeScript types in `src/types/`
- Story interface as central data model
- Separate types for different AI providers and services

## File Naming Conventions

- **Components**: PascalCase (e.g., `StoryContent.tsx`)
- **Actions**: camelCase with descriptive suffixes (e.g., `baserowStoryActions.ts`)
- **Types**: camelCase with `.ts` extension
- **Utils**: camelCase describing functionality
- **Pages**: lowercase with Next.js conventions

## Import Patterns

- Use `@/` alias for src directory imports
- Group imports: external libraries, internal modules, types
- Server actions imported in client components for form actions
- AI flows imported only in server actions