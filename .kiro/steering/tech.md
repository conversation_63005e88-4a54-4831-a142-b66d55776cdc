# Technology Stack & Build System

## Core Technologies

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript with strict mode enabled
- **Styling**: Tailwind CSS with shadcn/ui components
- **Authentication**: Firebase Auth (preserved from migration)
- **Database**: Baserow (PostgreSQL-based, migrated from Firestore)
- **Storage**: MinIO S3-compatible storage (migrated from Firebase Storage)
- **AI Integration**: Genkit framework for AI flows

## Key Libraries & Dependencies

- **UI Components**: Radix UI primitives with shadcn/ui
- **Forms**: React Hook Form with Zod validation
- **Drag & Drop**: @dnd-kit for timeline interactions
- **Video**: Remotion for video rendering and playback
- **Audio**: fluent-ffmpeg for audio processing
- **State Management**: React Query (@tanstack/react-query)
- **File Handling**: JSZip for archive operations

## Development Commands

```bash
# Development server (runs on port 9002)
npm run dev

# AI development with Genkit
npm run genkit:dev
npm run genkit:watch

# Build and deployment
npm run build
npm run start

# Code quality
npm run lint
npm run typecheck

# Database operations
npm run setup:baserow
npm run test:baserow-auth
npm run check:baserow
npm run test:baserow

# Video rendering
npm run remotion:render
npm run remotion:render:fast
npm run remotion:benchmark
```

## Architecture Notes

- **Hybrid Architecture**: Vercel frontend + self-hosted Raspberry Pi backend
- **Remote Access**: Cloudflare tunnels for global backend access
- **Environment**: Uses GOOGLE_APPLICATION_CREDENTIALS for service account
- **Build Config**: TypeScript and ESLint errors ignored during builds for faster iteration
- **Image Optimization**: Next.js image optimization with remote patterns for placeholder services