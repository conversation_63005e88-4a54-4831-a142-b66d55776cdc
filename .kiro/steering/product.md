# StoryTailor Product Overview

StoryTailor is an AI-powered animated story generator that allows users to create complete video stories from simple text prompts. The application generates scripts, narration audio, image prompts, and assembles them into animated videos.

## Core Features

- **AI Script Generation**: Generate video scripts from user prompts using Google AI or Perplexity
- **Multi-language Narration**: Create MP3 narration in English, Spanish, and Romanian using ElevenLabs or Google TTS
- **Character & Scene Prompts**: Auto-generate detailed character, item, and location descriptions
- **Image Generation**: Create scene images using Picsart, Gemini, or Imagen3 APIs
- **Video Assembly**: Timeline-based video editor for arranging images, audio, and text
- **User Authentication**: Secure user accounts via Firebase Auth
- **Multi-provider AI**: Support for Google AI, Perplexity, ElevenLabs, and multiple image generation services

## Target Users

- Content creators looking to generate animated stories quickly
- Educators creating visual storytelling content
- Parents wanting to create personalized stories for children
- Small businesses needing video content without extensive production resources

## Business Model

- User-provided API keys for AI services (cost-efficient for users)
- Self-hosted backend with hybrid cloud architecture
- Migrated from Firebase to Baserow + MinIO for cost reduction (30-55% savings)