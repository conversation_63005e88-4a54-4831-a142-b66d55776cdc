{"enabled": true, "name": "Documentation Sync", "description": "Listens to TypeScript source files, configuration files, and other relevant project files. When changes are detected, automatically updates documentation in README.md and the docs/ folder to keep documentation in sync with code changes.", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.ts", "src/**/*.tsx", "*.ts", "*.js", "*.json", "package.json", "tsconfig.json", "next.config.ts", "remotion.config.ts", "tailwind.config.ts", "components.json"]}, "then": {"type": "askAgent", "prompt": "Source code files have been modified in this TypeScript/Next.js project. Please review the changes and update the documentation accordingly. Focus on:\n\n1. Update README.md with any new features, API changes, or setup instructions\n2. Update relevant files in the docs/ folder (blueprint.md, video-rendering.md, etc.) \n3. Ensure documentation reflects current code structure and functionality\n4. Add or update code examples if new components or utilities were added\n5. Update any configuration or setup instructions if config files changed\n\nPlease analyze the changed files and provide comprehensive documentation updates that help users understand the current state of the project."}}