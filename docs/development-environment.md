# Development Environment Setup

## Overview

StoryTailor is optimized for development with VS Code and the Kiro AI agent, providing enhanced AI capabilities, intelligent code completion, and seamless integration with the Model Context Protocol (MCP) ecosystem.

## IDE Configuration

### VS Code Settings

The project includes optimized VS Code settings for the best development experience:

**File**: `.vscode/settings.json`
```json
{
    "IDX.aI.enableInlineCompletion": true,
    "IDX.aI.enableCodebaseIndexing": true,
    "kiroAgent.configureMCP": "Enabled",
    "typescript.autoClosingTags": false
}
```

### Configuration Benefits

#### AI-Powered Development
- **Inline Completion**: AI-powered code suggestions as you type
- **Codebase Indexing**: Intelligent understanding of your entire project structure
- **Context Awareness**: AI suggestions based on project patterns and conventions

#### Kiro Agent Integration
- **MCP Support**: Full Model Context Protocol integration
- **External Tools**: Access to web search, documentation, and research capabilities
- **Enhanced AI**: Improved story generation with external context and validation

#### TypeScript Optimization
- **Auto-closing Tags**: Disabled for better control over JSX/TSX development
- **Strict Mode**: Enhanced type checking for better code quality
- **Fast Iteration**: Build errors ignored during development for faster feedback

## Model Context Protocol (MCP)

### Current Configuration Status: ACTIVE ✅

**Configuration Hierarchy**:
1. **User-level**: `~/.kiro/settings/mcp.json` (global configuration)
2. **Workspace-level**: `.kiro/settings/mcp.json` (project-specific overrides)
3. **Legacy**: `.kilocode/mcp.json` and `mcp_settings.json` (backward compatibility)

### Active MCP Servers

#### Fetch Server ✅
- **Purpose**: HTTP requests and external data retrieval
- **Auto-approved Tools**: Web content fetching, API calls
- **Benefits**: Real-time research for story content, fact-checking, inspiration gathering

#### Context7 Server ✅
- **Purpose**: Enhanced context understanding and processing
- **Benefits**: Better narrative coherence, improved story flow, character consistency

#### Brave Search (Legacy)
- **Purpose**: Web search capabilities
- **Status**: Available in legacy configurations
- **Benefits**: Research and content discovery

### MCP Benefits for StoryTailor Development

#### Story Generation Enhancement
- **Research Integration**: Fetch external content for story inspiration
- **Fact Checking**: Validate story elements against real-world information
- **Cultural Sensitivity**: Check content appropriateness across different cultures
- **Trend Analysis**: Incorporate current trends and popular themes

#### Development Productivity
- **Code Context**: Better understanding of project structure and patterns
- **API Documentation**: Real-time access to external API documentation
- **Best Practices**: AI suggestions based on current industry standards
- **Debugging Support**: Enhanced error analysis and solution suggestions

## Development Workflow

### Starting Development

```bash
# 1. Start the development server
npm run dev

# 2. Start Genkit AI development (optional)
npm run genkit:watch

# 3. Open VS Code with Kiro agent
code .
```

### AI-Enhanced Development Features

#### Intelligent Code Completion
- **Context-aware suggestions** based on your project structure
- **Pattern recognition** for consistent coding style
- **API integration** suggestions for external services

#### Codebase Understanding
- **Project indexing** for better AI context
- **Cross-file references** and dependency analysis
- **Architecture awareness** for better suggestions

#### MCP-Powered Research
- **Story research** directly in the IDE
- **API documentation** lookup
- **Best practices** and examples

### Recommended Extensions

#### Essential Extensions
- **Kiro**: AI-powered development assistant (required)
- **TypeScript**: Enhanced TypeScript support
- **Tailwind CSS IntelliSense**: CSS class suggestions
- **ES7+ React/Redux/React-Native snippets**: React development shortcuts

#### Optional Extensions
- **Prettier**: Code formatting
- **ESLint**: Code linting and quality
- **GitLens**: Enhanced Git integration
- **Thunder Client**: API testing within VS Code

## Configuration Management

### Environment Variables

**Development**: `.env.local`
```env
# Core services
BASEROW_API_URL="https://baserow.holoanima.com/api"
MINIO_ENDPOINT="https://minio-api.holoanima.com"

# AI services (user-provided)
GOOGLE_API_KEY="your-key-here"

# Development features
NODE_ENV="development"
NEXT_PUBLIC_DEV_MODE="true"
```

### MCP Server Configuration

**User-level**: `~/.kiro/settings/mcp.json`
```json
{
  "mcpServers": {
    "fetch": {
      "command": "uvx",
      "args": ["mcp-server-fetch@latest"],
      "disabled": false,
      "autoApprove": ["fetch"]
    },
    "context7": {
      "command": "uvx", 
      "args": ["context7-mcp@latest"],
      "disabled": false,
      "autoApprove": []
    }
  }
}
```

### Project-specific MCP

**Workspace-level**: `.kiro/settings/mcp.json` (optional)
```json
{
  "mcpServers": {
    "storytailor-tools": {
      "command": "uvx",
      "args": ["storytailor-mcp-tools@latest"],
      "env": {
        "BASEROW_TOKEN": "project-specific-token"
      },
      "disabled": false,
      "autoApprove": ["story-research", "content-validation"]
    }
  }
}
```

## Debugging and Troubleshooting

### Common Issues

#### MCP Server Problems
```bash
# Check if uvx is installed
uvx --version

# Test MCP server manually
uvx mcp-server-fetch@latest

# Check Kiro agent status
# Use VS Code command palette: "Kiro: Check MCP Status"
```

#### AI Features Not Working
1. **Check Kiro agent**: Ensure extension is installed and enabled
2. **Verify settings**: Confirm `kiroAgent.configureMCP: "Enabled"`
3. **Restart VS Code**: Sometimes required after configuration changes
4. **Check logs**: View Kiro agent logs in VS Code output panel

#### Development Server Issues
```bash
# Clear Next.js cache
rm -rf .next

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Check port availability (default: 9002)
lsof -i :9002
```

### Performance Optimization

#### VS Code Performance
- **Exclude large directories** from indexing (node_modules, .next)
- **Limit AI features** if experiencing slowdowns
- **Use workspace-specific settings** for project customization

#### Development Server
- **Fast refresh**: Enabled by default in Next.js 15
- **TypeScript checking**: Runs in separate process
- **Hot reloading**: Automatic for most file changes

## Testing and Quality Assurance

### Code Quality Tools

```bash
# TypeScript checking
npm run typecheck

# ESLint checking
npm run lint

# Build verification
npm run build
```

### AI-Assisted Testing
- **Test generation**: AI suggestions for test cases
- **Bug detection**: Enhanced error analysis
- **Code review**: AI-powered code quality suggestions

## Deployment Preparation

### Pre-deployment Checklist
- [ ] All environment variables configured
- [ ] TypeScript compilation successful
- [ ] ESLint checks passing
- [ ] MCP servers tested and working
- [ ] Build process completed successfully

### Production Considerations
- **MCP servers**: May need different configuration for production
- **AI features**: Consider rate limits and API costs
- **Performance**: Monitor AI feature impact on build times

## Future Enhancements

### Planned IDE Features
- **Custom MCP servers** for StoryTailor-specific tools
- **Enhanced debugging** with AI-powered error analysis
- **Automated testing** with AI-generated test cases
- **Performance monitoring** integrated into development workflow

### AI Development Tools
- **Story template generation** with AI assistance
- **Character consistency checking** across story elements
- **Automated content validation** for different audiences
- **Multi-language support** with AI-powered translation validation

## Support and Resources

### Documentation
- **Kiro Agent**: Official documentation and guides
- **MCP Protocol**: Model Context Protocol specification
- **VS Code**: Official VS Code documentation
- **Next.js**: Framework documentation and best practices

### Community
- **GitHub Issues**: Project-specific problems and feature requests
- **Kiro Community**: AI development best practices and tips
- **MCP Ecosystem**: Available servers and tools

---

*The StoryTailor development environment is optimized for AI-enhanced productivity, providing developers with intelligent assistance throughout the entire development lifecycle.*