# Model Context Protocol (MCP) Integration

StoryTailor integrates with Model Context Protocol (MCP) servers to enhance AI capabilities with external tools and data sources. This enables more sophisticated story generation, research capabilities, and content enhancement.

## Overview

MCP allows <PERSON><PERSON>ailor to connect with various external services and tools through standardized protocols, expanding the AI's capabilities beyond basic text generation.

## Recent Changes

**Latest Update**: MC<PERSON> has been **ENABLED** in VSCode settings (`kiroAgent.configureMCP: "Enabled"`). The fetch server (`mcp-server-fetch`) is active with auto-approval in the user-level MCP configuration, providing seamless HTTP request capabilities for enhanced story generation and research.

## Current Configuration

### Active MCP Servers

#### Fetch Server (`mcp-server-fetch`)
- **Status**: Enabled with auto-approval
- **Purpose**: HTTP request capabilities for external data retrieval
- **Capabilities**: Web scraping, API calls, content fetching
- **Usage**: Research and fact-checking for story content, external API integration
- **Auto-approved tools**: `fetch`

#### Context7 (`@upstash/context7-mcp`)
- **Status**: Enabled
- **Purpose**: Enhanced context understanding and processing
- **Capabilities**: Improved narrative coherence and character consistency
- **Usage**: Automatically enhances story generation flows

### Legacy Configurations

The project maintains multiple legacy MCP configurations for compatibility:
- **`.kilocode/mcp.json`**: Contains context7 and brave-search configurations
- **`mcp_settings.json`**: Legacy brave-search configuration
- **Brave Search**: Web search capabilities for research and fact-checking

## Configuration Files

### User-Level Configuration
**Location**: `~/.kiro/settings/mcp.json`
**Scope**: Global across all projects

```json
{
  "mcpServers": {
    "fetch": {
      "command": "uvx",
      "args": ["mcp-server-fetch"],
      "env": {},
      "disabled": false,
      "autoApprove": ["fetch"]
    },
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"],
      "env": {},
      "disabled": false,
      "autoApprove": []
    }
  }
}
```

### Workspace-Level Configurations
**Location**: `.kilocode/mcp.json`
**Scope**: Project-specific legacy configuration

```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp"],
      "env": {
        "DEFAULT_MINIMUM_TOKENS": ""
      }
    },
    "brave-search": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-brave-search"],
      "env": {
        "BRAVE_API_KEY": "BSA3djWdF_zTNp2cCSbM8hPAVKf2G2y"
      }
    }
  }
}
```

### Legacy Configuration Files
**Location**: `mcp_settings.json`
**Scope**: Legacy configuration maintained for compatibility

```json
{
  "servers": {
    "github.com/modelcontextprotocol/servers/tree/main/src/brave-search": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-brave-search"],
      "env": {
        "BRAVE_API_KEY": "BSA3djWdF_zTNp2cCSbM8hPAVKf2G2y"
      }
    }
  }
}
```

## Available MCP Servers for StoryTailor

### Recommended Servers

1. **AWS Documentation** (`awslabs.aws-documentation-mcp-server`)
   - Useful for cloud deployment documentation
   - Configuration:
   ```json
   "aws-docs": {
     "command": "uvx",
     "args": ["awslabs.aws-documentation-mcp-server@latest"],
     "env": {
       "FASTMCP_LOG_LEVEL": "ERROR"
     },
     "disabled": false,
     "autoApprove": []
   }
   ```

2. **File System** (`@modelcontextprotocol/server-filesystem`)
   - Enhanced file operations
   - Useful for managing story assets and media files

3. **Web Search** (`@modelcontextprotocol/server-brave-search`)
   - Research capabilities for story content
   - Requires Brave Search API key

## Integration with StoryTailor Features

### Story Generation Enhancement
- **Context7** improves narrative coherence across story chunks
- Maintains character consistency throughout generated content
- Enhances dialogue and scene transitions

### Research Capabilities (Now Active with MCP Enabled)
- **Fetch server** actively retrieves reference material for story themes, character inspiration, and cultural context
- **Real-time Content**: Access current events, trending topics, or seasonal themes for story relevance
- **Educational Content**: Fetch educational resources for fact-checking and accuracy in educational stories
- **Visual References**: Retrieve image references and style guides for consistent character and scene generation
- **Brave Search** provides comprehensive fact-checking for educational stories (available in legacy configs)
- **API Integration**: Connect with external story databases, writing prompts, or creative resources

### Content Validation
- MCP servers can validate generated content for accuracy
- Cross-reference story elements with external sources
- Ensure cultural sensitivity and appropriateness

## Setup Instructions

### Prerequisites
1. Install `uv` and `uvx`:
   ```bash
   # macOS with Homebrew
   brew install uv
   
   # Alternative installation methods:
   # https://docs.astral.sh/uv/getting-started/installation/
   ```

### Adding New MCP Servers

1. **Edit Configuration File**:
   - User-level: `~/.kiro/settings/mcp.json`
   - Workspace-level: `.kiro/settings/mcp.json`

2. **Add Server Configuration**:
   ```json
   {
     "mcpServers": {
       "your-server-name": {
         "command": "uvx",
         "args": ["package-name@latest"],
         "env": {
           "API_KEY": "your-api-key-if-needed"
         },
         "disabled": false,
         "autoApprove": ["tool1", "tool2"]
       }
     }
   }
   ```

3. **Server Auto-Reconnection**:
   - Servers automatically reconnect on configuration changes
   - No need to restart the application

### Testing MCP Integration

Use Kiro's command palette to:
1. Search for "MCP" commands
2. View MCP server status
3. Test individual MCP tools
4. Manage server connections

## Best Practices

### Security
- Only enable servers you actively use
- Use `autoApprove` sparingly and only for trusted tools
- Keep API keys in environment variables when possible

### Performance
- Disable unused servers to reduce resource usage
- Monitor server logs for performance issues
- Use appropriate log levels (`ERROR` for production)

### Development
- Test MCP tools individually before integrating into workflows
- Use workspace-level configs for project-specific servers
- Keep user-level configs for commonly used servers

## Troubleshooting

### Common Issues

1. **Server Won't Start**:
   - Verify `uvx` is installed and accessible
   - Check package name and version
   - Review server logs in Kiro's MCP panel

2. **API Key Issues**:
   - Ensure environment variables are properly set
   - Check API key permissions and quotas
   - Verify key format matches server requirements

3. **Tool Not Available**:
   - Confirm server is enabled (`"disabled": false`)
   - Check if tool is in `autoApprove` list if needed
   - Restart server connection if necessary

### Debug Commands

```bash
# Test uvx installation
uvx --version

# Test specific MCP server
uvx context-7-mcp --help

# Check server logs
# (Available in Kiro's MCP Server view)
```

## Future Enhancements

### Planned Integrations
- **Image Generation APIs**: Direct integration with Picsart, DALL-E
- **Audio Processing**: Enhanced narration and voice synthesis
- **Translation Services**: Multi-language story support
- **Content Moderation**: Automated content safety checks

### Custom MCP Servers
- StoryTailor-specific MCP server for story operations
- Integration with Baserow database through MCP
- MinIO storage operations via MCP protocol