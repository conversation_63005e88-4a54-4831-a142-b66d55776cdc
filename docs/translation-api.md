# Translation API Documentation

## Overview

StoryTailor provides comprehensive multi-language translation capabilities with **built-in automatic database persistence**. All translation functions now automatically save results to Baserow, eliminating the need for manual save operations.

## API Functions

### Complete Workflow Functions (Recommended)

#### `generateAndSaveSpanishTranslation()`

Generates Spanish translation and automatically saves to Baserow database.

```typescript
export async function generateAndSaveSpanishTranslation(
    storyId: string,
    input: GenerateSpanishTranslationInput
): Promise<{ 
    success: boolean, 
    data?: z.infer<typeof AISpanishTranslationOutputSchema>, 
    error?: string 
}>
```

**Parameters**:
- `storyId`: Unique identifier for the story
- `input`: Translation input containing userId, chunks, and API key

**Workflow Steps**:
1. Generate Spanish translation using AI
2. Retrieve current story from Baserow
3. Update story with Spanish narration chunks
4. Save updated story to Baserow
5. Return translation result

**Usage Example**:
```typescript
// Simple usage - auto-save happens automatically
const result = await generateSpanishTranslation({
  userId: user.uid,
  chunks: story.narrationChunks,
  aiProvider: 'google'
});

if (result.success) {
  console.log('Spanish translation completed and auto-saved');
  // Translation is already saved to Baserow
} else {
  console.error('Translation failed:', result.error);
}
```

**Alternative with Explicit Story ID**:
```typescript
const result = await generateAndSaveSpanishTranslation(storyId, {
  userId: user.uid,
  chunks: story.narrationChunks,
  aiProvider: 'google'
});
```

#### `generateAndSaveRomanianTranslation()`

Generates Romanian translation and automatically saves to Baserow database.

```typescript
export async function generateAndSaveRomanianTranslation(
    storyId: string,
    input: GenerateRomanianTranslationInput
): Promise<{ 
    success: boolean, 
    data?: z.infer<typeof AIRomanianTranslationOutputSchema>, 
    error?: string 
}>
```

**Parameters**:
- `storyId`: Unique identifier for the story
- `input`: Translation input containing userId, chunks, and API key

**Workflow**: Same as Spanish translation but for Romanian language.

### Auto-Saving Translation Functions (Primary)

#### `generateSpanishTranslation()`

Generates Spanish translation with **automatic Baserow persistence**.

```typescript
export async function generateSpanishTranslation(
    input: GenerateSpanishTranslationInput
): Promise<{ 
    success: boolean, 
    data?: z.infer<typeof AISpanishTranslationOutputSchema>, 
    error?: string 
}>
```

**Auto-Save Features**:
- Automatically finds matching story by content analysis
- Updates story with Spanish translation chunks
- Saves to Baserow without additional function calls
- Works across all AI providers (Google, Perplexity)
- Handles both schema validation and text parsing workflows

#### `generateRomanianTranslation()`

Generates Romanian translation with **automatic Baserow persistence**.

```typescript
export async function generateRomanianTranslation(
    input: GenerateRomanianTranslationInput
): Promise<{ 
    success: boolean, 
    data?: z.infer<typeof AIRomanianTranslationOutputSchema>, 
    error?: string 
}>
```

**Auto-Save Features**: Same as Spanish translation but for Romanian language.

## Input Types

### `GenerateSpanishTranslationInput`

```typescript
interface GenerateSpanishTranslationInput {
  userId: string;
  chunks: NarrationChunk[];
  apiKey: string;
}
```

### `GenerateRomanianTranslationInput`

```typescript
interface GenerateRomanianTranslationInput {
  userId: string;
  chunks: NarrationChunk[];
  apiKey: string;
}
```

### `NarrationChunk`

```typescript
interface NarrationChunk {
  id: string;
  text: string;
  audioUrl?: string;
  duration?: number;
}
```

## Output Schemas

### Spanish Translation Output

```typescript
const AISpanishTranslationOutputSchema = z.object({
  spanishChunks: z.array(z.object({
    id: z.string(),
    text: z.string(),
    audioUrl: z.string().optional(),
    duration: z.number().optional()
  }))
});
```

### Romanian Translation Output

```typescript
const AIRomanianTranslationOutputSchema = z.object({
  romanianChunks: z.array(z.object({
    id: z.string(),
    text: z.string(),
    audioUrl: z.string().optional(),
    duration: z.number().optional()
  }))
});
```

## Error Handling

All translation functions return a standardized response format:

```typescript
interface TranslationResponse {
  success: boolean;
  data?: TranslationData;
  error?: string;
}
```

### Common Error Scenarios

1. **Translation Generation Failed**:
   ```typescript
   {
     success: false,
     error: "AI translation service unavailable"
   }
   ```

2. **Story Retrieval Failed**:
   ```typescript
   {
     success: false,
     error: "Failed to get story for saving: Story not found"
   }
   ```

3. **Database Save Failed**:
   ```typescript
   {
     success: false,
     error: "Translation completed but failed to save: Database connection error"
   }
   ```

## Best Practices

### 1. Use Auto-Saving Translation Functions

**Recommended (Auto-Save)**:
```typescript
// Automatic persistence - no manual save needed
const result = await generateSpanishTranslation(input);
// Translation is automatically saved to Baserow
```

**Alternative (Explicit Workflow)**:
```typescript
// When you have the story ID available
const result = await generateAndSaveSpanishTranslation(storyId, input);
```

**Avoid (Manual Workflow)**:
```typescript
// No longer necessary - auto-save handles this
const translation = await generateSpanishTranslation(input);
const story = await getStory(storyId, userId);
const updatedStory = { ...story, spanishNarrationChunks: translation.data.spanishChunks };
await saveStory(updatedStory, userId);
```

### 2. Handle Errors Gracefully

```typescript
const result = await generateSpanishTranslation(input);

if (!result.success) {
  // Show user-friendly error message
  toast.error(`Translation failed: ${result.error}`);
  return;
}

// Continue with success flow - translation is already saved
toast.success('Spanish translation completed and saved successfully');
```

### 3. Provide User Feedback

```typescript
// Show loading state
setIsTranslating(true);

try {
  const result = await generateSpanishTranslation(input);
  
  if (result.success) {
    // Update UI with new translation
    // Database is automatically updated by auto-save
    setStory(prev => ({
      ...prev,
      spanishNarrationChunks: result.data.spanishChunks
    }));
  }
} finally {
  setIsTranslating(false);
}
```

## Performance Considerations

### Request Deduplication

The translation system includes request ID tracking to prevent duplicate processing:

```typescript
// Automatic request ID generation
const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
console.log(`[generateSpanishTranslation] Request ID: ${requestId}`);
```

### Client-Side Protection

Implement UI guards to prevent concurrent translation requests:

```typescript
// Prevent multiple simultaneous translations
if (isTranslating) {
  console.log('Translation already in progress, ignoring duplicate call');
  return;
}

// Disable UI elements during translation
<Button disabled={isTranslating || !canTranslate}>
  {isTranslating ? 'Translating...' : 'Generate Spanish Translation'}
</Button>
```

## Monitoring and Debugging

### Log Patterns

**Successful Spanish Translation with Auto-Save**:
```
[generateSpanishTranslation] Schema validation successful, 15 chunks translated
[autoSaveTranslationToBaserow] Attempting to save spanish translation for user user_123
[autoSaveTranslationToBaserow] Found matching story: story_456
[autoSaveTranslationToBaserow] Successfully saved spanish translation to Baserow for story story_456
```

**Translation Success but Auto-Save Warning**:
```
[generateSpanishTranslation] Text parsing successful, 15 chunks translated
[autoSaveTranslationToBaserow] Could not find matching story for spanish translation
```

**Failed Translation**:
```
[generateSpanishTranslation] All Google AI models failed. Last error: Network/quota error
```

### Request Tracking

Each translation request generates a unique ID for debugging:
```
[generateSpanishTranslation] Request ID: req_1704067200000_abc123def
```

## Migration Notes

### Auto-Save Integration

The core translation functions now include automatic persistence:

**Current (Auto-Save Enabled)**:
```typescript
const result = await generateSpanishTranslation(input);
// Translation automatically saved to Baserow
```

**Alternative (Explicit Workflow)**:
```typescript
const result = await generateAndSaveSpanishTranslation(storyId, input);
// Explicit workflow when story ID is known
```

**No Migration Required**: Existing code using `generateSpanishTranslation()` and `generateRomanianTranslation()` now automatically benefits from database persistence.

### Database Schema

The translation system expects the following Baserow table structure:

**Stories Table (ID: 696)**:
- `spanishNarrationChunks`: JSON field for Spanish translations
- `romanianNarrationChunks`: JSON field for Romanian translations

## Related Documentation

- [Translation Performance](./translation-performance.md) - Performance optimization and troubleshooting
- [Blueprint](./blueprint.md) - Overall application architecture
- [Data Handling Improvements](./data-handling-improvements.md) - Database optimization details