# Data Formatting Standards

## Overview

StoryTailor implements consistent data formatting standards across all database operations to ensure data integrity, compatibility, and optimal performance with Baserow (PostgreSQL-based) backend.

## Date Formatting Standard

### Format: YYYY-MM-DD (ISO Date Only)

All date fields in the Baserow database use the standardized **YYYY-MM-DD** format for optimal compatibility and performance.

**Implementation Pattern**:
```typescript
// Standard date formatting across all operations
updated_at: new Date().toISOString().split('T')[0] // Format as YYYY-MM-DD
```

**Example Output**: `2025-01-09`

### Why This Format?

1. **Database Compatibility**: PostgreSQL date fields work optimally with ISO date format
2. **Timezone Independence**: Date-only format avoids timezone conversion issues
3. **Consistent Sorting**: Lexicographic sorting matches chronological sorting
4. **Storage Efficiency**: Smaller storage footprint compared to full ISO timestamps
5. **Query Performance**: Faster date range queries and indexing

### Applied Fields

This formatting standard is consistently applied across all date fields:

#### Stories Table (ID: 696)
- `created_at`: Story creation date
- `updated_at`: Last modification date

#### User API Keys Table (ID: 697)
- `created_at`: API key creation date
- `last_used`: Last usage timestamp (date only)

### Implementation Examples

#### Story Creation
```typescript
const storyData = {
  title: story.title,
  content: story.userPrompt,
  created_at: story.createdAt instanceof Date 
    ? story.createdAt.toISOString().split('T')[0] 
    : new Date().toISOString().split('T')[0],
  updated_at: new Date().toISOString().split('T')[0]
};
```

#### Story Updates
```typescript
const updates = {
  image_prompts: JSON.stringify(imagePrompts),
  action_prompts: JSON.stringify(actionPrompts),
  updated_at: new Date().toISOString().split('T')[0] // Format as YYYY-MM-DD
};
```

#### API Key Management
```typescript
const apiKeyData = {
  user_id: userId,
  api_key_hash: JSON.stringify(apiKeys),
  created_at: new Date().toISOString().split('T')[0],
  last_used: new Date().toISOString().split('T')[0]
};
```

#### Timeline Updates
```typescript
const updates = {
  timeline_tracks: JSON.stringify(timelineTracks),
  updated_at: new Date().toISOString().split('T')[0]
};
```

### Migration Considerations

#### From Full ISO Timestamps
If migrating from systems using full ISO timestamps (`2025-01-09T14:30:00.000Z`), the conversion is straightforward:

```typescript
// Before (full timestamp)
updated_at: new Date().toISOString()

// After (date only)
updated_at: new Date().toISOString().split('T')[0]
```

#### Backward Compatibility
The date-only format maintains backward compatibility:
- **Reading**: Both formats can be parsed by JavaScript `Date()` constructor
- **Sorting**: Chronological order is preserved
- **Queries**: Date range queries work with both formats

### Performance Benefits

#### Database Performance
- **Smaller Index Size**: Date-only fields create more compact indexes
- **Faster Queries**: Date comparisons are more efficient
- **Reduced Storage**: Approximately 50% less storage per date field

#### Application Performance
- **Consistent Parsing**: Eliminates timezone-related parsing issues
- **Predictable Sorting**: Consistent sort behavior across environments
- **Cache Efficiency**: More predictable cache keys for date-based queries

### Testing and Validation

#### Unit Tests
```typescript
describe('Date Formatting', () => {
  it('should format dates consistently', () => {
    const testDate = new Date('2025-01-09T14:30:00.000Z');
    const formatted = testDate.toISOString().split('T')[0];
    expect(formatted).toBe('2025-01-09');
  });
});
```

#### Database Validation
```sql
-- Verify date format in Baserow
SELECT created_at, updated_at 
FROM stories 
WHERE created_at ~ '^\d{4}-\d{2}-\d{2}$';
```

### Error Handling

#### Invalid Date Handling
```typescript
function formatDate(date: Date | string | undefined): string {
  try {
    const dateObj = date instanceof Date ? date : new Date(date || Date.now());
    return dateObj.toISOString().split('T')[0];
  } catch (error) {
    console.warn('Invalid date provided, using current date:', error);
    return new Date().toISOString().split('T')[0];
  }
}
```

#### Validation Schema
```typescript
import { z } from 'zod';

const DateOnlySchema = z.string().regex(
  /^\d{4}-\d{2}-\d{2}$/,
  'Date must be in YYYY-MM-DD format'
);
```

### Best Practices

#### 1. Always Use the Standard Format
```typescript
// ✅ Correct
updated_at: new Date().toISOString().split('T')[0]

// ❌ Avoid
updated_at: new Date().toISOString()
updated_at: new Date().toDateString()
updated_at: Date.now()
```

#### 2. Handle Edge Cases
```typescript
// Handle existing Date objects
const formatStoryDate = (date: Date | string | undefined) => {
  if (date instanceof Date) {
    return date.toISOString().split('T')[0];
  }
  if (typeof date === 'string' && date.includes('T')) {
    return date.split('T')[0]; // Extract date part from ISO string
  }
  return new Date().toISOString().split('T')[0]; // Default to current date
};
```

#### 3. Consistent Application
- Apply the same formatting across all database operations
- Use helper functions for complex date handling
- Document any exceptions to the standard format

### Related Documentation

- **[Security Enhancements](./security-enhancements.md)** - Security implementation with standardized date formatting
- **[Data Handling Improvements](./data-handling-improvements.md)** - Database optimization and data integrity
- **[Blueprint](./blueprint.md)** - Overall application architecture

### Future Considerations

#### Potential Enhancements
1. **Timezone Support**: Add timezone-aware date handling if needed
2. **Date Utilities**: Create centralized date formatting utilities
3. **Validation**: Enhanced date validation with custom Zod schemas
4. **Internationalization**: Locale-specific date display (while maintaining storage format)

### Conclusion

The standardized YYYY-MM-DD date format provides:
- **Consistency** across all database operations
- **Performance** optimization for PostgreSQL backend
- **Reliability** with predictable behavior
- **Maintainability** with clear formatting standards

This standard ensures data integrity and optimal performance while maintaining backward compatibility and ease of development.