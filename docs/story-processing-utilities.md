# Story Processing Utilities

## Overview

StoryTailor includes a comprehensive set of story processing utilities in `src/actions/utils/storyHelpers.ts` that handle character recognition, prompt transformation, content parsing, and data validation. These utilities are essential for converting user-generated content into AI-optimized formats for image generation, audio processing, and story assembly.

## Types and Interfaces

### Entity Mapping Types

StoryTailor includes structured TypeScript interfaces for entity mapping operations:

```typescript
export interface EntityMapping {
    name: string;
    type: string;
    description: string;
}

export interface StructuredEntityMappings {
    [placeholder: string]: EntityMapping;
}
```

**EntityMapping Interface**:
- `name`: The entity name (e.g., "<PERSON>", "Magic Stone")
- `type`: The entity type (e.g., "character", "item", "location")
- `description`: Full entity description text

**StructuredEntityMappings Interface**:
- Maps placeholder strings (e.g., "@FoxCharacter") to EntityMapping objects
- Provides type-safe access to entity information
- Enables structured processing of story entities

### Type Safety Improvements

The entity mapping system uses strict TypeScript typing for enhanced type safety:

```typescript
// Enhanced type safety with Record<string, unknown> instead of any
export function parseStructuredEntityMappings(storyData: Record<string, unknown>): {
    characters: StructuredEntityMappings;
    items: StructuredEntityMappings;
    locations: StructuredEntityMappings;
}
```

**Benefits**:
- **Strict Type Checking**: Prevents runtime errors from malformed data
- **Better IDE Support**: Enhanced autocomplete and error detection
- **Code Quality**: Eliminates use of `any` type for better maintainability
- **Runtime Safety**: Ensures data structure integrity during processing

### Database Storage

The structured entity mappings are stored in dedicated JSON columns in the Story interface:

```typescript
interface Story {
  // ... other fields
  
  // Structured entity mappings (new format for better performance and accuracy)
  character_mappings?: string; // JSON string of character placeholder mappings
  item_mappings?: string; // JSON string of item placeholder mappings
  location_mappings?: string; // JSON string of location placeholder mappings
}
```

**Benefits of Structured Storage**:
- **Performance**: Dedicated columns enable faster queries and indexing
- **Type Safety**: Structured interfaces prevent runtime errors
- **Backward Compatibility**: Maintains support for legacy `detailsPrompts` format
- **Scalability**: JSON format allows for future entity property extensions

## Core Functions

### Character Recognition & Transformation

#### `transformActionPromptWithStoryData()`

The primary function for transforming action prompts using actual story data for accurate placeholder conversion.

```typescript
export async function transformActionPromptWithStoryData(
  prompt: string, 
  storyData: any
): Promise<string>
```

**Features**:
- Converts `@CharacterName` placeholders to descriptive phrases based on actual character descriptions
- Handles characters, items, and locations with intelligent entity mapping
- Processes generic terms ("character", "animal", "creature") with context-aware replacements
- Falls back to pattern matching when story data is unavailable

**Example Transformation**:
```typescript
// Input: "@OllieOtter splashes in the pond"
// Output: "the otter splashes in the pond"

// Input: "The character explores the forest"
// Output: "the fox explores the forest" (based on story context)
```

#### `getDescriptivePhrase()`

Determines appropriate descriptive phrases based on entity information and type.

```typescript
function getDescriptivePhrase(
  entityInfo: string, 
  entityType: 'character' | 'item' | 'location'
): string
```

**Character Recognition Logic**:
1. **Name Analysis**: Checks character name for animal keywords
2. **Description Analysis**: Analyzes character description for animal types  
3. **Fallback Handling**: Provides generic descriptors for unrecognized types
4. **Human Character Support**: Handles human characters (girl, boy, child)

### Supported Animal Types

#### Comprehensive Animal Recognition

The system supports **25+ animal types** across multiple categories:

**Woodland Creatures (14 types)**:
- fox, deer, hedgehog, raccoon, otter, beaver, squirrel, chipmunk, porcupine, skunk, mole, weasel, possum, badger

**Farm Animals (5 types)**:
- pig, sheep, cow, goat, horse

**Common Pets (5 types)**:
- cat, dog, hamster, guinea pig, ferret

**Birds & Aquatic (4 types)**:
- bird, owl, duck, fish

**Large Animals (5 types)**:
- bear, lion, tiger, elephant, wolf

**Small Animals (4 types)**:
- mouse, rabbit/bunny, frog, turtle/tortoise

#### `extractAnimalType()`

Dynamically extracts animal types from character names or descriptions.

```typescript
function extractAnimalType(lowerName: string, lowerDesc: string): string | null
```

**Features**:
- Searches for animal keywords in combined name and description text
- Prioritizes specific animals over general terms
- Handles compound animal names (e.g., "guinea pig", "sea turtle")
- Returns null for unrecognized types

### Generic Term Processing

#### `replaceGenericTermsWithSpecificAnimals()`

Handles cases where AI generates action prompts with generic terms instead of specific placeholders.

```typescript
function replaceGenericTermsWithSpecificAnimals(
  prompt: string, 
  characterMappings: Map<string, string>
): string
```

**Supported Generic Terms**:
- "the character" → "the [animal]"
- "character" → "[animal]"
- "the animal" → "the [animal]"
- "animal" → "[animal]"
- "the creature" → "the [animal]"
- "creature" → "[animal]"

**Context-Aware Selection**:
The system uses context clues to select the most appropriate character when multiple characters exist:

- **Teaching Context**: professor, teacher, chalkboard, lesson → matches academic characters
- **Authority Context**: points, instructs, leads → matches leadership characters
- **Wise Context**: wise, elder, experienced → matches wise characters
- **Small/Energetic Context**: small, quick, scurries → matches small, energetic characters
- **Playful Context**: playful, curious, mischief → matches playful characters

### Entity Mapping

#### `extractEntityMappings()`

Extracts entity mappings from prompts text with support for multiple format variations.

```typescript
function extractEntityMappings(promptsText: string): Map<string, string>
```

**Process**:
1. Removes header lines (e.g., "Character Prompts:")
2. Splits text into entity blocks by double newlines
3. Parses each block for name-placeholder pairs using flexible pattern matching
4. Stores name and description for analysis
5. Provides comprehensive debug logging for troubleshooting

**Supported Formats**:
- **Prefixed Format**: "Character: Rusty - @FoxCharacter: A brave little fox..."
- **Prefixed Format**: "Item: Magic Stone - @GlowStone: A radiant pebble..."
- **Prefixed Format**: "Location: Forest - @MagicalWoods: An ancient woodland..."
- **Simple Format**: "Rusty - @FoxCharacter: A brave little fox..." (fallback)
- **Simple Format**: "Magic Stone - @GlowStone: A radiant pebble..." (fallback)
- **Simple Format**: "Forest - @MagicalWoods: An ancient woodland..." (fallback)

**Enhanced Pattern Matching**:
- **Primary Pattern**: Matches `Character:`, `Item:`, or `Location:` prefixed formats
- **Fallback Pattern**: Handles simple "Name - @Placeholder" format for backward compatibility
- **Error Handling**: Logs warnings for unparseable lines to aid debugging
- **Debug Logging**: Comprehensive logging of successful mappings for troubleshooting

### Location & Item Processing

#### `extractLocationType()` & `extractItemType()`

Similar to animal extraction but for locations and items.

**Location Patterns**:
- Specific: classroom, learning school, clearing
- General: beach, bridge, castle, forest, garden, house, lake, meadow, mountain, pond, river, stone, tree

**Item Patterns**:
- Specific: inspection badge, spectacles, podium, acorns
- General: acorn, badge, book, crystal, fence, gem, glasses, key, stone, tumbler, twig

## Text Processing Utilities

### JSON Extraction

#### `extractJsonFromPerplexityResponse()`

Extracts JSON from Perplexity AI responses that may contain reasoning tags and markdown.

```typescript
export function extractJsonFromPerplexityResponse(responseText: string): string
```

**Features**:
- Handles `<think>` reasoning tags
- Removes markdown code block formatting
- Attempts to fix common JSON parsing issues
- Provides fallback extraction methods

#### `extractValidJsonFromText()`

General-purpose JSON extraction from AI model text responses.

```typescript
export function extractValidJsonFromText(text: string): string | null
```

**Process**:
1. Removes markdown code block markers
2. Uses brace counting for balanced JSON extraction
3. Validates extracted JSON before returning
4. Falls back to regex matching if needed

### Audio Processing

#### `getMp3DurationFromDataUri()`

Estimates audio duration from MP3 or WAV data URIs.

```typescript
export function getMp3DurationFromDataUri(dataUri: string): number
```

**Features**:
- Supports both MP3 and WAV formats
- Detects placeholder audio files
- Handles very short audio clips
- Provides reasonable fallback durations

**Detection Logic**:
- MP3: Estimates based on 128 kbps bitrate
- WAV: Uses 48kHz sample rate estimation
- Placeholder detection for empty audio
- Minimum duration enforcement (1 second)

### Script Validation

#### `fixMissingPeriods()`

Automatically fixes missing periods in script chunks.

```typescript
export function fixMissingPeriods(
  chunks: string[], 
  originalScript: string
): string[] | null
```

**Logic**:
- Adds periods to chunks that don't end with punctuation
- Only adds periods when next chunk starts with capital letter
- Validates changes don't break content integrity
- Returns null if fixing fails

#### `validateScriptChunks()`

Validates script chunks against the original script.

```typescript
export function validateScriptChunks(
  originalScript: string, 
  chunks: string[]
): { isValid: boolean, error?: string }
```

**Validation Checks**:
- Word count comparison with 2% tolerance
- Content length similarity (90-110% range)
- Whitespace normalization for accurate comparison
- Detailed error reporting for debugging

## Legacy Support

### `transformActionPromptForVideo()` (Deprecated)

Legacy function for basic pattern-matching transformation.

```typescript
export function transformActionPromptForVideo(prompt: string): string
```

**Note**: This function is deprecated in favor of `transformActionPromptWithStoryData()` which provides more accurate conversion based on actual story data.

## Usage Examples

### Entity Mapping Types Usage

```typescript
import { EntityMapping, StructuredEntityMappings } from '@/actions/utils/storyHelpers';

// Example EntityMapping object
const foxCharacter: EntityMapping = {
    name: "Rusty",
    type: "character", 
    description: "A brave little fox with copper fur and bright amber eyes"
};

// Example StructuredEntityMappings usage
const entityMappings: StructuredEntityMappings = {
    "@FoxCharacter": {
        name: "Rusty",
        type: "character",
        description: "A brave little fox with copper fur and bright amber eyes"
    },
    "@MagicStone": {
        name: "Glowing Crystal",
        type: "item",
        description: "A radiant pebble that shimmers with inner light"
    },
    "@EnchantedForest": {
        name: "Mystical Woods",
        type: "location", 
        description: "An ancient woodland filled with towering trees"
    }
};

// Type-safe access to entity information
const characterInfo = entityMappings["@FoxCharacter"];
if (characterInfo.type === "character") {
    console.log(`Character: ${characterInfo.name} - ${characterInfo.description}`);
}
```

### Basic Character Transformation

```typescript
// Transform prompts with story data
const transformedPrompt = await transformActionPromptWithStoryData(
  "@OllieOtter splashes in the @ClearStream",
  storyData
);
// Result: "the otter splashes in the stream"
```

### Generic Term Processing

```typescript
// Context-aware generic term replacement
const prompt = "The wise character teaches the young animal";
const result = await transformActionPromptWithStoryData(prompt, storyData);
// Result: "The wise owl teaches the young mouse" (based on story context)
```

### JSON Extraction

```typescript
// Extract JSON from AI response
const aiResponse = "```json\n{\"characters\": [\"fox\", \"owl\"]}\n```";
const jsonStr = extractValidJsonFromText(aiResponse);
const data = JSON.parse(jsonStr);
```

### Entity Mapping with Enhanced Format Support

```typescript
// Prefixed format (recommended)
const promptsText = `
Character Prompts:

Character: Rusty - @FoxCharacter
A brave little fox with copper fur and bright amber eyes.

Item: Magic Stone - @GlowStone
A radiant pebble that shimmers with inner light.

Location: Enchanted Forest - @MagicalWoods
A mystical woodland filled with ancient trees.
`;

// Simple format (backward compatibility)
const legacyPromptsText = `
Character Prompts:

Rusty - @FoxCharacter
A brave little fox with copper fur and bright amber eyes.

Magic Stone - @GlowStone
A radiant pebble that shimmers with inner light.
`;

// Both formats work with the same function
const mappings = extractEntityMappings(promptsText);
// Debug logs will show successful mappings and any parsing issues
```

### Audio Duration Estimation

```typescript
// Estimate audio duration
const dataUri = "data:audio/mpeg;base64,UklGRiQAAABXQVZF...";
const duration = getMp3DurationFromDataUri(dataUri);
console.log(`Audio duration: ${duration} seconds`);
```

## Error Handling

All utility functions include comprehensive error handling:

- **Graceful Degradation**: Functions continue working with partial data
- **Fallback Mechanisms**: Multiple approaches for content processing
- **Detailed Logging**: Console warnings and errors for debugging
- **Validation**: Input validation and sanity checks
- **Safe Defaults**: Reasonable default values when processing fails

## Performance Considerations

- **Efficient Pattern Matching**: Optimized regex patterns for character recognition
- **Minimal Processing**: Only processes content when necessary
- **Caching Friendly**: Results can be cached for repeated operations
- **Memory Efficient**: No large data structures or memory leaks

## Integration Points

These utilities are used throughout StoryTailor:

- **Image Generation**: Character placeholder transformation for AI prompts
- **Story Processing**: Content validation and formatting
- **Audio Generation**: Duration estimation and content parsing
- **Database Operations**: JSON parsing and data validation
- **User Interface**: Content display and formatting

## Related Documentation

- **[Character Recognition System](./character-recognition-system.md)** - Detailed character recognition documentation
- **[Blueprint](./blueprint.md)** - Overall application architecture
- **[Image Generation Improvements](./image-generation-improvements.md)** - Image generation integration
- **[Translation System](./translation-system-overview.md)** - Translation workflow integration

---

*The story processing utilities form the backbone of StoryTailor's content transformation pipeline, ensuring that user-generated content is optimally formatted for AI processing while maintaining narrative coherence and character consistency.*