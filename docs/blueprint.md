# **App Name**: StoryTailor

## Core Features:

- AI Script Generation: Generate an animated video script using a prompt. The tool incorporates themes, character descriptions, and story twists based on user inputs, tailored for children and adults, maintaining a specific word count for optimal engagement. Example of prompt from the user: "Create a fun and engaging animated storyline. It features a (brave little fox exploring the forest). Include themes (of environmental awareness and courage). Add a series of whimsical and exciting adventures. Layer depth and subtle twists into the narrative. Captivate both children and adults with the story. Keep the length around 300 words for engagement. Start with something like (“<PERSON>”)"
- Narration Audio Generation: Create narration audio using the generated script. Integrates with ElevenLabs API to produce high-quality MP3 audio for video narration, automatically synced with the script. Multi-language translation (Spanish and Romanian) includes complete workflow integration with automatic Baserow persistence and enhanced error handling.
- Prompt Autogeneration: Create detailed character/item/location prompts based on the main script. Examples of prompts: Characters Rusty a sleek 3yo red fox with bright copper fur and amber eyes, wearing a woven fern satchel. Owl an aged 15yo white and brown owl with mottled plumage and a tattered olive-green cloak. Squirrel a small 2yo grey tree squirrel with short, fluffy fur and a vibrant blue scarf. Locations Forest an expansive woodland of ancient oaks and towering evergreens, dappled with soft sunlight. Stream a bubbling, clear water stream with mossy banks and sunlight glints on its surface. Twisting Cave a narrow, winding cavern with irregular rock formations and glistening, shadowed walls. Items Satchel a compact bag crafted from woven ferns with a natural green texture. Glowstone a tiny radiant pebble that shimmers like a miniature sun with warm, glowing facets.
- AI Image Prompting: Generate a sequence of image prompts tailored to the narration, determining the quantity of images according to the duration of the audio. Creates coherent, scene-specific prompts for the animation with automatic database optimization and **comprehensive character placeholder transformation system**. 

**Enhanced Character Recognition System**:
  - **25+ Animal Types Supported**: Comprehensive coverage across multiple categories
    - **Woodland Creatures**: fox, deer, hedgehog, raccoon, otter, beaver, squirrel, chipmunk, porcupine, skunk, mole, weasel, possum, badger
    - **Farm Animals**: pig, sheep, cow, goat, horse  
    - **Common Pets**: cat, dog, hamster, guinea pig, ferret
    - **Birds & Aquatic**: bird, owl, duck, fish
    - **Large Animals**: bear, lion, tiger, elephant, wolf
    - **Small Animals**: mouse, rabbit/bunny, frog, turtle/tortoise
  - **Smart Placeholder Transformation**: Converts `@CharacterName` references to natural language descriptions (e.g., `@OllieOtter` → `the otter`) for optimal AI understanding
  - **Dual Analysis System**: Analyzes both character names and descriptions to identify animal types accurately
  - **Context-Aware Generic Term Processing**: Intelligently replaces generic terms ("character", "animal", "creature") with specific animal types based on story context and character traits
  - **Enhanced Entity Mapping**: Extracts and maps characters, items, and locations from story data with flexible format support (both prefixed "Character: Name - @Placeholder" and simple "Name - @Placeholder" formats)
  - **Structured Entity Storage**: New JSON-based entity mapping system with dedicated database columns (`character_mappings`, `item_mappings`, `location_mappings`) for improved performance and type safety
  - **Strict Type Safety**: Enhanced TypeScript interfaces with `Record<string, unknown>` instead of `any` for better code quality and runtime safety
  - **Comprehensive Debugging**: Built-in logging for successful mappings and parsing issues to aid troubleshooting
  - **Fallback Handling**: Graceful degradation for unrecognized character types with intelligent defaults

**Advanced Text Processing Utilities**:
  - **JSON Extraction**: Robust parsing of AI responses with support for Perplexity reasoning tags and markdown code blocks
  - **Audio Duration Estimation**: Accurate duration calculation from MP3/WAV data URIs with placeholder detection
  - **Script Validation**: Comprehensive validation of script chunks against original content with similarity thresholds
  - **Content Parsing**: Advanced text processing with error handling and content normalization
  - **Entity Format Flexibility**: Support for multiple entity description formats with automatic fallback parsing
  - **Structured Entity Types**: TypeScript interfaces for type-safe entity mapping operations with EntityMapping and StructuredEntityMappings interfaces
  - **Enhanced Type Safety**: Strict typing with `Record<string, unknown>` instead of `any` for improved code quality and runtime error prevention

Generated prompts are immediately saved to dedicated database columns for optimal performance. Example of prompts for text to image model: Wide front shot at eye-level of the otter standing proudly in a sunlit clearing of the forest, his sleek coat gleaming in the light. He is standing and looking around. It is a sunny morning. Medium side shot at eye-level of the otter trotting through the forest, satchel in tow, as he follows a shimmering path. The otter is walking, following the path. The otter is standing in a sunlit clearing of the forest. It is a sunny morning. (Character placeholders are automatically transformed to natural language for better AI comprehension)
- Video Assembly & Export: Arrange video images into a single animation and download the resulting video in MP4 format with advanced story processing integration. The system uses Remotion for video composition with intelligent character consistency, accurate audio synchronization, and comprehensive content validation. Features include batch processing for large stories, caching for performance optimization, and graceful error handling with multiple recovery mechanisms.
- User Authentication: Firebase Auth integration for secure user accounts and session management
- Database Structure: Migrated from Firestore to Baserow (PostgreSQL) with optimized table structure:
  - **Stories Table (ID: 696)**: Complete story data with JSON fields for complex nested data and intelligent fallback handling
  - **User API Keys Table (ID: 697)**: Encrypted storage of user-provided API keys for AI services
  - **Data Integrity**: Enhanced smart fallback system with improved validation prioritizes dedicated columns over settings data for image and action prompts
  - **Automatic Optimization**: New image prompts are automatically saved to dedicated columns during generation for optimal performance
  - **Security & Authorization**: Comprehensive user authorization checks and story validation prevent unauthorized access to user data
  - **Code Quality**: Clean, maintainable data parsing logic with comprehensive error handling and security logging
- Storage System: MinIO S3-compatible storage replacing Firebase Storage for cost-effective file management
- Model Context Protocol (MCP): Enhanced AI capabilities with external tools and data sources, integrated with Kiro agent for seamless development experience
- Development Environment: VS Code integration with AI-powered inline completion and codebase indexing

## Style Guidelines:

- Primary color: Soft teal (#A0E7E5) for a calming, story-like atmosphere.
- Secondary color: Light beige (#E6D8B9) for backgrounds, to ensure comfortable readability.
- Accent: Coral (#FF6F61) for interactive elements and call-to-action buttons, ensuring an approachable interface.
- Clean and readable fonts for easy story consumption.
- Friendly and whimsical icons to guide users.
- Clean and spacious layout with clear sections for prompt input, script preview, and video display.
- Gentle transitions and loading animations for a seamless user experience.