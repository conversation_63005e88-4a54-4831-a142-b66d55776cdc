# Qwen Code Context for StoryTailor

This document provides essential context for Qwen Code to understand and assist with the StoryTailor project.

## Project Overview

StoryTailor is a Next.js (v15) application that enables users to generate animated video scripts, narration audio, image prompts, and complete videos using AI. It features a hybrid architecture for cost-effective backend services:

- **Frontend**: Next.js 15 with App Router, TypeScript, Tailwind CSS, and shadcn/ui.
- **Authentication**: Firebase Auth (preserved from migration).
- **Database**: Baserow (PostgreSQL-based, migrated from Firestore).
- **Storage**: MinIO S3-compatible storage (migrated from Firebase Storage).
- **AI Integration**: Genkit framework for managing AI flows.
- **Video Processing**: Remotion for video rendering and playback.

The application has successfully migrated from a full Firebase stack to this hybrid model, achieving 30-55% cost reduction while maintaining data sovereignty.

## Key Technologies

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript (strict mode)
- **Styling**: Tailwind CSS, shadcn/ui
- **State Management**: React Query (@tanstack/react-query)
- **Authentication**: Firebase Auth
- **Database**: Baserow (REST API over PostgreSQL)
- **Storage**: MinIO (S3-compatible API)
- **AI**: Genkit, Google AI
- **Video**: Remotion
- **Model Context Protocol (MCP)**: For enhanced AI capabilities with external tools

## Building and Running

### Prerequisites

- Node.js (v18 or later)
- npm or yarn
- Firebase project (for authentication)
- Access to Baserow database and MinIO storage (self-hosted or cloud)

### Environment Setup

Create a `.env.local` file in the project root with the necessary environment variables. See the main `README.md` for a template and details.

### Development

```bash
# Install dependencies
npm install

# Run the development server (port 9002)
npm run dev

# Run the Genkit development server (for AI flows)
npm run genkit:watch
```

### Testing Baserow & MinIO Connections

```bash
# Test Baserow connection and structure
npm run test:baserow
npm run check:baserow

# Test Baserow authentication
npm run test:baserow-auth

# Setup Baserow schema (if needed)
npm run setup:baserow
```

### Linting and Type Checking

```bash
# Run ESLint
npm run lint

# Run TypeScript compiler check
npm run typecheck
```

### Building for Production

```bash
# Build the application
npm run build

# Start the production server
npm run start
```

### Video Rendering

```bash
# Render a Remotion video
npm run remotion:render

# Fast render with concurrency
npm run remotion:render:fast

# Benchmark rendering performance
npm run remotion:benchmark
```

## Development Conventions

- **Code Style**: TypeScript with strict mode. ESLint and Prettier are used for code formatting and linting.
- **Component Structure**: Components are located in `src/components/`. UI components often use shadcn/ui primitives.
- **State Management**: React Query is used for server state management. Client state is managed with React hooks (useState, useContext).
- **Authentication**: Firebase Auth is used via a custom `AuthProvider` and `useAuth` hook.
- **Database Interaction**: Baserow is accessed through `src/lib/baserow.ts`. CRUD operations for stories are implemented.
- **Storage Interaction**: MinIO is accessed through `src/lib/minio.ts`. File upload, download, and URL signing are supported.
- **AI Flows**: Genkit flows are defined in `src/ai/`.
- **API Actions**: Business logic for interacting with services is encapsulated in `src/actions/` (e.g., `baserowStoryActions.ts`, `minioStorageActions.ts`).

## Key Files and Directories

- **`README.md`**: Main project documentation.
- **`package.json`**: Project dependencies and scripts.
- **`next.config.ts`**: Next.js configuration.
- **`src/app/`**: Next.js App Router pages and layout.
- **`src/components/`**: React components.
- **`src/hooks/`**: Custom React hooks.
- **`src/lib/`**: Service integrations (firebase, baserow, minio).
- **`src/actions/`**: Business logic functions for interacting with services.
- **`src/ai/`**: Genkit AI flows.
- **`.env.local`**: Local environment variables (not committed).
- **`MIGRATION-COMPLETED.md`**: Details on the migration from Firebase to Baserow/MinIO.

## Deployment

The recommended deployment is a hybrid model:

- **Frontend**: Deploy to Vercel.
- **Backend Services**: Self-hosted Baserow and MinIO, accessible via Cloudflare tunnels.

Environment variables for Vercel should point to the tunnel URLs for Baserow and MinIO, while keeping Firebase Auth credentials for the original Firebase project.

## Qwen Added Memories
- Fixed placeholder replacement issue in StoryTailor where generic terms like "character" were not being correctly replaced with specific animal types. Enhanced the replaceGenericTermsWithSpecificAnimalsStructured function in src/actions/utils/storyHelpers.ts to improve context clue matching and fallback logic for selecting the main character when multiple characters are present.
- Fixed the placeholder replacement issue in StoryTailor by modifying the approach to generate action prompts without placeholders. Instead of generating action prompts with @CharacterName placeholders and then converting them to descriptive terms, the AI now generates action prompts that directly use descriptive character information from the CHARACTER REFERENCE. This eliminates the need for placeholder replacement logic and ensures that action prompts contain accurate descriptive terms from the start.
- Successfully fixed the placeholder replacement issue in StoryTailor by implementing the suggested approach of generating action prompts without placeholders. Updated the image prompt template to instruct the AI to generate action prompts with descriptive character information directly instead of using @CharacterName placeholders. Modified the video generation logic, download story utilities, and UI components to use action prompts directly without placeholder transformation. The build, linting, and type checking all pass successfully.
- Successfully implemented backward compatibility and fixed character type extraction in StoryTailor. Added backward compatibility to handle existing action prompts with placeholders while supporting new action prompts with descriptive terms. Fixed character type extraction to correctly identify animal types from names/descriptions. Added a function to fix existing character mappings in Baserow that have incorrect types. All changes pass build, linting, and type checking.
